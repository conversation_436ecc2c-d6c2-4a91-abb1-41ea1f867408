@extends('layouts.master')

@section('css')
    <!-- Custom CSS for enhanced dashboard -->
    <style>
        .dashboard-stat-card {
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }

        .dashboard-stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            font-size: 3.5rem;
            opacity: 0.9;
            filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
            transition: all 0.3s ease;
        }

        .stat-icon:hover {
            transform: scale(1.1);
            opacity: 1;
        }

        .progress {
            height: 8px;
            margin-top: 10px;
            border-radius: 4px;
        }

        .rental-card {
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .rental-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .chart-container {
            position: relative;
            margin: auto;
            height: 300px;
        }

        .counter-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            margin-bottom: 10px;
            display: block;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .counter-number:hover {
            transform: scale(1.05);
        }

        .badge-pill {
            padding-right: 1em;
            padding-left: 1em;
        }

        .icon-circle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            padding: 15px;
        }

        .bg-light-primary {
            background-color: rgba(78, 94, 255, 0.15);
        }

        .bg-light-success {
            background-color: rgba(10, 187, 135, 0.15);
        }

        .bg-light-danger {
            background-color: rgba(250, 92, 124, 0.15);
        }

        .bg-light-info {
            background-color: rgba(57, 175, 209, 0.15);
        }

        .bg-light-warning {
            background-color: rgba(255, 184, 34, 0.15);
        }

        .badge-soft-warning {
            background-color: rgba(255, 184, 34, 0.18);
            color: #ffb822;
        }

        .text-warning {
            color: #ffb822 !important;
        }

        /* New styles for enhanced dashboard */
        .dashboard-header {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .dashboard-welcome {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }

        .dashboard-date {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .dashboard-section {
            margin-bottom: 2rem;
        }

        .dashboard-section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
    </style>
@endsection

@section('content')
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- Dashboard Header -->
        <div class="row dashboard-header">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="dashboard-welcome">Welcome back, {{ Auth::user()->name }}</h4>
                        <p class="dashboard-date">{{ now()->format('l, F j, Y') }}</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" id="refreshDashboard">
                            <i class="fe-refresh-cw mr-1"></i> Refresh Data
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards Row -->
        <div class="row">
            <div class="col-md-6 col-xl-3">
                <div class="card dashboard-stat-card bg-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="text-muted text-uppercase mb-0">Total Devices</h5>
                            <div class="icon-circle bg-light-primary">
                                <i class="fe-monitor stat-icon text-primary" style="font-size: 3rem;"></i>
                            </div>
                        </div>
                        <h3 class="counter-number text-primary">{{ $totalDevices }}</h3>
                        <div class="d-flex align-items-center mt-3">
                            <span class="badge badge-pill badge-primary"> {{ $onlinePercentage }}% Online </span>
                            <span class="text-muted ml-2">({{ $onlineDevices }} of {{ $totalDevices }})</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: {{ $onlinePercentage }}%">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-xl-3">
                <div class="card dashboard-stat-card bg-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="text-muted text-uppercase mb-0">Online Devices</h5>
                            <div class="icon-circle bg-light-success">
                                <i class="fe-check-circle stat-icon text-success" style="font-size: 3rem;"></i>
                            </div>
                        </div>
                        <h3 class="counter-number text-success">{{ $onlineDevices }}</h3>
                        <div class="d-flex align-items-center mt-3">
                            <span class="badge badge-pill badge-success">Active</span>
                            <span class="text-success ml-2">Connected and operational</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-xl-3">
                <div class="card dashboard-stat-card bg-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="text-muted text-uppercase mb-0">Offline Devices</h5>
                            <div class="icon-circle bg-light-danger">
                                <i class="fe-x-circle stat-icon text-danger" style="font-size: 3rem;"></i>
                            </div>
                        </div>
                        <h3 class="counter-number text-danger">{{ $offlineDevices }}</h3>
                        <div class="d-flex align-items-center mt-3">
                            <span class="badge badge-pill badge-danger">Offline</span>
                            <span class="text-danger ml-2">Not responding</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-danger" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-xl-3">
                <div class="card dashboard-stat-card bg-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="text-muted text-uppercase mb-0">Total Customers</h5>
                            <div class="icon-circle bg-light-warning">
                                <i class="fe-users stat-icon text-warning" style="font-size: 3rem;"></i>
                            </div>
                        </div>
                        <h3 class="counter-number text-warning">{{ $totalCustomers ?? 0 }}</h3>
                        <div class="d-flex align-items-center mt-3">
                            <span class="badge badge-pill badge-warning">{{ $verifiedCustomers ?? 0 }} Verified</span>
                            <span class="text-warning ml-2">Active users</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-warning" role="progressbar"
                                style="width: {{ $totalCustomers > 0 ? ($verifiedCustomers / $totalCustomers) * 100 : 0 }}%">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Second Stats Row -->
        {{-- <div class="row">
            <div class="col-md-6 col-xl-3">
                <div class="card dashboard-stat-card bg-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="text-muted text-uppercase mb-0">Active Ad Plans</h5>
                            <div class="icon-circle bg-light-info">
                                <i class="fe-play-circle stat-icon text-info" style="font-size: 3rem;"></i>
                            </div>
                        </div>
                        <h3 class="counter-number text-info">{{ $activeAdPlans }}</h3>
                        <div class="d-flex align-items-center mt-3">
                            <span class="badge badge-pill badge-soft-info">{{ $totalAdPlans }} Total</span>
                            <span class="text-info ml-2">Running campaigns</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" role="progressbar"
                                style="width: {{ $totalAdPlans > 0 ? ($activeAdPlans / $totalAdPlans) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}

        <!-- Charts Row -->
        <div class="row">
            <div class="col-xl-8">
                <div class="card dashboard-stat-card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">Device Status History</h4>
                        <div class="chart-container">
                            <canvas id="deviceStatusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4">
                <div class="card dashboard-stat-card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">Device Status</h4>
                        <div class="chart-container">
                            <canvas id="deviceStatusPie"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Powerbank Rentals Table -->
        <div class="row">
            <div class="col-12">
                <div class="card dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4 class="header-title mb-0">Recent Powerbank Rentals</h4>
                            <a href="{{ route('rentals.index') }}" class="btn btn-sm btn-primary">View All</a>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover table-centered mb-0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Powerbank</th>
                                        <th>Customer</th>
                                        <th>Device</th>
                                        <th>Location</th>
                                        <th>Rented At</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($recentRentals ?? [] as $rental)
                                        <tr>
                                            <td>{{ $rental->id }}</td>
                                            <td>{{ $rental->powerbank->serial_number ?? 'Unknown' }}</td>
                                            <td>{{ $rental->customer_id ?? 'N/A' }}</td>
                                            <td>{{ $rental->device->sn ?? 'N/A' }}</td>
                                            <td>{{ $rental->device->location ?? 'Unknown' }}</td>
                                            <td>{{ isset($rental->rented_at) ? $rental->rented_at->format('M d, H:i') : 'N/A' }}
                                            </td>
                                            <td>
                                                <span
                                                    class="badge badge-{{ $rental->returned_at ? 'success' : 'warning' }}">
                                                    {{ $rental->returned_at ? 'Returned' : 'Active' }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ route('rentals.show', $rental->id) }}"
                                                    class="btn btn-sm btn-outline-primary">
                                                    <i class="fe-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center">
                                                <div class="alert alert-info mb-0">
                                                    <i class="fe-info mr-1"></i> No recent powerbank rentals found.
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Devices and Heartbeats Row -->
        <div class="row">
            <div class="col-xl-6">
                <div class="card dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="header-title mb-0">Recent Devices</h4>
                            <a href="{{ route('device.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Serial Number</th>
                                        <th>Model</th>
                                        <th>Status</th>
                                        <th>Added</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($recentDevices as $device)
                                        <tr>
                                            <td>{{ $device->sn }}</td>
                                            <td>{{ $device->model }}</td>
                                            <td>
                                                @if ($device->status == 1)
                                                    <span class="badge badge-success">Online</span>
                                                @else
                                                    <span class="badge badge-danger">Offline</span>
                                                @endif
                                            </td>
                                            <td>{{ $device->created_at->diffForHumans() }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Customers Row -->
            {{-- <div class="row"> --}}
            <div class="col-xl-6">
                <div class="card dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="header-title mb-0">Recent Customers</h4>
                            <a href="{{ route('customer.index') }}" class="btn btn-sm btn-outline-primary">View
                                All</a>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Status</th>
                                        <th>Joined</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($recentCustomers ?? [] as $customer)
                                        <tr>
                                            <td>{{ $customer->name }}</td>
                                            <td>{{ $customer->email }}</td>
                                            <td>
                                                @if ($customer->card_verified)
                                                    <span class="badge badge-success">Verified</span>
                                                @else
                                                    <span class="badge badge-warning">Pending</span>
                                                @endif
                                            </td>
                                            <td>{{ $customer->created_at->diffForHumans() }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            {{-- </div> --}}

            <div class="col-xl-6">
                <div class="card dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="header-title mb-0">Recent Heartbeats</h4>
                            <a href="{{ route('device.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Serial Number</th>
                                        <th>Last Heartbeat</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($recentHeartbeats as $device)
                                        <tr>
                                            <td>{{ $device->sn }}</td>
                                            <td>{{ $device->last_heartbeat_at ? \Carbon\Carbon::parse($device->last_heartbeat_at)->diffForHumans() : 'Never' }}
                                            </td>
                                            <td>
                                                @if ($device->isOnline())
                                                    <span class="badge badge-success">Online</span>
                                                @else
                                                    <span class="badge badge-danger">Offline</span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ route('device.show', $device->id) }}"
                                                    class="btn btn-sm btn-info">
                                                    <i class="fe-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div> <!-- end container-fluid -->
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Refresh dashboard button
            $('#refreshDashboard').on('click', function() {
                location.reload();
            });

            // Initialize counterup with proper configuration
            $('.counter-number').each(function() {
                $(this).prop('Counter', 0).animate({
                    Counter: $(this).text()
                }, {
                    duration: 1000,
                    easing: 'swing',
                    step: function(now) {
                        $(this).text(Math.ceil(now));
                    }
                });
            });

            // Device Status Pie Chart
            var pieCtx = document.getElementById('deviceStatusPie').getContext('2d');
            var pieChart = new Chart(pieCtx, {
                type: 'pie',
                data: {
                    labels: ['Online', 'Offline'],
                    datasets: [{
                        data: [{{ $onlineDevices }}, {{ $offlineDevices }}],
                        backgroundColor: ['#28a745', '#dc3545'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        position: 'bottom'
                    }
                }
            });

            // Device Status History Chart
            var historyData = {!! json_encode($deviceStatusHistory) !!};
            var labels = historyData.map(item => item.date);
            var onlineData = historyData.map(item => parseInt(item.online) || 0);
            var offlineData = historyData.map(item => parseInt(item.offline) || 0);

            var historyCtx = document.getElementById('deviceStatusChart').getContext('2d');
            var historyChart = new Chart(historyCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                            label: 'Online Devices',
                            data: onlineData,
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            borderWidth: 2,
                            fill: true
                        },
                        {
                            label: 'Offline Devices',
                            data: offlineData,
                            borderColor: '#dc3545',
                            backgroundColor: 'rgba(220, 53, 69, 0.1)',
                            borderWidth: 2,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        xAxes: [{
                            reverse: true,
                            gridLines: {
                                color: "rgba(0,0,0,0.05)"
                            }
                        }],
                        yAxes: [{
                            ticks: {
                                beginAtZero: true
                            },
                            gridLines: {
                                color: "rgba(0,0,0,0.05)"
                            }
                        }]
                    }
                }
            });
        });
    </script>
@endsection
