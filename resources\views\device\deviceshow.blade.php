@extends('layouts.master')

@section('content')
    <!-- Row -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card-box">
                <h4 class="header-title"><b>Device Details</b></h4>
                <p class="sub-header">
                    Detailed information about the device.
                </p>

                <div class="table-responsive">
                    <table class="table table-bordered mb-0">
                        <tbody>
                            <tr>
                                <th width="30%">ID</th>
                                <td>{{ $device->id }}</td>
                            </tr>
                            <tr>
                                <th>Serial Number</th>
                                <td>{{ $device->sn }}</td>
                            </tr>
                            <tr>
                                <th>Manufacturer</th>
                                <td>{{ $device->manufacturer }}</td>
                            </tr>
                            <tr>
                                <th>Model</th>
                                <td>{{ $device->model }}</td>
                            </tr>
                            <tr>
                                <th>Type</th>
                                <td>{{ $device->device_type }}</td>
                            </tr>
                            <tr>
                                <th>Hardware Version</th>
                                <td>{{ $device->hardware_version }}</td>
                            </tr>
                            <tr>
                                <th>Firmware Version</th>
                                <td>{{ $device->firmware_version }}</td>
                            </tr>
                            <tr>
                                <th>Location</th>
                                <td>
                                    @if ($device->latitude && $device->longitude)
                                        Latitude: {{ $device->latitude }}<br>
                                        Longitude: {{ $device->longitude }}
                                    @else
                                        Not specified
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td>
                                    <span class="badge badge-{{ $device->status == 1 ? 'success' : 'danger' }}">
                                        {{ $device->status == 1 ? 'Online' : 'Offline' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>Active</th>
                                <td>
                                    <span class="badge badge-{{ $device->isactive == 1 ? 'primary' : 'danger' }}">
                                        {{ $device->isactive == 1 ? 'Active' : 'Disabled' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>Created At</th>
                                <td>{{ $device->created_at }}</td>
                            </tr>
                            <tr>
                                <th>Last Updated</th>
                                <td>{{ $device->updated_at }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="mt-4 text-right">

                    <a href="{{ route('device.edit', $device->id) }}" class="btn btn-primary waves-effect waves-light">
                        <i class="far fa-edit mr-1"></i> Edit Device
                    </a>
                    <a href="{{ route('device.index') }}" class="btn btn-light waves-effect ml-1">
                        <i class="fas fa-arrow-left mr-1"></i> Back to List
                    </a>
                    <a href="{{ route('powerbanks.by-device', $device->id) }}" class="btn btn-info">
                        <i class="fas fa-battery-full mr-1"></i> View Powerbanks ({{ $device->powerbanks->count() }})
                    </a>

                    <!-- Device action buttons -->
                    <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#ejectAllDeviceModal">
                        <i class="fas fa-eject mr-1"></i> Force Eject All
                    </button>
                    <button type="button" class="btn btn-info" data-toggle="modal" data-target="#rebootDeviceModal">
                        <i class="fas fa-sync-alt mr-1"></i> Reboot Device
                    </button>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card-box">
                <h4 class="header-title"><b>Device QR Code</b></h4>
                <p class="sub-header">
                    Scan this QR code to access the device.
                </p>

                <div class="text-center">
                    <div class="qr-container mb-3">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://pixelflow.com.pk/Lease?o=ng==&&t={{ $device->sn }}"
                            alt="Device QR Code" class="img-fluid border p-2">
                    </div>
                    <p class="mb-1"><strong>Device URL:</strong></p>
                    <div class="input-group">
                        <input type="text" class="form-control"
                            value="https://pixelflow.com.pk/Lease?o=ng==&&t={{ $device->sn }}" readonly>
                        <div class="input-group-append">
                            <button class="btn btn-primary copy-btn" type="button">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <small class="text-muted mt-2 d-block">Click the button to copy the URL</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Eject All Powerbanks from Device Modal -->
    <div class="modal fade" id="ejectAllDeviceModal" tabindex="-1" role="dialog"
        aria-labelledby="ejectAllDeviceModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ejectAllDeviceModalLabel">Eject All Powerbanks</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to eject all powerbanks from device <strong>{{ $device->sn }}</strong>?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <form action="{{ route('devices.eject-all') }}" method="POST">
                        @csrf
                        <input type="hidden" name="device_id" value="{{ $device->id }}">
                        <button type="submit" class="btn btn-warning">Eject All</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Reboot Device Modal -->
    <div class="modal fade" id="rebootDeviceModal" tabindex="-1" role="dialog" aria-labelledby="rebootDeviceModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rebootDeviceModalLabel">Reboot Device</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to reboot device <strong>{{ $device->sn }}</strong>?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <form action="{{ route('devices.reboot') }}" method="POST">
                        @csrf
                        <input type="hidden" name="device_id" value="{{ $device->id }}">
                        <button type="submit" class="btn btn-info">Reboot Device</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Eject All Powerbanks Modal (Force Command) -->
    <div class="modal fade" id="ejectAllDeviceModal" tabindex="-1" role="dialog"
        aria-labelledby="ejectAllDeviceModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ejectAllDeviceModalLabel">Force Eject All Powerbanks</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to force eject all powerbanks from device
                        <strong>{{ $device->sn }}</strong>?
                    </p>

                    <div class="form-group">
                        <label for="forceAims">Select AIMS Group</label>
                        <select class="form-control" id="forceAims">
                            @if($aimsGroups->isEmpty())
                                <option value="0">AIMS 0 (0 powerbanks)</option>
                            @else
                                @foreach ($aimsGroups as $aims => $powerbanks)
                                    <option value="{{ $aims }}">AIMS {{ $aims }} ({{ count($powerbanks) }} powerbanks)</option>
                                @endforeach
                            @endif
                        </select>
                        <small class="form-text text-muted">Select which AIMS group to eject powerbanks from</small>
                    </div>

                    <div id="ejectAllStatus" class="alert alert-info d-none">
                        <span id="ejectAllMessage">Sending force command to device...</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" id="confirmEjectAllBtn" class="btn btn-warning">Force Eject All</button>
                    <form id="ejectAllForm" action="{{ route('devices.eject-all') }}" method="POST" style="display:none;">
                        @csrf
                        <input type="hidden" name="device_id" value="{{ $device->id }}">
                        <input type="hidden" name="aims" id="ejectAllAims" value="0">
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Reboot Device Modal -->
    <div class="modal fade" id="rebootDeviceModal" tabindex="-1" role="dialog"
        aria-labelledby="rebootDeviceModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rebootDeviceModalLabel">Reboot Device</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to reboot device <strong>{{ $device->sn }}</strong>?</p>
                    <div id="rebootCommandStatus" class="alert alert-info d-none">
                        <span id="rebootCommandMessage">Sending reboot command to device...</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" id="confirmRebootBtn" class="btn btn-info">Reboot Device</button>
                    <form id="rebootDeviceForm" action="{{ route('devices.reboot') }}" method="POST"
                        style="display:none;">
                        @csrf
                        <input type="hidden" name="device_id" value="{{ $device->id }}">
                        <input type="hidden" name="device_sn" value="{{ $device->sn }}">
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Eject Powerbank Modal -->
    <div class="modal fade" id="ejectPowerbankModal" tabindex="-1" role="dialog"
        aria-labelledby="ejectPowerbankModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ejectPowerbankModalLabel">Eject Powerbank</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to eject powerbank <strong id="powerbankSN"></strong>?</p>

                    <form id="ejectPowerbankForm" action="{{ route('powerbanks.eject') }}" method="POST">
                        @csrf
                        <input type="hidden" id="powerbankId" name="powerbank_id">
                        <input type="hidden" id="ejectDeviceSn" name="device_sn">
                        <input type="hidden" id="ejectSlotNumber" name="slot_number">

                        <div class="form-group">
                            <label for="ejectAims">AIMS Value</label>
                            <input type="number" class="form-control" id="ejectAims" name="aims" min="0"
                                max="4" value="0" required>
                            <small class="form-text text-muted">AIMS must be between 0 and 4</small>
                        </div>

                        <div id="ejectCommandStatus" class="alert alert-info mt-3 d-none">
                            <p id="ejectCommandMessage">Sending commands to device...</p>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmEjectBtn">Eject</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Group powerbanks by AIMS -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">Powerbanks by AIMS</h4>
            <div class="btn-group">
                <button id="refreshPowerbanksBtn" class="btn btn-sm btn-info">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <button id="toggleAutoRefreshBtn" class="btn btn-sm btn-danger">
                    <i class="fas fa-stop"></i> Stop Auto-Refresh
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- Loading indicator -->
            <div id="refreshIndicator" class="text-center mb-3" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <span class="ml-2">Refreshing powerbanks status...</span>
            </div>

            <!-- Tab navigation - only defined once here -->
            <ul class="nav nav-tabs" id="aimsTab" role="tablist">
                @php
                    $firstAims = true;
                @endphp

                @if($aimsGroups->isEmpty())
                    <li class="nav-item">
                        <a class="nav-link active" id="aims-0-tab" data-toggle="tab" href="#aims-0" role="tab"
                            aria-controls="aims-0" aria-selected="true">
                            AIMS 0 (0)
                        </a>
                    </li>
                @else
                    @foreach ($aimsGroups as $aims => $powerbanks)
                        <li class="nav-item">
                            <a class="nav-link {{ $firstAims ? 'active' : '' }}" id="aims-{{ $aims }}-tab"
                                data-toggle="tab" href="#aims-{{ $aims }}" role="tab"
                                aria-controls="aims-{{ $aims }}"
                                aria-selected="{{ $firstAims ? 'true' : 'false' }}">
                                AIMS {{ $aims }} ({{ count($powerbanks) }})
                            </a>
                        </li>
                        @php $firstAims = false; @endphp
                    @endforeach
                @endif
            </ul>

            <!-- Tab content container that will be refreshed via AJAX -->
            <div class="tab-content mt-3" id="aimsTabContent">
                <div id="powerbanks-container">
                    @include('device.partials.powerbanks-table', [
                        'aimsGroups' => $aimsGroups,
                        'firstAims' => true,
                        'device' => $device,
                    ])
                </div>
            </div>
        </div>
    </div>

    <!-- End Row -->
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Auto-refresh powerbanks status
            let refreshInterval;
            let isRefreshing = false;

            // Function to refresh powerbanks status via AJAX
            const refreshPowerbanks = function() {
                // Skip if already refreshing
                if (isRefreshing) return;

                isRefreshing = true;

                // Store the currently active tab
                const activeTabId = $('#aimsTab .nav-link.active').attr('id');

                // Show loading indicator
                $('#refreshIndicator').show();

                $.ajax({
                    url: "{{ route('devices.powerbanks-status', $device->id) }}",
                    type: "GET",
                    dataType: "json",
                    success: function(response) {
                        if (response.success) {
                            // Replace the powerbanks container with the updated one
                            $('#powerbanks-container').html(response.html);

                            // Update the tab counts if aimsGroups data is provided
                            if (response.aimsGroups) {
                                // First, ensure all tabs exist
                                response.aimsGroups.forEach(function(group) {
                                    const tabId = `aims-${group.aims}-tab`;
                                    if ($(`#${tabId}`).length === 0) {
                                        // Create new tab if it doesn't exist
                                        $('#aimsTab').append(
                                            `<li class="nav-item">
                                                <a class="nav-link" id="${tabId}" data-toggle="tab"
                                                   href="#aims-${group.aims}" role="tab"
                                                   aria-controls="aims-${group.aims}" aria-selected="false">
                                                    AIMS ${group.aims} (${group.count})
                                                </a>
                                            </li>`
                                        );
                                    } else {
                                        // Update existing tab
                                        $(`#${tabId}`).text(
                                            `AIMS ${group.aims} (${group.count})`);
                                    }
                                });
                            }

                            // Restore the active tab if it exists
                            if (activeTabId && $(`#${activeTabId}`).length) {
                                $(`#${activeTabId}`).tab('show');
                            } else if ($('#aimsTab .nav-link').length) {
                                // Otherwise select the first tab
                                $('#aimsTab .nav-link:first').tab('show');
                            }

                            console.log("Powerbanks status refreshed successfully at " + new Date()
                                .toLocaleTimeString());

                            // Re-attach event handlers to dynamically loaded elements
                            attachEjectButtonHandlers();
                        } else {
                            console.error("Error refreshing powerbanks status:", response.message);
                        }

                        // Hide loading indicator
                        $('#refreshIndicator').hide();
                        isRefreshing = false;
                    },
                    error: function(xhr) {
                        console.error("AJAX error:", xhr.responseText);
                        // Hide loading indicator
                        $('#refreshIndicator').hide();
                        isRefreshing = false;
                    }
                });
            };

            // Function to attach event handlers to dynamically loaded eject buttons
            function attachEjectButtonHandlers() {
                $(document).off('click', '.eject-btn').on('click', '.eject-btn', function() {
                    var id = $(this).data('id');
                    var sn = $(this).data('sn');
                    var deviceSn = '{{ $device->sn }}';
                    var slotNumber = $(this).data('slot');
                    var aims = $(this).data('aims') || 1;

                    console.log("Eject button clicked:", id, sn, deviceSn, slotNumber, aims);

                    // Set values for the modal form
                    $('#powerbankId').val(id);
                    $('#powerbankSN').text(sn);
                    $('#ejectDeviceSn').val(deviceSn);
                    $('#ejectSlotNumber').val(slotNumber || 1);
                    $('#ejectAims').val(aims);

                    // Reset status message
                    $('#ejectCommandStatus').addClass('d-none');
                    $('#ejectCommandMessage').text('Sending commands to device...');
                });
            }

            // Start auto-refresh with 30-second interval
            refreshInterval = setInterval(refreshPowerbanks, 30000);

            // Manual refresh button
            $('#refreshPowerbanksBtn').on('click', function() {
                $(this).find('i').addClass('fa-spin');
                refreshPowerbanks();
                setTimeout(() => {
                    $(this).find('i').removeClass('fa-spin');
                }, 1000);
            });

            // Toggle auto-refresh
            $('#toggleAutoRefreshBtn').on('click', function() {
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                    refreshInterval = null;
                    $(this).html('<i class="fas fa-play"></i> Start Auto-Refresh');
                    $(this).removeClass('btn-danger').addClass('btn-success');
                } else {
                    refreshInterval = setInterval(refreshPowerbanks, 30000);
                    $(this).html('<i class="fas fa-stop"></i> Stop Auto-Refresh');
                    $(this).removeClass('btn-success').addClass('btn-danger');
                }
            });

            // Initial attachment of event handlers
            attachEjectButtonHandlers();

            // Function to generate a random message ID
            function generateMessageId() {
                return Math.floor(Math.random() * 90000) + 10000;
            }

            // Function to save device command via AJAX
            function saveDeviceCommand(deviceSn, command, messageId, data, rawCommand, aims) {
                return $.ajax({
                    url: "{{ route('device-commands.store') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        device_sn: deviceSn,
                        command: command,
                        message_id: messageId,
                        data: JSON.stringify(data),
                        raw_command: rawCommand,
                        aims: aims || 0 // Default to 0 if not provided
                    }
                });
            }

            // Handle confirm eject all button click (Force command)
            $('#confirmEjectAllBtn').on('click', function() {
                var deviceSn = '{{ $device->sn }}';
                var aims = $('#forceAims').val();

                // Set the AIMS value in the form
                $('#ejectAllAims').val(aims);

                // Show status message
                $('#ejectAllStatus').removeClass('d-none');
                $('#ejectAllMessage').text('Sending force command to device...');

                // Generate message ID and create force command
                var messageId = generateMessageId();
                var commandData = {
                    n: 0
                };
                // Include the aims parameter in the main JSON object, not in the data object
                var forceCommand = "#*{\"cmd\":\"force\",\"sn\":\"" + deviceSn +
                    "\",\"aims\":" + aims +
                    ",\"data\":{\"msg\":" + messageId + ",\"n\":0}}*#";

                console.log("Sending force command:", forceCommand);

                // Save command to device_commands table
                saveDeviceCommand(deviceSn, "force", messageId, commandData, forceCommand, aims)
                    .done(function(response) {
                        console.log("Force command saved successfully:", response);
                        $('#ejectAllMessage').text(
                            'Force command sent successfully. Updating database...');

                        // Add a flag to indicate the command was already sent via AJAX
                        $('#ejectAllForm').append(
                            '<input type="hidden" name="command_sent" value="true">');

                        // Submit the form to update the database
                        $('#ejectAllForm').submit();
                    })
                    .fail(function(xhr) {
                        console.error("Error saving force command:", xhr.responseText);
                        $('#ejectAllMessage').text(
                            'Error sending force command. Updating database anyway...');

                        // Submit the form anyway
                        $('#ejectAllForm').submit();
                    });
            });

            // Handle confirm reboot button click
            $('#confirmRebootBtn').on('click', function() {
                var deviceSn = '{{ $device->sn }}';

                // Show status message
                $('#rebootCommandStatus').removeClass('d-none');
                $('#rebootCommandMessage').text('Sending reboot command to device...');

                // Generate message ID and create reboot command
                var messageId = generateMessageId();
                var commandData = {
                    dev: 1
                };
                var rebootCommand = "#*{\"cmd\":\"reboot\",\"sn\":\"" + deviceSn +
                    "\",\"data\":{\"msg\":" + messageId + ",\"dev\":1}}*#";

                console.log("Sending reboot command:", rebootCommand);

                // Save command to device_commands table
                saveDeviceCommand(deviceSn, "reboot", messageId, commandData, rebootCommand)
                    .done(function(response) {
                        console.log("Reboot command saved successfully:", response);
                        $('#rebootCommandMessage').text(
                            'Reboot command sent successfully. Updating database...');

                        // Add a flag to indicate the command was already sent via AJAX
                        $('#rebootDeviceForm').append(
                            '<input type="hidden" name="command_sent" value="true">');

                        // Submit the form to update the database
                        $('#rebootDeviceForm').submit();
                    })
                    .fail(function(xhr) {
                        console.error("Error saving reboot command:", xhr.responseText);
                        $('#rebootCommandMessage').text(
                            'Error sending reboot command. Updating database anyway...');

                        // Submit the form anyway
                        $('#rebootDeviceForm').submit();
                    });
            });

            // Handle eject powerbank button click (setup modal)
            $(document).on('click', '.eject-btn', function() {
                var id = $(this).data('id');
                var sn = $(this).data('sn');
                var deviceSn = '{{ $device->sn }}';
                var slotNumber = $(this).data('slot');
                var aims = $(this).data('aims') || 0;

                // Ensure aims is within valid range
                aims = Math.min(Math.max(0, aims), 4);

                console.log("Eject button clicked:", id, sn, deviceSn, slotNumber, aims);

                // Set values for the modal form
                $('#powerbankId').val(id);
                $('#powerbankSN').text(sn);
                $('#ejectDeviceSn').val(deviceSn);
                $('#ejectSlotNumber').val(slotNumber || 1);
                $('#ejectAims').val(aims);

                // Reset status message
                $('#ejectCommandStatus').addClass('d-none');
                $('#ejectCommandMessage').text('Sending commands to device...');
            });

            // Handle confirm eject button click inside the modal (for single powerbank)
            $('#confirmEjectBtn').on('click', function() {
                var deviceSn = $('#ejectDeviceSn').val();
                var slotNumber = $('#ejectSlotNumber').val();
                var aims = $('#ejectAims').val() || 0;

                // Validate aims value
                if (aims < 0 || aims > 4) {
                    alert('AIMS value must be between 0 and 4');
                    return false;
                }

                if (deviceSn) {
                    // Show status message
                    $('#ejectCommandStatus').removeClass('d-none');

                    // Generate message ID and create rent command for a specific slot
                    var messageId = generateMessageId();
                    var slotNum = slotNumber || 1;
                    var commandData = {
                        n: parseInt(slotNum)
                    };
                    var rentCommand = "#*{\"cmd\":\"rent\",\"sn\":\"" + deviceSn +
                        "\",\"aims\":" + aims +
                        ",\"data\":{\"msg\":" + messageId + ",\"n\":" + slotNum + "}}*#";

                    console.log("Sending rent command for slot " + slotNum + " with aims " + aims + ":",
                        rentCommand);
                    $('#ejectCommandMessage').text('Sending rent command to device...');

                    // Save command to device_commands table
                    saveDeviceCommand(deviceSn, "rent", messageId, commandData, rentCommand, aims)
                        .done(function(response) {
                            console.log("Rent command saved successfully:", response);
                            $('#ejectCommandMessage').text(
                                'Rent command sent successfully. Updating database...');

                            // Add a flag to indicate the command was already sent via AJAX
                            $('#ejectPowerbankForm').append(
                                '<input type="hidden" name="command_sent" value="true">');

                            // Add the aims value to the form
                            $('#ejectPowerbankForm').append(
                                '<input type="hidden" name="aims" value="' + aims + '">');

                            // Submit the form to update the database
                            $('#ejectPowerbankForm').submit();
                        })
                        .fail(function(xhr) {
                            console.error("Error saving rent command:", xhr.responseText);
                            $('#ejectCommandMessage').text(
                                'Error sending rent command. Updating database anyway...');

                            // Add the aims value to the form
                            $('#ejectPowerbankForm').append(
                                '<input type="hidden" name="aims" value="' + aims + '">');

                            // Submit the form without the flag, so the controller will create the command
                            $('#ejectPowerbankForm').submit();
                        });
                } else {
                    console.warn("No device serial number provided for powerbank");
                    $('#ejectCommandMessage').text(
                        'No device serial number provided. Updating database only...');

                    // Add the aims value to the form
                    $('#ejectPowerbankForm').append(
                        '<input type="hidden" name="aims" value="' + aims + '">');

                    // Submit the form without sending commands
                    $('#ejectPowerbankForm').submit();
                }
            });

            // Function to copy text to clipboard
            function copyToClipboard(button) {
                const input = button.closest('.input-group').querySelector('input');
                input.select();
                document.execCommand('copy');

                // Show feedback
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.remove('btn-primary');
                button.classList.add('btn-success');

                setTimeout(function() {
                    button.innerHTML = originalHTML;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-primary');
                }, 2000);
            }

            // Attach copy to clipboard functionality
            $('.copy-btn').on('click', function() {
                copyToClipboard(this);
            });
        });
    </script>
@endsection



