@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-6">
            <div class="card-box">
                <h4 class="header-title">Edit Advertisement Material</h4>
                <p class="sub-header">
                    Update the details of your advertisement material.
                </p>

                <form method="POST" class="parsley-examples" action="{{ route('admaterial.update', $admaterial->id) }}"
                    enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <div class="form-group">
                        <label for="name">Material Name<span class="text-danger">*</span></label>
                        <input type="text" name="name" parsley-trigger="change" required
                            placeholder="Enter material name" class="form-control" id="name"
                            value="{{ old('name', $admaterial->name) }}">
                        @error('name')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label>Material Type</label>
                        <input type="text" class="form-control" value="{{ ucfirst($admaterial->type) }}" readonly>
                        <small class="form-text text-muted">Material type cannot be changed after creation</small>
                    </div>

                    <div class="form-group">
                        <label>Current File</label>
                        <div class="border p-3 rounded">
                            @if ($admaterial->type === 'image')
                                <img src="{{ $admaterial->file_url }}" alt="{{ $admaterial->name }}" class="img-fluid mb-2"
                                    style="max-height: 200px;">
                            @else
                                <video controls class="w-100 mb-2" style="max-height: 200px;">
                                    <source src="{{ $admaterial->file_url }}" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            @endif
                            <div>
                                <a href="{{ $admaterial->file_url }}" target="_blank" class="btn btn-sm btn-info">
                                    <i class="fas fa-external-link-alt mr-1"></i> Open in new tab
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="file">Replace File (optional)</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="file" name="file">
                            <label class="custom-file-label" for="file">Choose file</label>
                        </div>
                        <small class="form-text text-muted" id="file-help">
                            @if ($admaterial->type === 'image')
                                Supported formats: PNG. Max size: 20MB.
                            @else
                                Supported formats: MP4. Max size: 20MB.
                            @endif
                        </small>
                        @error('file')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea name="description" class="form-control" id="description" rows="3"
                            placeholder="Enter material description">{{ old('description', $admaterial->description) }}</textarea>
                        @error('description')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group text-right mb-0">
                        <button class="btn btn-primary waves-effect waves-light" type="submit">
                            <i class="fas fa-save mr-1"></i> Update Material
                        </button>
                        <a href="{{ route('admaterial.show', $admaterial->id) }}" class="btn btn-light waves-effect ml-1">
                            <i class="fas fa-arrow-left mr-1"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Show selected filename in custom file input
            $('input[type="file"]').change(function(e) {
                var fileName = e.target.files[0].name;
                $('.custom-file-label').html(fileName);
            });

            // Initialize form validation
            $('.parsley-examples').parsley();
        });
    </script>
@endsection
