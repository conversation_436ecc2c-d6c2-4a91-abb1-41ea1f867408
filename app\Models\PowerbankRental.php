<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PowerbankRental extends Model
{
    use HasFactory;

    protected $fillable = [
        'powerbank_id',
        'device_id',
        'customer_id',
        'rented_at',
        'returned_at',
        'initial_charge',
        'return_charge',
        'rental_fee',
        'aims', // Add aims to fillable
    ];

    protected $casts = [
        'rented_at' => 'datetime',
        'returned_at' => 'datetime',
    ];

    /**
     * Get the powerbank associated with the rental.
     */
    public function powerbank()
    {
        return $this->belongsTo(Powerbank::class);
    }

    /**
     * Get the customer associated with the rental.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the device associated with the rental.
     */
    public function device()
    {
        return $this->belongsTo(Device::class);
    }

    /**
     * Calculate the rental duration in hours.
     */
    public function getDurationAttribute()
    {
        if (!$this->returned_at) {
            return Carbon::parse($this->rented_at)->diffInHours(Carbon::now());
        }

        return Carbon::parse($this->rented_at)->diffInHours($this->returned_at);
    }
}
