<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('devices')) {
            // First, check if both columns exist
            if (Schema::hasColumn('devices', 'type') && Schema::hasColumn('devices', 'device_type')) {
                // Copy data from 'type' to 'device_type' where device_type is null
                DB::statement('UPDATE devices SET device_type = type WHERE device_type IS NULL');

                // Drop the 'type' column
                Schema::table('devices', function (Blueprint $table) {
                    $table->dropColumn('type');
                });
            }

            // Ensure device_type exists
            if (!Schema::hasColumn('devices', 'device_type')) {
                Schema::table('devices', function (Blueprint $table) {
                    $table->string('device_type')->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('devices')) {
            // Add back the 'type' column if it doesn't exist
            if (!Schema::hasColumn('devices', 'type')) {
                Schema::table('devices', function (Blueprint $table) {
                    $table->string('type')->nullable();
                });

                // Copy data back from device_type to type
                if (Schema::hasColumn('devices', 'device_type')) {
                    DB::statement('UPDATE devices SET type = device_type');
                }
            }
        }
    }
};
