<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Customer extends Authenticatable
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'contact_no',
        'age',
        'password',
        'email_verified_at',
        'contact_verified_at',
        'card_verified',
        'status',
        'notes',
        'payment_method',
        'payment_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'contact_verified_at' => 'datetime',
        'card_verified' => 'boolean',
        'status' => 'boolean',
    ];

    /**
     * Get the rentals for the customer.
     */
    public function rentals()
    {
        return $this->hasMany(PowerbankRental::class);
    }

    /**
     * Get the active rentals for the customer.
     */
    public function activeRentals()
    {
        return $this->rentals()->whereNull('returned_at');
    }
}
