<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Customer extends Authenticatable
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'contact_no',
        'dob',
        'password',
        'email_verified_at',
        'contact_verified_at',
        'card_verified',
        'payment_method',
        'payment_id',
        'card_holder_name',
        'card_number',
        'card_expiry',
        'card_brand',
        'social_id',
        'social_type',
        'status',
        'notes'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'contact_verified_at' => 'datetime',
        'card_verified' => 'boolean',
        'status' => 'boolean',
    ];

    /**
     * Get the rentals for the customer.
     */
    public function rentals()
    {
        return $this->hasMany(PowerbankRental::class);
    }

    /**
     * Get the active rentals for the customer.
     */
    public function activeRentals()
    {
        return $this->rentals()->whereNull('returned_at');
    }

    /**
     * Encrypt sensitive card data on set, decrypt on get
     */
    public function setCardNumberAttribute($value)
    {
        $this->attributes['card_number'] = $value ? encrypt($value) : null;
    }
    public function getCardNumberAttribute($value)
    {
        return $value ? decrypt($value) : null;
    }
    public function setCardExpiryAttribute($value)
    {
        $this->attributes['card_expiry'] = $value ? encrypt($value) : null;
    }
    public function getCardExpiryAttribute($value)
    {
        return $value ? decrypt($value) : null;
    }
    public function setCvvAttribute($value)
    {
        $this->attributes['cvv'] = $value ? encrypt($value) : null;
    }
    public function getCvvAttribute($value)
    {
        return $value ? decrypt($value) : null;
    }
}
