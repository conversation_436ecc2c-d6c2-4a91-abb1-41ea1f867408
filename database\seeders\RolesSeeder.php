<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class RolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create administrator role
        $adminRole = Role::firstOrCreate([
            'name' => 'Administrator',
            'slug' => 'administrator',
        ]);

        // Attach all permissions to administrator role
        $permissions = Permission::all();
        $adminRole->permissions()->sync($permissions->pluck('id')->toArray());

        // Create other roles...
    }
}