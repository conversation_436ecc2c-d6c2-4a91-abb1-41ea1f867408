<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DatabaseBackup;
use App\Services\ActivityLogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;

class DatabaseBackupController extends Controller
{
    public function index()
    {
        $this->authorize('manage-backups');

        $backups = DatabaseBackup::with('creator')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.backups.index', compact('backups'));
    }

    public function create()
    {
        $this->authorize('manage-backups');

        try {
            Artisan::call('db:backup');
            
            ActivityLogService::log(
                'create', 
                'database_backup', 
                'Created a new database backup'
            );

            return redirect()->route('admin.backups.index')
                ->with('success', 'Database backup created successfully.');
        } catch (\Exception $e) {
            return redirect()->route('admin.backups.index')
                ->with('error', 'Failed to create backup: ' . $e->getMessage());
        }
    }

    public function download($id)
    {
        $this->authorize('manage-backups');

        $backup = DatabaseBackup::findOrFail($id);

        if (!file_exists($backup->path)) {
            return redirect()->route('admin.backups.index')
                ->with('error', 'Backup file not found.');
        }

        // Update downloaded_at timestamp
        $backup->update(['downloaded_at' => now()]);

        ActivityLogService::log(
            'download', 
            'database_backup', 
            'Downloaded database backup: ' . $backup->filename
        );

        return Response::download($backup->path, $backup->filename);
    }

    public function destroy($id)
    {
        $this->authorize('manage-backups');

        $backup = DatabaseBackup::findOrFail($id);

        // Delete the file
        if (file_exists($backup->path)) {
            unlink($backup->path);
        }

        // Delete the record
        $backup->delete();

        ActivityLogService::log(
            'delete', 
            'database_backup', 
            'Deleted database backup: ' . $backup->filename
        );

        return redirect()->route('admin.backups.index')
            ->with('success', 'Backup deleted successfully.');
    }
}
