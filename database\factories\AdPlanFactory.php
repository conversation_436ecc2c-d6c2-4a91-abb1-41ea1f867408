<?php

namespace Database\Factories;

use App\Models\AdPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

class AdPlanFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AdPlan::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Generate random start and end times
        $startHour = fake()->numberBetween(0, 22);
        $endHour = fake()->numberBetween($startHour + 1, 23);
        
        return [
            'name' => fake()->words(3, true) . ' Campaign',
            'description' => fake()->paragraph(2),
            'start_time' => sprintf('%02d:00', $startHour),
            'end_time' => sprintf('%02d:59', $endHour),
            'is_active' => fake()->boolean(80), // 80% chance of being active
            'priority' => fake()->numberBetween(1, 10),
            'created_at' => fake()->dateTimeBetween('-3 months', 'now'),
            'updated_at' => fake()->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the ad plan is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the ad plan is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the ad plan has high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => fake()->numberBetween(8, 10),
        ]);
    }
}