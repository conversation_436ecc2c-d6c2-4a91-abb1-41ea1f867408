<?php

namespace Database\Seeders;

use App\Models\Device;
use App\Models\DeviceData;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DeviceDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all devices or create sample data with random SNs if no devices exist
        $devices = Device::all();
        
        if ($devices->count() > 0) {
            // Create data for each device
            foreach ($devices as $device) {
                // Create 10-20 records per device
                $recordCount = rand(10, 20);
                
                // Create regular data entries
                DeviceData::factory()
                    ->count($recordCount - 3) // Reserve 3 for specific types
                    ->create([
                        'sn' => $device->serial_number,
                    ]);
                
                // Create at least one heartbeat
                DeviceData::factory()
                    ->heartbeat()
                    ->create([
                        'sn' => $device->serial_number,
                        'created_at' => now()->subMinutes(rand(1, 60)),
                    ]);
                
                // Create at least one error
                DeviceData::factory()
                    ->error()
                    ->create([
                        'sn' => $device->serial_number,
                        'created_at' => now()->subDays(rand(1, 30)),
                    ]);
                
                // Create a very recent entry
                DeviceData::factory()
                    ->create([
                        'sn' => $device->serial_number,
                        'created_at' => now()->subMinutes(rand(0, 10)),
                    ]);
            }
        } else {
            // No devices in database, create sample data with random SNs
            $serialNumbers = [];
            
            // Generate 5 random serial numbers
            for ($i = 0; $i < 5; $i++) {
                $serialNumbers[] = 'DEV' . fake()->unique()->numerify('######');
            }
            
            // Create data for each serial number
            foreach ($serialNumbers as $sn) {
                // Create 10-20 records per SN
                $recordCount = rand(10, 20);
                
                DeviceData::factory()
                    ->count($recordCount)
                    ->create([
                        'sn' => $sn,
                    ]);
            }
        }
        
        // Create some data with various statuses to demonstrate filtering
        $statuses = ['success', 'error', 'pending', 'warning'];
        foreach ($statuses as $status) {
            DeviceData::factory()
                ->count(5)
                ->create([
                    'st' => $status,
                    'created_at' => now()->subHours(rand(1, 24)),
                ]);
        }
        
        // Create some data with various commands
        $commands = ['heartbeat', 'status', 'config', 'update', 'reboot', 'log'];
        foreach ($commands as $command) {
            DeviceData::factory()
                ->count(3)
                ->create([
                    'cmd' => $command,
                    'created_at' => now()->subHours(rand(1, 24)),
                ]);
        }
    }
}