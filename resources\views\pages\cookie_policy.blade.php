<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title><PERSON>ie Policy - Pixel Flow</title>
  <style>
    :root {
      --primary: #2c3e50;
      --secondary: #3498db;
      --background: #f4f6f8;
      --text: #333;
    }

    body {
      margin: 0;
      font-family: 'Segoe UI', sans-serif;
      background-color: var(--background);
      color: var(--text);
      line-height: 1.6;
    }

    header {
      background-color: var(--primary);
      color: white;
      text-align: center;
      padding: 40px 20px;
      position: relative;
    }

    .back-button {
      position: absolute;
      top: 20px;
      left: 20px;
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: 10px 15px;
      border-radius: 5px;
      text-decoration: none;
      font-size: 14px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .back-button:hover {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
      text-decoration: none;
    }

    .back-button::before {
      content: "←";
      font-size: 16px;
    }

    header img.logo {
      max-height: 60px;
      margin-bottom: 10px;
    }

    header h1 {
      margin: 0;
      font-size: 32px;
    }

    main {
      max-width: 1000px;
      margin: 30px auto;
      background: white;
      padding: 30px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border-radius: 8px;
    }

    h2 {
      color: var(--primary);
      border-bottom: 2px solid var(--secondary);
      padding-bottom: 5px;
      margin-top: 40px;
      font-size: 22px;
    }

    h3 {
      color: var(--primary);
      margin-top: 25px;
      font-size: 18px;
    }

    ul {
      margin-left: 20px;
      padding-left: 10px;
    }

    li {
      margin-bottom: 6px;
    }

    a {
      color: var(--secondary);
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    footer {
      text-align: center;
      padding: 20px;
      color: #888;
      font-size: 14px;
    }

    @media (max-width: 768px) {
      .back-button {
        position: relative;
        top: auto;
        left: auto;
        display: inline-block;
        margin-bottom: 20px;
      }
      
      header {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <header>
    <a href="{{ url('/') }}" class="back-button">Back to Home</a>
    <h1>Cookie Policy</h1>
    <p>Effective for users of Pixel Flow LLP</p>
  </header>

  <main>
    <p>In compliance with PECA 2016 and ETO 2002, Pixel Flow LLP provides this Cookie Policy to explain how cookies are used on <strong>www.pixelflow.com.pk</strong>.</p>

    <h2>Validity of Consent</h2>
    <p>User consent for cookies is valid for a maximum of 24 months.</p>

    <h2>Children Under 16</h2>
    <p>If under 16, please have a parent or guardian approve cookie usage.</p>

    <h2>Updates</h2>
    <p>You will be notified of any changes in cookie purposes.</p>

    <h2>1. What Are Cookies?</h2>
    <p>Cookies are small text files that store information on your device during website visits. They help recognize returning users, save preferences, and improve browsing experience.</p>

    <h2>2. Types of Cookies</h2>
    <h3>By Purpose</h3>
    <ul>
      <li><strong>Technical:</strong> Enable core functions (e.g. session login, navigation).</li>
      <li><strong>Personalization:</strong> Save preferences like language and region.</li>
      <li><strong>Analytical:</strong> Measure usage and improve content.</li>
      <li><strong>Advertising:</strong> Deliver relevant ads based on browsing behavior.</li>
    </ul>

    <h3>By Source</h3>
    <ul>
      <li><strong>First-party:</strong> Set by our domain.</li>
      <li><strong>Third-party:</strong> Set by external services (e.g., YouTube, Facebook).</li>
    </ul>

    <h3>By Duration</h3>
    <ul>
      <li><strong>Session:</strong> Expire after browser session ends.</li>
      <li><strong>Persistent:</strong> Stored for longer periods.</li>
    </ul>

    <h2>3. What Cookies Are Used?</h2>
    <ul>
      <li>Technical or Necessary Cookies</li>
      <li>Preference or Personalization Cookies</li>
      <li>Analysis or Measurement Cookies</li>
      <li>Marketing or Advertising Cookies</li>
    </ul>

    <h2>4. Who Uses the Cookie Data?</h2>
    <p>Pixel Flow (first-party) and external providers (third-party) use cookies to enhance functionality and deliver personalized content.</p>

    <h2>5. Managing Cookies</h2>
    <p>You can manage cookies via your browser:</p>
    <ul>
      <li><strong>Chrome:</strong> Settings > Privacy > Site Settings > Cookies</li>
      <li><strong>Firefox:</strong> Preferences > Privacy & Security</li>
      <li><strong>Safari:</strong> Preferences > Privacy</li>
      <li><strong>Edge:</strong> Settings > Cookies and Site Permissions</li>
    </ul>
    <p>Alternatively, use the browser's Help section.</p>

    <h3>Options:</h3>
    <ul>
      <li><strong>Delete:</strong> Clear browser history to remove all cookies.</li>
      <li><strong>Block:</strong> Disable cookie storage (may affect functionality).</li>
      <li><strong>Manage:</strong> Allow or block cookies per site.</li>
    </ul>

    <h2>6. Data Transfers to Third Countries</h2>
    <p>For third-party cookies, refer to the respective provider's privacy policy regarding international data transfers.</p>

    <h2>7. Profiling</h2>
    <p>If profiling affects your rights or decisions, we will inform you accordingly.</p>

    <h2>8. Data Retention</h2>
    <p>Cookie-related data will be retained no longer than 24 months unless otherwise specified.</p>
  </main>

  <footer>
    &copy; 2025 PixelFlow. All rights reserved.
  </footer>
</body>
</html>
