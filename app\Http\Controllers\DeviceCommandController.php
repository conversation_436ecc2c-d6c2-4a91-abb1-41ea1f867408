<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DeviceCommand;
use Illuminate\Support\Facades\Log;

class DeviceCommandController extends Controller
{
    /**
     * Store a newly created device command in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'device_sn' => 'required|string',
            'command' => 'required|string',
            'message_id' => 'required|integer',
            'data' => 'required|string',
            'raw_command' => 'required|string',
            'aims' => 'nullable|integer'
        ]);

        try {
            $command = DeviceCommand::create([
                'device_sn' => $request->device_sn,
                'command' => $request->command,
                'message_id' => $request->message_id,
                'data' => $request->data,
                'raw_command' => $request->raw_command,
                'status' => 'pending',
                'aims' => $request->aims ?? 0,
                'created_by' => auth()->id(),
            ]);

            // Log the command
            Log::channel('device_logs')->info('Device command created via AJAX', [
                'device_sn' => $request->device_sn,
                'command' => $request->command,
                'message_id' => $request->message_id,
                'aims' => $request->aims ?? 0
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Command created successfully',
                'command' => $command
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating device command: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error creating command: ' . $e->getMessage()
            ], 500);
        }
    }
}

