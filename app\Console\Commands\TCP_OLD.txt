<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\CommunicationLog;
use App\Models\DeviceCommand;
use Illuminate\Support\Facades\Log;

class TCPServer extends Command
{
    protected $signature = 'tcp:server';
    protected $description = 'Start a TCP server to communicate with devices';

    // Store multiple connected devices
    protected static array $clients = [];
    private $serverSocket;

    public function handle()
    {
        $ip = '0.0.0.0';
        $port = 8089;
        $server = stream_socket_server("tcp://$ip:$port", $errno, $errstr);

        if (!$server) {
            $this->error("Failed to start server: $errstr ($errno)");
            return 1;
        }

        stream_set_blocking($server, false);
        //$this->clients = [];

        $this->info("TCP Server started at $ip:$port");

        // Variables for periodic commands
        $lastDetailTime = time();

        while (true) {
            $newClient = @stream_socket_accept($server, 0);
            if ($newClient) {
                stream_set_blocking($newClient, false);
                self::$clients[] = [
                    'conn' => $newClient,
                    'sn' => null,
                    'last_detail_sent' => time()
                ];
                $this->info("New client connected.");
            }

            foreach (self::$clients as $index => &$clientInfo) {
                if (!is_array($clientInfo) || !isset($clientInfo['conn'])) {
                    unset(self::$clients[$index]); // Clean invalid entry
                    continue;
                }

                $conn = $clientInfo['conn'];
                $sn = $clientInfo['sn'] ?? null;

                // Read device data
                $data = @fread($conn, 2048);
                if ($data) {
                    $clean = trim($data, "#*");
                    $json = json_decode($clean, true);

                    if ($json && isset($json['cmd'])) {
                        $clientInfo['sn'] = $json['sn'] ?? $clientInfo['sn'];
                        //$response = $this->handleCommand($json);
                        $response = $this->processRequest($data, $conn);

                        if ($response) {
                            fwrite($conn, "#*" . json_encode($response) . "*#");
                        }
                    }
                }

                // Handle rent-safe detail check (triggered from DB)
                if ($sn) {

                    // Get all pending commands ordered by creation date
                    $pendingRent = DeviceCommand::where('status', 'pending')
                        ->orderBy('created_at')
                        ->first();

                    if ($pendingRent && !$pendingRent->created_at) {
                        // Send detail with timestamp
                        $timestamp = time();
                        $msgId = rand(10000, 99999);
                        $detailCmd = [
                            "cmd" => "detail",
                            "msg" => $msgId,
                            "aims" => 0,
                            "sn" => $sn,
                            "data" => [
                                "n" => 0,
                                "timestamp" => $timestamp
                            ]
                        ];

                        if (is_resource($conn) && !feof($conn)) {
                            fwrite($conn, "#*" . json_encode($detailCmd) . "*#");
                            $pendingRent->executed_at = $timestamp;
                            $pendingRent->save();
                            $this->info("Sent pre-rent detail to $sn");
                        } else {
                            $this->warn("Cannot write to $sn: broken connection");
                            fclose($conn);
                            unset(self::$clients[$index]);
                        }
                    }
                    //fwrite($conn, "#*" . json_encode($detailCmd) . "*#");


                    // Send detail commands every 5 seconds
                    $currentTime = time();
                    if ($currentTime - $lastDetailTime >= 5) {
                        $this->processPendingCommands($conn);
                        $lastDetailTime = $currentTime;
                    }
                }

                // Auto disconnect
                if (!is_resource($conn) || feof($conn)) {
                    fclose($conn);
                    unset(self::$clients[$index]);
                    $this->info("Client disconnected.");
                }
            }

            usleep(100000); // 100ms
        }

        fclose($server);
        return 0;
    }


    /**
     * Remove disconnected client
     */
    protected function removeDisconnectedClient($client)
    {
        // Find the client in the array
        $key = null;
        foreach (self::$clients as $k => $c) {
            if ($c === $client) {
                $key = $k;
                break;
            }
        }

        if ($key !== null) {
            // Only try to close if it's still a valid resource
            if (is_resource($client) && get_resource_type($client) === 'stream') {
                @fclose($client);
            }
            unset(self::$clients[$key]);
            $this->info("Client disconnected. Remaining clients: " . count(self::$clients));
        }
    }

    /**
     * Clean up disconnected clients
     */
    protected function cleanupDisconnectedClients()
    {
        foreach (self::$clients as $key => $client) {
            // Check if the client is still a valid resource
            if (!is_resource($client) || get_resource_type($client) !== 'stream') {
                unset(self::$clients[$key]);
                $this->info("Removed invalid client resource. Remaining clients: " . count(self::$clients));
                continue;
            }

            // Check if the client has disconnected
            try {
                $metadata = @stream_get_meta_data($client);
                if ($metadata && isset($metadata['eof']) && $metadata['eof']) {
                    if (is_resource($client)) {
                        @fclose($client);
                    }
                    unset(self::$clients[$key]);
                    $this->info("Client disconnected (EOF). Remaining clients: " . count(self::$clients));
                }
            } catch (\Exception $e) {
                // If we can't get metadata, the client is probably disconnected
                unset(self::$clients[$key]);
                $this->info("Client disconnected (Exception). Remaining clients: " . count(self::$clients));
            }
        }
    }

    private function acceptNewClients()
    {
        $newClient = @stream_socket_accept($this->serverSocket, 0);
        if ($newClient) {
            stream_set_blocking($newClient, false);
            self::$clients[] = $newClient;
            $this->info("New client connected: " . count(self::$clients));
        }
    }

    private function processClientMessages()
    {
        foreach (self::$clients as $index => $client) {
            $data = fread($client, 1024);
            if ($data !== false && strlen($data) > 0) {
                $response = $this->processRequest($data, $client);
                fwrite($client, json_encode($response) . "\n"); // Send response
            }
        }
    }


    private function oremoveDisconnectedClients()
    {
        foreach (self::$clients as $key => $client) {
            if (!is_resource($client) || stream_get_meta_data($client)['eof']) {
                fclose($client);
                unset(self::$clients[$key]);
                echo "Client $key disconnected and removed.\n";
            }
        }
    }

    private function processRequest($data, $conn)
    {
        try {
            // Trim protocol-specific markers
            $jsonData = trim($data, "#*");

            // Try to decode JSON
            $request = json_decode($jsonData, true);

            $this->info("Process request recieved from device :\n" . $jsonData);

            // Check if JSON is valid and has required fields
            if (!$request || !isset($request['cmd']) || !isset($request['sn'])) {
                $this->error("Invalid request format: " . $jsonData);
                $response = ['error' => 'Invalid request'];
                @fwrite($conn, json_encode($response));
                return;
            }

            $sn = $request['sn'];
            self::$clients[$sn] = $conn; // Store device connection
            //self::$clients[$sn] = $conn; // Store device connection

            $command = $request['cmd'];
            $response = [];

            // Process command
            switch ($command) {
                case 'login':
                    $response = $this->handleLogin($request, $sn);
                    break;
                case 'heart':
                    $response = $this->handleHeartbeat($request, $sn);
                    break;
                case 'detail':
                    $response = $this->handleDetailQuery($request, $sn);
                    break;
                case 'rent':
                    $response = $this->handleRent($request, $sn);
                    break;
                case 'return':
                    $response = $this->handleReturn($request, $sn);
                    break;
                case 'force':
                    $response = $this->handleForcePopOut($request, $sn);
                    break;
                case 'reboot':
                    $response = $this->handleReboot($request, $sn);
                    break;
                case 'vol':
                    $response = $this->handleVolumeControl($request, $sn);
                    break;
                case 'detailup':
                    $response = $this->handleDetailup($request, $sn);
                    break;
                case 'list_flash':
                    $response = $this->handleVoiceRefresh($request, $sn);
                    break;
                default:
                    $response = ['error' => 'Unknown command'];
            }

            $this->info("Received from device: " . $sn);
            $this->info($data);
            $this->info("Sending to device: " . $sn);
            $this->info("#*" . json_encode($response) . "*#");

            // Save log in database
            try {
                CommunicationLog::create([
                    'device_sn' => $sn,
                    'command' => $command,
                    'request_data' => $request,
                    'response_data' => $response
                ]);
            } catch (\Exception $e) {
                $this->error("Failed to save communication log: " . $e->getMessage());
            }

            // Process any pending commands for this device
            $this->processPendingCommands($conn);

            // Send response with error handling
            try {
                @fwrite($conn, "#*" . json_encode($response) . "*#");
            } catch (\Exception $e) {
                $this->error("Error sending response to client: " . $e->getMessage());
            }
        } catch (\Exception $e) {
            $this->error("Error processing request: " . $e->getMessage());
        }
    }

    public function processPendingCommands_old($conn = null)
    {
        $this->info('Processing pending device commands...');

        try {
            // Get all pending commands ordered by creation date
            $pendingCommands = \App\Models\DeviceCommand::where('status', 'pending')
                ->orderBy('created_at')
                ->get();

            if ($pendingCommands->isEmpty()) {
                $this->info("No pending commands to process");
                return 0;
            }

            foreach ($pendingCommands as $command) {
                // Check if rent command is expired (older than 45 seconds)
                if ($command->command === 'rent') {
                    $createdAt = new \DateTime($command->created_at);
                    $now = new \DateTime();
                    $interval = $createdAt->diff($now);
                    $secondsElapsed = $interval->s + ($interval->i * 60) + ($interval->h * 3600);

                    if ($secondsElapsed > 45) {
                        $this->warn("Rent command ID: {$command->id} for device: {$command->device_sn} expired (age: {$secondsElapsed}s)");

                        // Update command status to expired
                        $command->update([
                            'status' => 'expired',
                            'executed_at' => now()
                        ]);

                        // Log the expired command
                        \Illuminate\Support\Facades\Log::channel('device_logs')->info('Rent command expired', [
                            'command_id' => $command->id,
                            'device_sn' => $command->device_sn,
                            'age' => $secondsElapsed,
                            'created_at' => $command->created_at
                        ]);

                        continue; // Skip to next command
                    }
                }

                // If a specific connection is provided, only process commands for that device
                if ($conn) {
                    // Find the device SN for this connection
                    $deviceSn = null;
                    foreach (self::$clients as $sn => $client) {
                        if ($client === $conn && !is_numeric($sn)) {
                            $deviceSn = $sn;
                            break;
                        }
                    }

                    // Skip if this command is not for the current device
                    if ($deviceSn && $command->device_sn !== $deviceSn) {
                        continue;
                    }
                } else {
                    // If no specific connection, find the connection for this device
                    $conn = self::$clients[$command->device_sn] ?? null;

                    // Skip if device is not connected
                    if (!$conn || !is_resource($conn)) {
                        continue;
                    }
                }

                $this->info("Processing command ID: {$command->id} for device: {$command->device_sn}");

                try {
                    // Send the command to the device
                    $success = @fwrite($conn, $command->raw_command);

                    if ($success) {
                        // Update command status to sent
                        $command->update([
                            'status' => 'sent',
                            'executed_at' => now()
                        ]);

                        $this->info("Command sent successfully");

                        // Add a small delay between commands to avoid flooding the device
                        usleep(500000); // 0.5 second delay
                    } else {
                        // Mark as failed if sending was unsuccessful
                        $command->update(['status' => 'failed']);
                        $this->error("Failed to send command");
                    }
                } catch (\Exception $e) {
                    // Log the error and mark command as failed
                    $command->update(['status' => 'failed']);
                    $this->error("Error processing command: " . $e->getMessage());
                    \Illuminate\Support\Facades\Log::error("Error processing command ID {$command->id}: " . $e->getMessage());
                }
            }

            $this->info('Finished processing pending commands');
            return 0;
        } catch (\Exception $e) {
            $this->error("Error in processPendingCommands: " . $e->getMessage());
            return 1;
        }
    }

    public function processPendingCommands($conn = null)
    {
        $this->info('Processing pending device commands...');

        try {
            // Get all pending commands ordered by creation date
            $pendingCommands = \App\Models\DeviceCommand::where('status', 'pending')
                ->orderBy('created_at')
                ->get();

            if ($pendingCommands->isEmpty()) {
                $this->info("No pending commands to process");
                return 0;
            }

            foreach ($pendingCommands as $command) {
                // Check if rent command is expired (older than 45 seconds)
                if ($command->command === 'rent') {
                    $createdAt = new \DateTime($command->created_at);
                    $now = new \DateTime();
                    $interval = $createdAt->diff($now);
                    $secondsElapsed = $interval->s + ($interval->i * 60) + ($interval->h * 3600);

                    if ($secondsElapsed > 45) {
                        $this->warn("Rent command ID: {$command->id} for device: {$command->device_sn} expired (age: {$secondsElapsed}s)");

                        // Update command status to expired
                        $command->update([
                            'status' => 'expired',
                            'executed_at' => now()
                        ]);

                        // Log the expired command
                        \Illuminate\Support\Facades\Log::channel('device_logs')->info('Rent command expired', [
                            'command_id' => $command->id,
                            'device_sn' => $command->device_sn,
                            'age' => $secondsElapsed,
                            'created_at' => $command->created_at
                        ]);

                        continue; // Skip to next command
                    }
                }

                // If a specific connection is provided, find the device SN for this connection
                $connectionDeviceSn = null;
                if ($conn) {
                    foreach (self::$clients as $sn => $client) {
                        if ($client === $conn && !is_numeric($sn)) {
                            $connectionDeviceSn = $sn;
                            break;
                        }
                    }

                    // Skip if this command is not for the current device
                    if ($connectionDeviceSn && $command->device_sn !== $connectionDeviceSn) {
                        $this->info("Found device SN: $connectionDeviceSn for connection");
                        $this->info("Skipping command ID: {$command->id} - not for current device (command SN: {$command->device_sn}, connection SN: {$connectionDeviceSn})");
                        continue;
                    }
                } else {
                    // If no specific connection, find the connection for this device
                    $conn = self::$clients[$command->device_sn] ?? null;

                    // Skip if device is not connected
                    if (!$conn || !is_resource($conn)) {
                        $this->warn("Device {$command->device_sn} not connected, skipping command ID: {$command->id}");
                        continue;
                    }
                }

                // Double-check that we're sending to the correct device by comparing SNs
                $targetDeviceSn = null;
                foreach (self::$clients as $sn => $client) {
                    if ($client === $conn && !is_numeric($sn)) {
                        $targetDeviceSn = $sn;
                        break;
                    }
                }

                // Verify the command is being sent to the correct device
                if (!$targetDeviceSn || $targetDeviceSn !== $command->device_sn) {
                    $this->error("SN mismatch! Command ID: {$command->id} for device: {$command->device_sn}, but would be sent to: {$targetDeviceSn}");

                    // Mark as failed due to SN mismatch
                    $command->update([
                        'status' => 'failed',
                        'executed_at' => now()
                    ]);

                    // Log the error
                    \Illuminate\Support\Facades\Log::channel('device_logs')->error('Command SN mismatch', [
                        'command_id' => $command->id,
                        'command_device_sn' => $command->device_sn,
                        'target_device_sn' => $targetDeviceSn
                    ]);

                    continue; // Skip to next command
                }

                $this->info("Processing command ID: {$command->id} for device: {$command->device_sn}");

                try {
                    // Send the command to the device
                    $success = @fwrite($conn, $command->raw_command);

                    if ($success) {
                        // Update command status to sent
                        $command->update([
                            'status' => 'sent',
                            'executed_at' => now()
                        ]);

                        $this->info("Command sent successfully to device: {$command->device_sn}");

                        // Add a small delay between commands to avoid flooding the device
                        usleep(500000); // 0.5 second delay
                    } else {
                        // Mark as failed if sending was unsuccessful
                        $command->update(['status' => 'failed']);
                        $this->error("Failed to send command to device: {$command->device_sn}");
                    }
                } catch (\Exception $e) {
                    // Log the error and mark command as failed
                    $command->update(['status' => 'failed']);
                    $this->error("Error processing command: " . $e->getMessage());
                    \Illuminate\Support\Facades\Log::error("Error processing command ID {$command->id}: " . $e->getMessage());
                }
            }

            $this->info('Finished processing pending commands');
            return 0;
        } catch (\Exception $e) {
            $this->error("Error in processPendingCommands: " . $e->getMessage());
            return 1;
        }
    }


    // Handle the login command
    private function handleLogin($request, $sn)
    {
        // Log the login request
        $this->info("Device login request received from SN: {$sn}");

        // Extract device data from the request
        $deviceData = $request['data'] ?? [];

        // Prepare device attributes
        $deviceAttributes = [
            'status' => 1, // Online
            'last_login' => now(),
            'last_heartbeat_at' => now(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
        ];

        // Add additional device data if available
        if (!empty($deviceData)) {
            $deviceAttributes = array_merge($deviceAttributes, [
                'manufacturer' => $deviceData['mft'] ?? null,
                'model' => $deviceData['mod'] ?? null,
                'device_type' => $deviceData['type'] ?? null, // Use device_type consistently
                'slot_count' => $deviceData['num'] ?? null,
                'hardware_version' => $deviceData['hd'] ?? null,
                'firmware_version' => $deviceData['fw'] ?? null,
                'ccid' => $deviceData['ccid'] ?? null,
                'imei' => $deviceData['imei'] ?? null,
                'apn' => $deviceData['apn'] ?? null,
                'volume' => $deviceData['vol'] ?? null,
            ]);
        }

        // Check if device exists in database
        $device = \App\Models\Device::where('sn', $sn)->first();

        if ($device) {
            // Update existing device
            $this->info("Updating existing device: {$sn}");
            $device->update($deviceAttributes);
        } else {
            // Create new device with a default name based on SN
            $this->info("Creating new device with SN: {$sn}");
            $deviceAttributes['sn'] = $sn;
            $deviceAttributes['name'] = "Device {$sn}";

            $device = \App\Models\Device::create($deviceAttributes);
        }

        // Log successful login
        Log::info("Device login successful", [
            'device_sn' => $sn,
            'device_type' => $deviceData['type'] ?? 'unknown',
            'firmware' => $deviceData['fw'] ?? 'unknown',
            'timestamp' => now()->toDateTimeString()
        ]);

        // Send a heartbeat immediately after login
        $this->sendHeartbeatToDevice($sn);

        // Return standard login response
        return [
            'cmd' => 'login',
            'data' => [
                'r' => 0, // Success
                'msgack' => $request['msg'],
                'heart' => 45
            ]
        ];
    }

    /**
     * Handle the heartbeat command from a device
     *
     * @param array $request The request data
     * @param string $sn The device serial number
     * @return array The response data
     */
    private function handleHeartbeat($request, $sn)
    {
        $this->info("Received heartbeat from device: {$sn}");

        try {
            // Extract signal strength if available
            $signalStrength = $request['data']['csq'] ?? null;

            // Get the device from database
            $device = \App\Models\Device::where('sn', $sn)->first();

            if ($device) {
                // Update device heartbeat timestamp and status
                $device->update([
                    'status' => 1, // Online
                    'last_heartbeat_at' => now(),
                    'signal_strength' => $signalStrength
                ]);

                $this->info("Updated heartbeat for device {$sn}, signal strength: {$signalStrength}");

                // Log the heartbeat in communication logs
                try {
                    \App\Models\CommunicationLog::create([
                        'device_sn' => $sn,
                        'command' => 'heart',
                        'request_data' => $request,
                        'response_data' => [
                            'cmd' => 'heart',
                            'data' => [
                                'msgack' => $request['msg'] ?? rand(10000, 99999),
                                'int' => 45 // Heartbeat interval in seconds
                            ]
                        ]
                    ]);
                } catch (\Exception $e) {
                    $this->error("Failed to log heartbeat: " . $e->getMessage());
                }
            } else {
                // Device not found in database
                $this->warn("Heartbeat received from unknown device: {$sn}");

                // Optionally create a new device record
                try {
                    $device = \App\Models\Device::create([
                        'sn' => $sn,
                        'name' => "Device {$sn}",
                        'model' => 'Unknown',
                        'status' => 1, // Online
                        'last_heartbeat_at' => now(),
                        'signal_strength' => $signalStrength,
                        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null
                    ]);

                    $this->info("Created new device record for {$sn}");
                } catch (\Exception $e) {
                    $this->error("Failed to create device record: " . $e->getMessage());
                }
            }

            // Return standard heartbeat response
            return [
                'cmd' => 'heart',
                'data' => [
                    'msgack' => $request['msg'] ?? rand(10000, 99999),
                    'int' => 45 // Heartbeat interval in seconds
                ]
            ];
        } catch (\Exception $e) {
            $this->error("Error processing heartbeat: " . $e->getMessage());

            // Return a standard response even if there was an error
            return [
                'cmd' => 'heart',
                'data' => [
                    'msgack' => $request['msg'] ?? rand(10000, 99999),
                    'int' => 45
                ]
            ];
        }
    }

    private function handleDetailQuery($request, $sn)
    {
        return [
            'cmd' => 'detail',
            'data' => [
                'msgack' => $request['msg'],
                'st' => 2,
                'd' => [
                    ['n' => 1, 's' => 1, 'sn' => 'zd190421002', 'e' => 1],
                    ['n' => 2, 's' => 1, 'sn' => 'zd190421001', 'e' => 2]
                ]
            ]
        ];
    }

    private function handleRent($request, $sn)
    {
        $this->error("Rent Command Send");
        return [
            'cmd' => 'rent',
            'sn' => $sn,
            'data' => [
                //'r' => 0, // Success
                //'sn'=>'LC2306233899',
                'msgack' => $request['msg'],
                'n' => 0 //$request['data']['n']
            ]
        ];
    }

    private function handleForce($request, $sn)
    {
        return [
            'cmd' => 'force',
            'sn' => $sn,
            'data' => [
                //'r' => 0, // Success
                //'sn'=>'LC2306233899',
                'msgack' => $request['msg'],
                'n' => 0 //$request['data']['n']
            ]
        ];
    }

    private function handleDetailup($request, $sn)
    {
        // Process the powerbank details from the request
        $this->processPowerbankDetails($request, $sn);

        // Process any pending commands for this device
        //$this->processPendingCommands($conn);

        //$this->error("sending rent command");
        //$response = $this->handleRent($request, $sn);
        //return $response;

        // Return the standard response
        return [
            'cmd' => 'detailup',
            'data' => [
                'msgack' => $request['msg'],
            ]
        ];
    }

    /**
     * Process powerbank details from detailup command and save to database
     */
    private function processPowerbankDetails($request, $deviceSn)
    {
        // Get the device from the database
        $device = \App\Models\Device::where('sn', $deviceSn)->first();

        if (!$device) {
            $this->error("Device with SN {$deviceSn} not found in database");
            return;
        }

        $this->info("Processing powerbank details for device {$deviceSn}");

        // Get the slot details from the request
        $slots = $request['data']['d'] ?? [];

        if (empty($slots)) {
            $this->info("No slot data found in request");
            return;
        }

        // Process each slot
        foreach ($slots as $slot) {
            $slotNumber = $slot['n'] ?? null;
            $status = $slot['s'] ?? null;
            $serialNumber = $slot['sn'] ?? null;
            $chargeCode = $slot['e'] ?? null;

            $this->info("Processing slot {$slotNumber}: status={$status}, SN={$serialNumber}, chargeCode={$chargeCode}");

            // Skip empty slots or slots without a serial number
            if ($status === 0 || empty($serialNumber)) {
                $this->info("Slot {$slotNumber} is empty or has no serial number");
                continue;
            }

            // Check if powerbank exists
            $powerbank = \App\Models\Powerbank::where('serial_number', $serialNumber)->first();

            if ($powerbank) {
                // Update existing powerbank
                $this->info("Updating existing powerbank {$serialNumber}");

                $powerbank->update([
                    'device_id' => $device->id,
                    'slot_number' => $slotNumber,
                    'status' => $this->mapStatusCode($status, $chargeCode),
                ]);
            } else {
                // Create new powerbank
                $this->info("Creating new powerbank {$serialNumber}");

                \App\Models\Powerbank::create([
                    'serial_number' => $serialNumber,
                    'device_id' => $device->id,
                    'capacity' => 5000, // Default capacity
                    'current_charge' => 100, // Default charge
                    'slot_number' => $slotNumber,
                    'status' => $this->mapStatusCode($status, $chargeCode),
                    'charge_cycles' => 0,
                ]);
            }
        }

        // Log the successful processing
        $this->info("Successfully processed powerbank details for device {$deviceSn}");
    }

    /**
     * Map status code from device to powerbank status
     */
    private function mapStatusCode($status, $errorCode)
    {
        // Status 1 means the slot has a powerbank
        if ($status === 1) {
            // Check error code to determine status
            if ($errorCode === 9) {
                return 'available'; // Error code 9 seems to be normal
            } elseif ($errorCode === 8) {
                return 'charging'; // Assuming error code 8 means charging
            } else {
                return 'maintenance'; // Any other error code
            }
        }

        // Default to available
        return 'available';
    }

    /**
     * Handle the return command
     *
     * @param array $request The request data
     * @param string $sn The device serial number
     * @return array The response data
     */
    private function handleReturn($request, $sn)
    {
        $this->info("Processing return command from device: {$sn}");

        // Extract data from the request
        $slotNumber = $request['data']['n'] ?? null;
        $powerbankSn = $request['data']['sn'] ?? null;
        $errorCode = $request['data']['e'] ?? null;
        $slotData = $request['data']['d'] ?? [];
        $slotStatus = $request['data']['st'] ?? 0;

        if (!$slotNumber || !$powerbankSn) {
            $this->error("Invalid return request: Missing slot number or powerbank SN");
            return [
                'cmd' => 'return',
                'data' => [
                    'msgack' => $request['msg'],
                    'r' => 1, // Error
                    'n' => $slotNumber ?? 0
                ]
            ];
        }

        $this->info("Powerbank {$powerbankSn} returned to slot {$slotNumber} in device {$sn}");

        try {
            // Get the device from database
            $device = \App\Models\Device::where('sn', $sn)->first();

            if (!$device) {
                $this->error("Device with SN {$sn} not found in database");
                return [
                    'cmd' => 'return',
                    'data' => [
                        'msgack' => $request['msg'],
                        'r' => 1, // Error
                        'n' => $slotNumber
                    ]
                ];
            }

            // Find the powerbank by serial number
            $powerbank = \App\Models\Powerbank::where('serial_number', $powerbankSn)->first();

            if (!$powerbank) {
                // Powerbank not found in database, create a new record
                $this->info("Powerbank {$powerbankSn} not found in database, creating new record");

                $powerbank = \App\Models\Powerbank::create([
                    'serial_number' => $powerbankSn,
                    'device_id' => $device->id,
                    'slot_number' => $slotNumber,
                    'status' => 'available',
                    'current_charge' => 100, // Assume full charge for new powerbanks
                    'health' => 100,
                    'charge_cycles' => 0,
                    'last_maintenance_at' => null
                ]);

                $this->info("Created new powerbank record with ID: {$powerbank->id}");
            } else {
                // Update existing powerbank record
                $this->info("Updating existing powerbank record with ID: {$powerbank->id}");

                // Determine the status based on error code
                $status = 'available';
                if ($errorCode && $errorCode != 9) { // 9 is normal status
                    $status = 'maintenance';

                    // Log the error
                    \App\Models\PowerbankIssue::create([
                        'powerbank_id' => $powerbank->id,
                        'device_id' => $device->id,
                        'issue_type' => 'error_code',
                        'description' => "Error code {$errorCode} detected during return",
                        'status' => 'pending',
                        'reported_at' => now()
                    ]);

                    $this->warn("Powerbank {$powerbankSn} returned with error code {$errorCode}, marked for maintenance");
                }

                // Update powerbank details
                $powerbank->update([
                    'device_id' => $device->id,
                    'slot_number' => $slotNumber,
                    'status' => $status,
                    // Don't update charge level here as we don't have that info
                ]);
            }

            // Find the active rental for this powerbank
            $rental = \App\Models\PowerbankRental::where('powerbank_id', $powerbank->id)
                ->whereNull('returned_at')
                ->latest('rented_at')
                ->first();

            if ($rental) {
                // Calculate rental duration and fee
                $rentedAt = new \DateTime($rental->rented_at);
                $returnedAt = new \DateTime();
                $interval = $rentedAt->diff($returnedAt);

                // Calculate hours (rounded up)
                $hours = $interval->h + ($interval->i > 0 ? 1 : 0) + ($interval->d * 24);

                // Calculate fee (example: $2 per hour with $10 maximum)
                $hourlyRate = 2.00;
                $maxFee = 10.00;
                $rentalFee = min($hours * $hourlyRate, $maxFee);

                // Update rental record
                $rental->update([
                    'returned_at' => now(),
                    'return_charge' => null, // We don't have this info from the device
                    'rental_fee' => $rentalFee,
                    'return_device_id' => $device->id,
                    'return_slot_number' => $slotNumber
                ]);

                $this->info("Updated rental record ID: {$rental->id} with return information");
                $this->info("Rental duration: {$hours} hours, Fee: \${$rentalFee}");
            } else {
                $this->warn("No active rental found for powerbank {$powerbankSn}");
            }

            // Process all slot data to update device inventory
            if (!empty($slotData)) {
                $this->processSlotData($device, $slotData);
            }

            // Return success response
            return [
                'cmd' => 'return',
                'data' => [
                    'msgack' => $request['msg'],
                    'r' => 0, // Success
                    'n' => $slotNumber
                ]
            ];
        } catch (\Exception $e) {
            $this->error("Error processing return command: " . $e->getMessage());

            // Log the error
            Log::error("Return command error for device {$sn}, powerbank {$powerbankSn}: " . $e->getMessage());

            return [
                'cmd' => 'return',
                'data' => [
                    'msgack' => $request['msg'],
                    'r' => 1, // Error
                    'n' => $slotNumber
                ]
            ];
        }
    }

    /**
     * Process slot data from device and update inventory
     *
     * @param \App\Models\Device $device The device
     * @param array $slotData Array of slot data
     */
    private function processSlotData($device, $slotData)
    {
        $this->info("Processing slot data for device {$device->sn}");

        foreach ($slotData as $slot) {
            $slotNumber = $slot['n'] ?? null;
            $status = $slot['s'] ?? null;
            $powerbankSn = $slot['sn'] ?? null;
            $errorCode = $slot['e'] ?? null;

            // Skip invalid slots
            if (!$slotNumber) {
                continue;
            }

            // Empty slot
            if ($status === 0 || empty($powerbankSn)) {
                // Check if there was a powerbank in this slot and mark it as removed
                $existingPowerbank = \App\Models\Powerbank::where('device_id', $device->id)
                    ->where('slot_number', $slotNumber)
                    ->first();

                if ($existingPowerbank) {
                    $existingPowerbank->update([
                        'slot_number' => null,
                        'status' => 'unknown'
                    ]);

                    $this->warn("Powerbank {$existingPowerbank->serial_number} was in slot {$slotNumber} but is now missing");
                }

                continue;
            }

            // Occupied slot - find or create powerbank
            $powerbank = \App\Models\Powerbank::where('serial_number', $powerbankSn)->first();

            if (!$powerbank) {
                // Create new powerbank record
                $powerbank = \App\Models\Powerbank::create([
                    'serial_number' => $powerbankSn,
                    'device_id' => $device->id,
                    'slot_number' => $slotNumber,
                    'status' => ($errorCode && $errorCode != 9) ? 'maintenance' : 'available',
                    'current_charge' => 100, // Assume full charge
                    'health' => 100,
                    'charge_cycles' => 0
                ]);

                $this->info("Created new powerbank record for {$powerbankSn} in slot {$slotNumber}");
            } else {
                // Update existing powerbank
                $status = ($errorCode && $errorCode != 9) ? 'maintenance' : 'available';

                // Only update if the powerbank is not already assigned to another slot
                if ($powerbank->slot_number != $slotNumber || $powerbank->device_id != $device->id) {
                    $powerbank->update([
                        'device_id' => $device->id,
                        'slot_number' => $slotNumber,
                        'status' => $status
                    ]);

                    $this->info("Updated powerbank {$powerbankSn} to slot {$slotNumber} with status {$status}");
                }
            }

            // Log error if present
            if ($errorCode && $errorCode != 9) {
                \App\Models\PowerbankIssue::create([
                    'powerbank_id' => $powerbank->id,
                    'device_id' => $device->id,
                    'issue_type' => 'error_code',
                    'description' => "Error code {$errorCode} detected during inventory update",
                    'status' => 'pending',
                    'reported_at' => now()
                ]);

                $this->warn("Powerbank {$powerbankSn} has error code {$errorCode}, marked for maintenance");
            }
        }
    }

    private function handleForcePopOut($request, $sn)
    {
        return [
            "cmd" => "force",
            "sn" => $sn,
            "msgack" => $request['msg'],
            "data" => [
                "msgack" => 177,
                "n" => 3,
                "r" => 0,
                "d" =>
                [
                    ["n" => 1, "s" => 1, "sn" => "LC2012153009", "e" => 8],
                    ["n" => 2, "s" => 1, "sn" => "BP1910450152", "e" => 8],
                    ["n" => 3, "s" => 0],
                    ["n" => 4, "s" => 1, "sn" => "DT1910106011", "e" => 9]
                ],
                "st" => 3
            ]
        ];
    }

    private function handleReboot($request, $sn)
    {
        return [
            "cmd" => "reboot",
            "sn" => $sn,
            "data" => [
                "dev" => 1
            ]
        ];
    }

    private function handleVolumeControl($request, $sn)
    {
        return [
            "data" => "volume",
            "sn" => $sn,
            [
                "r" => 1, //0 =query , 1 = set
                "n" => 100 // 0 to 100 volume
            ]
        ];
    }

    private function handleVoiceRefresh($request, $sn)
    {
        return [
            "cmd" => "list_flash",
            "sn" => $sn,
            "data" => [
                "url" => ""
            ]
        ];
    }

    public static function oldsendCommandToAll($command)
    {
        foreach (self::$clients as $client) {
            //if (is_resource($client)) {
            fwrite($client, "#*" . json_decode($command) . "*#");
            //}
        }
    }

    public function sendCommandToAll($command)
    {
        foreach (self::$clients as $client) {
            if (is_resource($client)) {
                fwrite($client, "#*" . json_encode($command) . "*#");
            }
        }
    }

    /**
     * Send heartbeats to all connected devices
     */
    private function sendHeartbeats()
    {
        // Only send heartbeats every 30 seconds to avoid flooding
        static $lastHeartbeatTime = 0;
        $currentTime = time();

        if ($currentTime - $lastHeartbeatTime < 30) {
            return;
        }

        $lastHeartbeatTime = $currentTime;

        $this->info("Sending heartbeats to all connected devices...");

        foreach (self::$clients as $sn => $client) {
            // Skip numeric keys (these are connections without SN)
            if (is_numeric($sn)) {
                continue;
            }

            if (is_resource($client)) {
                $heartbeatResponse = [
                    'cmd' => 'heart',
                    'data' => [
                        'msgack' => rand(10000, 99999),
                        'int' => 45
                    ]
                ];

                try {
                    $result = @fwrite($client, "#*" . json_encode($heartbeatResponse) . "*#");
                    if ($result === false) {
                        $this->error("Failed to send heartbeat to device {$sn}");
                        $this->removeDisconnectedClient($client);
                    } else {
                        $this->info("Heartbeat sent to device {$sn}");

                        // Update device heartbeat timestamp in database
                        try {
                            $device = \App\Models\Device::where('sn', $sn)->first();
                            if ($device) {
                                $device->update(['last_heartbeat_at' => now()]);
                            }
                        } catch (\Exception $e) {
                            $this->error("Error updating device heartbeat in database: " . $e->getMessage());
                        }
                    }
                } catch (\Exception $e) {
                    $this->error("Error sending heartbeat to device {$sn}: " . $e->getMessage());
                    $this->removeDisconnectedClient($client);
                }
            } else {
                $this->error("Invalid resource for device {$sn}");
                unset(self::$clients[$sn]);
            }
        }
    }

    /**
     * Send a heartbeat to a specific device
     */
    private function sendHeartbeatToDevice($sn)
    {
        if (isset(self::$clients[$sn]) && is_resource(self::$clients[$sn])) {
            $heartbeatResponse = [
                'cmd' => 'heart',
                'data' => [
                    'msgack' => rand(10000, 99999),
                    'int' => 45
                ]
            ];

            $this->info("Sending initial heartbeat to device: {$sn}");

            try {
                @fwrite(self::$clients[$sn], "#*" . json_encode($heartbeatResponse) . "*#");
            } catch (\Exception $e) {
                $this->error("Error sending heartbeat to device {$sn}: " . $e->getMessage());
            }
        } else {
            $this->error("Cannot send heartbeat to device {$sn}: Connection not found");
        }
    }
}
