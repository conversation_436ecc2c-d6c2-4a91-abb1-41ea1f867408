<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('customers')) {
            Schema::table('customers', function (Blueprint $table) {
                if (Schema::hasColumn('customers', 'company_name')) {
                    $table->dropColumn('company_name');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('customers')) {
            Schema::table('customers', function (Blueprint $table) {
                if (!Schema::hasColumn('customers', 'company_name')) {
                    if (Schema::hasColumn('customers', 'contact_no')) {
                        $table->string('company_name')->nullable()->after('contact_no');
                    } else {
                        $table->string('company_name')->nullable();
                    }
                }
            });
        }
    }
};
