<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Otp extends Model
{
    use HasFactory;

    protected $fillable = [
        'identifier',
        'type', // 'email' or 'phone'
        'token',
        'valid',
        'expires_at',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'valid' => 'boolean',
    ];

    public function isValid()
    {
        return $this->valid && now()->lt($this->expires_at);
    }
}
