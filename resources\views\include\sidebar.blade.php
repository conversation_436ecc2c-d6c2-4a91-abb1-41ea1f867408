<!-- ========== Left Sidebar Start ========== -->
<div class="left-side-menu">

    <div class="slimscroll-menu">

        <!--- Sidemenu -->
        <div id="sidebar-menu">

            <ul class="metismenu" id="side-menu">

                <li class="menu-title">Navigation</li>

                <li>
                    <a href="{{ route('dashboard') }}">
                        <i class="fe-home"></i>
                        <span> Dashboard </span>
                    </a>
                </li>

                <!-- User, Role & Permission Management Section -->
                @can('view-users')
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-users"></i>
                            <span>User Management</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            @can('view-users')
                                <li><a href="{{ route('user.index') }}">List Users</a></li>
                            @endcan
                            @can('create-users')
                                <li><a href="{{ route('user.create') }}">Create User</a></li>
                            @endcan
                        </ul>
                    </li>
                @endcan

                @can('view-roles')
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-shield"></i>
                            <span>Role Management</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            @can('view-roles')
                                <li><a href="{{ route('role.index') }}">List Roles</a></li>
                            @endcan
                            @can('create-roles')
                                <li><a href="{{ route('role.create') }}">Create Role</a></li>
                            @endcan
                        </ul>
                    </li>
                @endcan

                @can('view-permissions')
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-lock"></i>
                            <span>Permission Management</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            @can('view-permissions')
                                <li><a href="{{ route('permission.index') }}">List Permissions</a></li>
                            @endcan
                            @can('create-permissions')
                                <li><a href="{{ route('permission.create') }}">Create Permission</a></li>
                            @endcan
                        </ul>
                    </li>
                @endcan

                @can('view-devices')
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-monitor"></i>
                            <span>Devices</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            @can('view-devices')
                                <li><a href="{{ route('device.index') }}">List of Devices</a></li>
                            @endcan
                            @can('create-devices')
                                <li><a href="{{ route('device.create') }}">Add Device</a></li>
                            @endcan
                        </ul>
                    </li>
                @endcan

                @can('view-device-groups')
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-layers"></i>
                            <span>Device Groups</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            @can('view-device-groups')
                                <li><a href="{{ route('devicegroups.index') }}">List Groups</a></li>
                            @endcan
                            @can('create-device-groups')
                                <li><a href="{{ route('devicegroups.create') }}">Create Group</a></li>
                            @endcan
                        </ul>
                    </li>
                @endcan

                @can('view-ad-materials')
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-image"></i>
                            <span>Ad Materials</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            @can('view-ad-materials')
                                <li><a href="{{ route('admaterial.index') }}">List Materials</a></li>
                            @endcan
                            @can('create-ad-materials')
                                <li><a href="{{ route('admaterial.create') }}">Add Material</a></li>
                            @endcan
                        </ul>
                    </li>
                @endcan

                @can('view-ad-plans')
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-calendar"></i>
                            <span>Ad Schedules</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            @can('view-ad-plans')
                                <li><a href="{{ route('adplan.index') }}">List Schedules</a></li>
                            @endcan
                            @can('create-ad-plans')
                                <li><a href="{{ route('adplan.create') }}">Create Schedule</a></li>
                            @endcan
                        </ul>
                    </li>
                @endcan

                @can('view-customers')
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-users"></i>
                            <span>Customers</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            @can('view-customers')
                                <li><a href="{{ route('customer.index') }}">List Customers</a></li>
                            @endcan
                            @can('create-customers')
                                <li><a href="{{ route('customer.create') }}">Add Customer</a></li>
                            @endcan
                        </ul>
                    </li>
                @endcan

                <!-- Powerbanks Menu -->
                <li>
                    <a href="javascript: void(0);">
                        <i class="fe-battery"></i>
                        <span>Powerbanks</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('powerbanks.index') }}">All Powerbanks</a></li>
                        <li><a href="{{ route('powerbanks.create') }}">Add New Powerbank</a></li>
                        {{-- <li><a href="{{ route('powerbanks.issues.index') }}">Reported Issues</a></li> --}}
                    </ul>
                </li>

                <!-- System Administration Menu -->
                @if (auth()->user()->hasPermission('view-user-activity') ||
                        auth()->user()->hasPermission('view-audit-trail') ||
                        auth()->user()->hasPermission('manage-backups'))
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-settings"></i>
                            <span>System Administration</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            @if (auth()->user()->hasPermission('view-user-activity'))
                                <li><a href="{{ route('admin.user-activity.index') }}">User Activity</a></li>
                            @endif
                            @if (auth()->user()->hasPermission('view-audit-trail'))
                                <li><a href="{{ route('admin.audit-trail.index') }}">Audit Trail</a></li>
                            @endif
                            @if (auth()->user()->hasPermission('manage-backups'))
                                <li><a href="{{ route('admin.backups.index') }}">Database Backups</a></li>
                            @endif
                        </ul>
                    </li>
                @endif

                <li>
                    <a href="javascript: void(0);">
                        <i class="fe-shield"></i>
                        <span> Security </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('profile.edit') }}">Change Password</a></li>
                        <li><a href="{{ route('two-factor.manage') }}">Two-Factor Authentication</a></li>
                    </ul>
                </li>

                {{-- <li>
                    <a href="javascript: void(0);">
                        <i class="fe-database"></i>
                        <span>Device Data</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('device-data.index') }}">All Device Data</a></li>
                    </ul>
                </li> --}}

            </ul>

        </div>
        <!-- End Sidebar -->

        <div class="clearfix"></div>

    </div>
    <!-- Sidebar -left -->

</div>
<!-- Left Sidebar End -->
