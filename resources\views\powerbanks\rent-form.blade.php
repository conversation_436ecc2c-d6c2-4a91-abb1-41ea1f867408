<div class="card">
    <div class="card-header">
        <h5 class="card-title">Rent Powerbank</h5>
    </div>
    <div class="card-body">
        <form action="{{ route('powerbanks.rent', $powerbank->id) }}" method="POST">
            @csrf
            <div class="form-group">
                <label for="customer_id">Customer ID</label>
                <input type="text" class="form-control @error('customer_id') is-invalid @enderror" 
                    id="customer_id" name="customer_id" value="{{ old('customer_id') }}" required>
                @error('customer_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <button type="submit" class="btn btn-primary">Rent Powerbank</button>
        </form>
    </div>
</div>