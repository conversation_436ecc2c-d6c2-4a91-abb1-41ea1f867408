<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApiLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'method',
        'url',
        'headers',
        'request_data',
        'response_data',
        'status_code',
        'ip_address',
    ];

    protected $casts = [
        'headers' => 'array',
        'request_data' => 'array',
        'response_data' => 'array',
    ];
}


// namespace App\Models;

// use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Model;

// class ApiLog extends Model
// {
//     use HasFactory;

//     protected $fillable = [
//         'url',
//         'method',
//         'request_data',
//         'response_data',
//         'status_code',
//         'ip_address'
//     ];
// }
