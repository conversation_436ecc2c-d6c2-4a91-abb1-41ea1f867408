@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Assign Devices to Group: {{ $devicegroup->name }}</h4>
                    <p class="sub-header">
                        Select the devices you want to include in this group.
                    </p>

                    <form method="POST" action="{{ route('devicegroup.assign.devices', $devicegroup->id) }}">
                        @csrf

                        <div class="form-group">
                            <div class="mb-3">
                                <button type="button" class="btn btn-sm btn-outline-primary mr-1" id="selectAll">Select
                                    All</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAll">Deselect
                                    All</button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered table-hover" id="devices-table">
                                    <thead>
                                        <tr>
                                            <th width="5%">Select</th>
                                            <th>Serial Number</th>
                                            <th>Model</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($devices as $device)
                                            <tr>
                                                <td class="text-center">
                                                    <input type="checkbox" name="devices[]" value="{{ $device->id }}"
                                                        {{ in_array($device->id, $assignedDevices) ? 'checked' : '' }}>
                                                </td>
                                                <td>{{ $device->sn }}</td>
                                                <td>{{ $device->model }}</td>
                                                <td>{{ $device->status }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="form-group text-right">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-1"></i> Save Changes
                            </button>
                            <a href="{{ route('devicegroups.show', $devicegroup->id) }}" class="btn btn-light ml-1">
                                <i class="fas fa-times mr-1"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Select all devices
            document.getElementById('selectAll').addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('input[name="devices[]"]');
                checkboxes.forEach(checkbox => checkbox.checked = true);
            });

            // Deselect all devices
            document.getElementById('deselectAll').addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('input[name="devices[]"]');
                checkboxes.forEach(checkbox => checkbox.checked = false);
            });
        });
    </script>
@endsection
