<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\CommunicationLog;
use App\Models\DeviceCommand;
use Illuminate\Support\Facades\Log;

class TCPServer extends Command
{
    protected $signature = 'tcp:server';
    protected $description = 'Start a TCP server to communicate with devices';

    // Store multiple connected devices
    protected static array $clients = [];
    private $serverSocket;

    public function handle()
    {
        $ip = '0.0.0.0';
        $port = 8089;
        $this->serverSocket = stream_socket_server("tcp://$ip:$port", $errno, $errstr);

        if (!$this->serverSocket) {
            $this->error("Failed to start server: $errstr ($errno)");
            return 1;
        }

        stream_set_blocking($this->serverSocket, false);

        $this->info("TCP Server started at $ip:$port");
        $this->info("Waiting for connections...");

        // Variables for periodic tasks
        $lastDetailTime = time();
        $lastHeartbeatTime = time();
        $lastCleanupTime = time();
        $lastMemoryCheckTime = time();

        // Rate limiting for command processing
        $commandProcessingInterval = 5; // Process commands every 5 seconds

        while (true) {
            // Accept new clients
            $newClient = @stream_socket_accept($this->serverSocket, 0);
            if ($newClient) {
                stream_set_blocking($newClient, false);

                // Store new clients with proper structure
                $clientId = count(self::$clients) + 1;
                self::$clients[$clientId] = [
                    'conn' => $newClient,
                    'sn' => null,
                    'connected_at' => time(),
                    'last_activity' => time(),
                    'last_heartbeat_sent' => 0,
                    'last_command_sent' => 0 // Track when last command was sent
                ];

                $this->info("New client connected. Total clients: " . count(self::$clients));
            }

            // Process existing clients
            foreach (self::$clients as $index => &$clientInfo) {
                $conn = $clientInfo['conn'];
                $sn = $clientInfo['sn'] ?? null;

                // Check if connection is still valid
                if (!is_resource($conn) || feof($conn)) {
                    $this->warn("Client disconnected (invalid resource or EOF): " . ($sn ?? "unknown"));
                    $this->removeDisconnectedClient($index);
                    continue;
                }

                // Read device data
                $data = @fread($conn, 2048);
                if ($data && strlen($data) > 0) {
                    // Update last activity timestamp
                    $clientInfo['last_activity'] = time();

                    $clean = trim($data, "#*");
                    $json = json_decode($clean, true);

                    if ($json && isset($json['cmd'])) {
                        // Store SN if available
                        if (isset($json['sn']) && !empty($json['sn'])) {
                            $clientInfo['sn'] = $json['sn'];

                            // If this is a new SN, update the client index
                            if (!is_numeric($json['sn']) && !isset(self::$clients[$json['sn']])) {
                                self::$clients[$json['sn']] = &$clientInfo;
                            }
                        }

                        $response = $this->processRequest($data, $conn);
                    }
                }
            }

            // Current time for periodic tasks
            $currentTime = time();

            // Process pending commands with rate limiting
            if ($currentTime - $lastDetailTime >= $commandProcessingInterval) {
                $this->processPendingCommands();
                $lastDetailTime = $currentTime;
            }

            // Send heartbeats every 30 seconds
            if ($currentTime - $lastHeartbeatTime >= 30) {
                $this->sendHeartbeats();
                $lastHeartbeatTime = $currentTime;
            }

            // Clean up inactive connections every 60 seconds
            if ($currentTime - $lastCleanupTime >= 60) {
                $this->cleanupInactiveConnections();
                $lastCleanupTime = $currentTime;
            }

            // Check memory usage every 5 minutes
            if ($currentTime - $lastMemoryCheckTime >= 300) {
                $memoryUsage = memory_get_usage(true) / 1024 / 1024;
                $this->info("Memory usage: {$memoryUsage} MB");
                $lastMemoryCheckTime = $currentTime;

                // If memory usage is too high, restart the server
                if ($memoryUsage > 500) { // 500 MB threshold
                    $this->warn("Memory usage too high ({$memoryUsage} MB). Restarting server...");

                    // Close all connections
                    foreach (self::$clients as $client) {
                        if (isset($client['conn']) && is_resource($client['conn'])) {
                            @fclose($client['conn']);
                        }
                    }

                    // Close server socket
                    fclose($this->serverSocket);

                    // Exit with success code to allow supervisor to restart
                    return 0;
                }
            }

            usleep(100000); // 100ms sleep to prevent CPU hogging
        }

        // Close server socket (this will never be reached in normal operation)
        fclose($this->serverSocket);
        return 0;
    }


    /**
     * Remove a disconnected client
     *
     * @param mixed $clientId The client ID to remove
     * @return void
     */
    private function removeDisconnectedClient($clientId)
    {
        if (!isset(self::$clients[$clientId])) {
            return;
        }

        $clientInfo = self::$clients[$clientId];
        $sn = $clientInfo['sn'] ?? null;

        // Close the connection if it's still a resource
        if (isset($clientInfo['conn']) && is_resource($clientInfo['conn'])) {
            @fclose($clientInfo['conn']);
        }

        // Remove the client from the array
        unset(self::$clients[$clientId]);

        // If this client had an SN, also remove the SN-indexed reference
        if ($sn && isset(self::$clients[$sn])) {
            unset(self::$clients[$sn]);
        }

        $this->info("Removed disconnected client" . ($sn ? " with SN: {$sn}" : ""));

        // Update device status in database if we have an SN
        if ($sn) {
            try {
                $device = \App\Models\Device::where('sn', $sn)->first();
                if ($device) {
                    $device->update(['status' => 0]); // Mark as offline
                    $this->info("Updated device {$sn} status to offline in database");
                }
            } catch (\Exception $e) {
                $this->error("Error updating device status: " . $e->getMessage());
            }
        }
    }

    /**
     * Clean up disconnected clients
     */
    protected function cleanupDisconnectedClients()
    {
        foreach (self::$clients as $key => $client) {
            // Check if the client is still a valid resource
            if (!is_resource($client) || get_resource_type($client) !== 'stream') {
                unset(self::$clients[$key]);
                $this->info("Removed invalid client resource. Remaining clients: " . count(self::$clients));
                continue;
            }

            // Check if the client has disconnected
            try {
                $metadata = @stream_get_meta_data($client);
                if ($metadata && isset($metadata['eof']) && $metadata['eof']) {
                    if (is_resource($client)) {
                        @fclose($client);
                    }
                    unset(self::$clients[$key]);
                    $this->info("Client disconnected (EOF). Remaining clients: " . count(self::$clients));
                }
            } catch (\Exception $e) {
                // If we can't get metadata, the client is probably disconnected
                unset(self::$clients[$key]);
                $this->info("Client disconnected (Exception). Remaining clients: " . count(self::$clients));
            }
        }
    }

    private function acceptNewClients()
    {
        $newClient = @stream_socket_accept($this->serverSocket, 0);
        if ($newClient) {
            stream_set_blocking($newClient, false);
            self::$clients[] = $newClient;
            $this->info("New client connected: " . count(self::$clients));
        }
    }

    /**
     * Process messages from connected clients
     */
    private function processClientMessages()
    {
        foreach (self::$clients as $index => $clientInfo) {
            // Skip if no connection
            if (!isset($clientInfo['conn']) || !is_resource($clientInfo['conn'])) {
                continue;
            }

            $conn = $clientInfo['conn'];

            // Read data from the client
            $data = @fread($conn, 4096);

            // Skip if no data or connection closed
            if ($data === false || $data === '') {
                continue;
            }

            // Process the data
            $this->info("Received data: " . $data);

            // Try to parse the data as JSON
            try {
                // Extract JSON from the data (remove any framing characters)
                $jsonData = $this->extractJson($data);

                if ($jsonData) {
                    $request = json_decode($jsonData, true);

                    if ($request) {
                        // Extract the device serial number
                        $sn = $request['sn'] ?? null;

                        // Extract the AIMS value
                        $aims = $request['aims'] ?? 0;

                        // Extract the command
                        $command = $request['cmd'] ?? null;

                        $this->info("Received command: {$command} from device: {$sn} with AIMS: {$aims}");

                        // Update client info with SN if available
                        if ($sn) {
                            self::$clients[$index]['sn'] = $sn;
                            self::$clients[$index]['last_activity'] = time();
                        }

                        // Process the command
                        $response = $this->processCommand($request, $sn, $conn);

                        // Send the response
                        if ($response) {
                            $formattedResponse = json_encode($response);
                            $this->info("Sending response: " . $formattedResponse);
                            @fwrite($conn, $formattedResponse . "\n");
                        }
                    } else {
                        $this->error("Failed to decode JSON: " . $jsonData);
                    }
                }
            } catch (\Exception $e) {
                $this->error("Error processing client message: " . $e->getMessage());
            }
        }
    }

    private function oremoveDisconnectedClients()
    {
        foreach (self::$clients as $key => $client) {
            if (!is_resource($client) || stream_get_meta_data($client)['eof']) {
                fclose($client);
                unset(self::$clients[$key]);
                echo "Client $key disconnected and removed.\n";
            }
        }
    }

    private function processRequest($data, $conn)
    {
        try {
            // Trim protocol-specific markers
            $jsonData = trim($data, "#*");

            // Try to decode JSON
            $request = json_decode($jsonData, true);

            $this->info("Process request received from device:\n" . $jsonData);

            // Check if JSON is valid and has required fields
            if (!$request || !isset($request['cmd'])) {
                $this->error("Invalid request format: " . $jsonData);
                $response = ['error' => 'Invalid request'];
                @fwrite($conn, "#*" . json_encode($response) . "*#");
                return;
            }

            $command = $request['cmd'];

            // Special handling for heartbeat command which may not have SN
            if ($command === 'heart') {
                // For heartbeat, we need to find the device by connection
                $sn = null;
                foreach (self::$clients as $clientId => $clientInfo) {
                    if (isset($clientInfo['conn']) && $clientInfo['conn'] === $conn) {
                        $sn = $clientInfo['sn'] ?? null;
                        if ($sn) {
                            $this->info("Found SN {$sn} for heartbeat from existing connection");
                            break;
                        }
                    }
                }

                if ($sn) {
                    // Process heartbeat with the found SN
                    $response = $this->handleHeartbeat($request, $sn);

                    // Update device status in database
                    try {
                        $device = \App\Models\Device::where('sn', $sn)->first();
                        if ($device) {
                            $device->update([
                                'status' => 1, // Online
                                'last_heartbeat_at' => now()
                            ]);
                        }
                    } catch (\Exception $e) {
                        $this->error("Error updating device status: " . $e->getMessage());
                    }

                    // Log the heartbeat
                    try {
                        \App\Models\CommunicationLog::create([
                            'device_sn' => $sn,
                            'command' => 'heart',
                            'request_data' => $request,
                            'response_data' => $response
                        ]);
                    } catch (\Exception $e) {
                        $this->error("Failed to save communication log: " . $e->getMessage());
                    }

                    // Send response
                    try {
                        @fwrite($conn, "#*" . json_encode($response) . "*#");
                    } catch (\Exception $e) {
                        $this->error("Error sending heartbeat response: " . $e->getMessage());
                    }

                    return $response;
                } else {
                    // We couldn't find the device SN for this heartbeat
                    $this->warn("Received heartbeat from unknown device connection");
                    $response = [
                        'cmd' => 'heart',
                        'data' => [
                            'msgack' => $request['data']['msg'] ?? rand(10000, 99999),
                            'int' => 45
                        ]
                    ];

                    try {
                        @fwrite($conn, "#*" . json_encode($response) . "*#");
                    } catch (\Exception $e) {
                        $this->error("Error sending heartbeat response: " . $e->getMessage());
                    }

                    return $response;
                }
            }

            // For all other commands, SN is required
            if (!isset($request['sn'])) {
                $this->error("Missing SN in request: " . $jsonData);
                $response = ['error' => 'Missing device SN'];
                @fwrite($conn, "#*" . json_encode($response) . "*#");
                return;
            }

            $sn = $request['sn'];

            // FIXED: Improved device connection storage
            // First, check if we already have this device SN as a key
            if (isset(self::$clients[$sn])) {
                // Update the connection for this device
                self::$clients[$sn]['conn'] = $conn;
                self::$clients[$sn]['last_activity'] = time();
                $this->info("Updated existing connection for device: {$sn}");
            } else {
                // Check if this connection is already stored with a numeric key
                $foundNumericKey = false;
                foreach (self::$clients as $key => $clientInfo) {
                    if (is_numeric($key) && is_array($clientInfo) && isset($clientInfo['conn']) && $clientInfo['conn'] === $conn) {
                        // Update this entry with the SN
                        self::$clients[$key]['sn'] = $sn;
                        self::$clients[$key]['last_activity'] = time();
                        $foundNumericKey = true;
                        $this->info("Updated numeric key entry with SN: {$sn}");
                        break;
                    }
                }

                // If not found with numeric key, check if it's stored with a different SN
                if (!$foundNumericKey) {
                    $foundWithDifferentSn = false;
                    foreach (self::$clients as $key => $clientInfo) {
                        if (!is_numeric($key) && is_array($clientInfo) && isset($clientInfo['conn']) && $clientInfo['conn'] === $conn) {
                            // This connection was previously associated with a different SN
                            // Remove the old entry
                            unset(self::$clients[$key]);
                            $foundWithDifferentSn = true;
                            $this->info("Removed connection previously associated with SN: {$key}");
                            break;
                        }
                    }

                    // Create a new entry with the SN as the key
                    self::$clients[$sn] = [
                        'conn' => $conn,
                        'sn' => $sn,
                        'connected_at' => time(),
                        'last_activity' => time()
                    ];

                    $this->info("Created new connection entry for device: {$sn}");
                }
            }

            $response = [];

            // Check if this is a rent response from the device (has slot data)
            if ($command === 'rent' && isset($request['data']['d']) && is_array($request['data']['d'])) {
                $this->info("Received rent response with slot data from device: {$sn}");
                $response = $this->handleRentResponse($request, $sn);
            } else {
                // Process other commands as before
                switch ($command) {
                    case 'login':
                        $response = $this->handleLogin($request, $sn);
                        break;
                    case 'detail':
                        $response = $this->handleDetailQuery($request, $sn);
                        break;
                    case 'rent':
                        $response = $this->handleRent($request, $sn);
                        break;
                    case 'return':
                        $response = $this->handleReturn($request, $sn);
                        break;
                    case 'force':
                        $response = $this->handleForcePopOut($request, $sn);
                        break;
                    case 'reboot':
                        $response = $this->handleReboot($request, $sn);
                        break;
                    case 'vol':
                        $response = $this->handleVolumeControl($request, $sn);
                        break;
                    case 'detailup':
                        $response = $this->handleDetailup($request, $sn);
                        break;
                    case 'list_flash':
                        $response = $this->handleVoiceRefresh($request, $sn);
                        break;
                    default:
                        $response = ['error' => 'Unknown command'];
                }
            }

            // Update device status in database
            try {
                $device = \App\Models\Device::where('sn', $sn)->first();
                if ($device) {
                    $device->update([
                        'status' => 1, // Online
                        'last_heartbeat_at' => now()
                    ]);
                    $this->info("Updated device status for {$sn} to online");
                }
            } catch (\Exception $e) {
                $this->error("Error updating device status: " . $e->getMessage());
            }

            // Save log in database
            try {
                \App\Models\CommunicationLog::create([
                    'device_sn' => $sn,
                    'command' => $command,
                    'request_data' => $request,
                    'response_data' => $response
                ]);
            } catch (\Exception $e) {
                $this->error("Failed to save communication log: " . $e->getMessage());
            }

            // Send response with error handling
            try {
                @fwrite($conn, "#*" . json_encode($response) . "*#");
            } catch (\Exception $e) {
                $this->error("Error sending response to client: " . $e->getMessage());
            }

            return $response;
        } catch (\Exception $e) {
            $this->error("Error processing request: " . $e->getMessage());
            return null;
        }
    }

    public function processPendingCommands_old($conn = null)
    {
        $this->info('Processing pending device commands...');

        try {
            // Get all pending commands ordered by creation date
            $pendingCommands = \App\Models\DeviceCommand::where('status', 'pending')
                ->orderBy('created_at')
                ->get();

            if ($pendingCommands->isEmpty()) {
                $this->info("No pending commands to process");
                return 0;
            }

            foreach ($pendingCommands as $command) {
                // Check if rent command is expired (older than 45 seconds)
                if ($command->command === 'rent') {
                    $createdAt = new \DateTime($command->created_at);
                    $now = new \DateTime();
                    $interval = $createdAt->diff($now);
                    $secondsElapsed = $interval->s + ($interval->i * 60) + ($interval->h * 3600);

                    if ($secondsElapsed > 45) {
                        $this->warn("Rent command ID: {$command->id} for device: {$command->device_sn} expired (age: {$secondsElapsed}s)");

                        // Update command status to expired
                        $command->update([
                            'status' => 'expired',
                            'executed_at' => now()
                        ]);

                        // Log the expired command
                        \Illuminate\Support\Facades\Log::channel('device_logs')->info('Rent command expired', [
                            'command_id' => $command->id,
                            'device_sn' => $command->device_sn,
                            'age' => $secondsElapsed,
                            'created_at' => $command->created_at
                        ]);

                        continue; // Skip to next command
                    }
                }

                // If a specific connection is provided, only process commands for that device
                if ($conn) {
                    // Find the device SN for this connection
                    $deviceSn = null;
                    foreach (self::$clients as $sn => $client) {
                        if ($client === $conn && !is_numeric($sn)) {
                            $deviceSn = $sn;
                            break;
                        }
                    }

                    // Skip if this command is not for the current device
                    if ($deviceSn && $command->device_sn !== $deviceSn) {
                        continue;
                    }
                } else {
                    // If no specific connection, find the connection for this device
                    $conn = self::$clients[$command->device_sn] ?? null;

                    // Skip if device is not connected
                    if (!$conn || !is_resource($conn)) {
                        continue;
                    }
                }

                $this->info("Processing command ID: {$command->id} for device: {$command->device_sn}");

                try {
                    // Send the command to the device
                    $success = @fwrite($conn, $command->raw_command);

                    if ($success) {
                        // Update command status to sent
                        $command->update([
                            'status' => 'sent',
                            'executed_at' => now()
                        ]);

                        $this->info("Command sent successfully");

                        // Add a small delay between commands to avoid flooding the device
                        usleep(500000); // 0.5 second delay
                    } else {
                        // Mark as failed if sending was unsuccessful
                        $command->update(['status' => 'failed']);
                        $this->error("Failed to send command");
                    }
                } catch (\Exception $e) {
                    // Log the error and mark command as failed
                    $command->update(['status' => 'failed']);
                    $this->error("Error processing command: " . $e->getMessage());
                    \Illuminate\Support\Facades\Log::error("Error processing command ID {$command->id}: " . $e->getMessage());
                }
            }

            $this->info('Finished processing pending commands');
            return 0;
        } catch (\Exception $e) {
            $this->error("Error in processPendingCommands: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Process pending commands with improved rate limiting
     */
    public function processPendingCommands()
    {
        $this->info('Processing pending device commands...');

        try {
            // Get all pending commands ordered by creation date
            // Limit to a reasonable batch size to prevent overload
            $pendingCommands = \App\Models\DeviceCommand::where('status', 'pending')
                ->orderBy('created_at')
                ->limit(5) // Process max 5 commands per cycle to prevent flooding
                ->get();

            if ($pendingCommands->isEmpty()) {
                $this->info("No pending commands to process");
                return 0;
            }

            $this->info("Found " . $pendingCommands->count() . " pending commands");

            // Track processed devices to avoid sending multiple commands to the same device in one cycle
            $processedDevices = [];

            // Track last command time per device to enforce minimum delay
            $lastCommandTime = [];

            foreach ($pendingCommands as $command) {
                // Check if command is too old (expired)
                $secondsElapsed = now()->diffInSeconds($command->created_at);

                if ($secondsElapsed > 45) {
                    $this->warn("Command ID: {$command->id} for device: {$command->device_sn} expired (age: {$secondsElapsed}s)");

                    // Update command status to expired
                    $command->update([
                        'status' => 'expired',
                        'executed_at' => now()
                        // Removed failure_reason field
                    ]);

                    // Log the failure reason instead
                    \Illuminate\Support\Facades\Log::error("Command failed - no active connection", [
                        'command_id' => $command->id,
                        'device_sn' => $command->device_sn
                    ]);

                    continue; // Skip to next command
                }

                $deviceSn = $command->device_sn;

                // Check if we need to enforce a minimum delay between commands to the same device
                if (isset($lastCommandTime[$deviceSn])) {
                    $timeSinceLastCommand = microtime(true) - $lastCommandTime[$deviceSn];
                    $minimumDelay = 2.0; // 2 seconds minimum delay between commands to same device

                    if ($timeSinceLastCommand < $minimumDelay) {
                        $this->info("Enforcing minimum delay for device {$deviceSn}. Only {$timeSinceLastCommand}s since last command, need {$minimumDelay}s");

                        // Skip this command for now, it will be processed in the next cycle
                        continue;
                    }
                }

                // Skip if we already processed a command for this device in this cycle
                if (in_array($deviceSn, $processedDevices)) {
                    $this->info("Skipping additional command for device {$deviceSn} to prevent flooding");
                    continue;
                }

                // Find the connection for this device
                $targetConn = null;
                foreach (self::$clients as $clientId => $clientInfo) {
                    if (isset($clientInfo['sn']) && $clientInfo['sn'] === $deviceSn) {
                        $targetConn = $clientInfo['conn'];
                        break;
                    }
                }

                if (!$targetConn) {
                    $this->warn("No active connection found for device: {$deviceSn}");

                    // Don't mark as failed immediately, give it a chance to connect
                    if ($secondsElapsed > 30) {
                        $command->update([
                            'status' => 'failed',
                            'executed_at' => now()
                            // Removed failure_reason field
                        ]);
                    }

                    continue;
                }

                // Check if connection is valid
                if (!is_resource($targetConn) || feof($targetConn)) {
                    $this->error("Connection for device {$deviceSn} is not valid, marking command as failed");

                    $command->update([
                        'status' => 'failed',
                        'executed_at' => now()
                        // Removed failure_reason field
                    ]);

                    // Log the failure reason instead
                    \Illuminate\Support\Facades\Log::error("Command failed - invalid connection", [
                        'command_id' => $command->id,
                        'device_sn' => $deviceSn
                    ]);

                    continue;
                }

                $this->info("Processing command ID: {$command->id} for device: {$deviceSn}");

                try {
                    // Ensure the command has the proper format with protocol markers
                    $rawCommand = $command->raw_command;
                    if (strpos($rawCommand, '#*') !== 0) {
                        $rawCommand = "#*" . $rawCommand;
                    }
                    if (substr($rawCommand, -2) !== '*#') {
                        $rawCommand .= "*#";
                    }

                    // IMPORTANT: Add a pre-send delay to ensure device is ready
                    $this->info("Adding pre-send delay of 1 second for device stability");
                    sleep(1);

                    // Use non-blocking mode for writes to prevent hanging
                    stream_set_blocking($targetConn, false);

                    // Send the command with a timeout
                    $writeResult = $this->writeWithTimeout($targetConn, $rawCommand, 3);

                    if ($writeResult) {
                        // Update command status to sent
                        $command->update([
                            'status' => 'sent',
                            'executed_at' => now()
                        ]);

                        $this->info("Command sent successfully to device: {$deviceSn}");

                        // Add this device to processed list
                        $processedDevices[] = $deviceSn;

                        // Record the time this command was sent
                        $lastCommandTime[$deviceSn] = microtime(true);

                        // IMPORTANT: Add a post-send delay to allow device to process
                        $this->info("Adding post-send delay of 2 seconds to prevent device overload");
                        sleep(2);
                    } else {
                        // Mark as failed if sending was unsuccessful
                        $command->update([
                            'status' => 'failed',
                            'executed_at' => now()
                            // Removed failure_reason field
                        ]);

                        // Log the failure reason instead
                        \Illuminate\Support\Facades\Log::error("Command failed - write failed or timed out", [
                            'command_id' => $command->id,
                            'device_sn' => $deviceSn
                        ]);

                        $this->error("Failed to send command to device: {$deviceSn}");
                    }
                } catch (\Exception $e) {
                    $this->error("Error sending command to device {$deviceSn}: " . $e->getMessage());

                    $command->update([
                        'status' => 'failed',
                        'executed_at' => now()
                        // Removed failure_reason field
                    ]);

                    // Log the exception instead
                    \Illuminate\Support\Facades\Log::error("Command failed - exception", [
                        'command_id' => $command->id,
                        'device_sn' => $deviceSn,
                        'exception' => $e->getMessage()
                    ]);
                }

                // Add a delay between processing different devices to reduce server load
                usleep(500000); // 0.5 second delay between different devices
            }

            return $pendingCommands->count();
        } catch (\Exception $e) {
            $this->error("Error processing pending commands: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Write data to a socket with timeout and rate limiting
     *
     * @param resource $socket The socket resource
     * @param string $data The data to write
     * @param int $timeout Timeout in seconds
     * @return bool True if write was successful, false otherwise
     */
    private function writeWithTimeout($socket, $data, $timeout = 3)
    {
        // Set socket to non-blocking mode
        $originalBlocking = stream_get_meta_data($socket)['blocked'] ?? true;
        stream_set_blocking($socket, false);

        // Prepare for select
        $write = [$socket];
        $read = $except = null;

        // Wait until the socket is ready for writing or timeout
        if (stream_select($read, $write, $except, $timeout, 0)) {
            // Try to write the data in smaller chunks to prevent buffer overflow
            $dataLength = strlen($data);
            $chunkSize = 512; // Send data in 512-byte chunks
            $bytesWritten = 0;

            for ($i = 0; $i < $dataLength; $i += $chunkSize) {
                $chunk = substr($data, $i, $chunkSize);
                $result = @fwrite($socket, $chunk);

                if ($result === false || $result === 0) {
                    // Write failed
                    stream_set_blocking($socket, $originalBlocking);
                    return false;
                }

                $bytesWritten += $result;

                // Add a small delay between chunks
                if ($i + $chunkSize < $dataLength) {
                    usleep(50000); // 50ms delay between chunks
                }
            }

            // Restore original blocking mode
            stream_set_blocking($socket, $originalBlocking);

            // Check if all bytes were written
            return ($bytesWritten === $dataLength);
        }

        // Restore original blocking mode
        stream_set_blocking($socket, $originalBlocking);

        // Timeout occurred
        return false;
    }


    /**
     * Send a command to a specific device with rate limiting
     *
     * @param string $sn Device serial number
     * @param array $command Command data
     * @return bool Success status
     */
    public static function sendCommandToDevice($sn, $command)
    {
        // Find the device connection
        $conn = null;
        foreach (self::$clients as $clientId => $clientInfo) {
            if (isset($clientInfo['sn']) && $clientInfo['sn'] === $sn) {
                $conn = $clientInfo['conn'];
                break;
            }
        }

        if (!$conn || !is_resource($conn)) {
            return false;
        }

        // Format the command
        $rawCommand = "#*" . json_encode($command) . "*#";

        // Add a delay before sending to ensure device is ready
        sleep(1);

        // Send the command
        try {
            // Use non-blocking mode
            stream_set_blocking($conn, false);

            // Send in smaller chunks
            $dataLength = strlen($rawCommand);
            $chunkSize = 512;
            $bytesWritten = 0;

            for ($i = 0; $i < $dataLength; $i += $chunkSize) {
                $chunk = substr($rawCommand, $i, $chunkSize);
                $result = @fwrite($conn, $chunk);

                if ($result === false || $result === 0) {
                    return false;
                }

                $bytesWritten += $result;

                // Add a small delay between chunks
                if ($i + $chunkSize < $dataLength) {
                    usleep(50000); // 50ms delay between chunks
                }
            }

            // Add a delay after sending to allow device to process
            sleep(1);

            return ($bytesWritten === $dataLength);
        } catch (\Exception $e) {
            return false;
        }
    }

    // Handle the login command
    private function handleLogin($request, $sn)
    {
        // Log the login request
        $this->info("Device login request received from SN: {$sn}");

        // Extract device data from the request
        $deviceData = $request['data'] ?? [];

        // Prepare device attributes
        $deviceAttributes = [
            'status' => 1, // Online
            'last_login' => now(),
            'last_heartbeat_at' => now(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
        ];

        // Add additional device data if available
        if (!empty($deviceData)) {
            $deviceAttributes = array_merge($deviceAttributes, [
                'manufacturer' => $deviceData['mft'] ?? null,
                'model' => $deviceData['mod'] ?? null,
                'device_type' => $deviceData['type'] ?? null, // Use device_type consistently
                'slot_count' => $deviceData['num'] ?? null,
                'hardware_version' => $deviceData['hd'] ?? null,
                'firmware_version' => $deviceData['fw'] ?? null,
                'ccid' => $deviceData['ccid'] ?? null,
                'imei' => $deviceData['imei'] ?? null,
                'apn' => $deviceData['apn'] ?? null,
                'volume' => $deviceData['vol'] ?? null,
            ]);
        }

        // Check if device exists in database
        $device = \App\Models\Device::where('sn', $sn)->first();

        if ($device) {
            // Update existing device
            $this->info("Updating existing device: {$sn}");
            $device->update($deviceAttributes);
        } else {
            // Create new device with a default name based on SN
            $this->info("Creating new device with SN: {$sn}");
            $deviceAttributes['sn'] = $sn;
            $deviceAttributes['name'] = "Device {$sn}";

            $device = \App\Models\Device::create($deviceAttributes);
        }

        // Log successful login
        Log::info("Device login successful", [
            'device_sn' => $sn,
            'device_type' => $deviceData['type'] ?? 'unknown',
            'firmware' => $deviceData['fw'] ?? 'unknown',
            'timestamp' => now()->toDateTimeString()
        ]);

        // Send a heartbeat immediately after login
        $this->sendHeartbeatToDevice($sn);

        // Return standard login response
        return [
            'cmd' => 'login',
            'data' => [
                'r' => 0, // Success
                'msgack' => $request['msg'],
                'heart' => 45
            ]
        ];
    }

    /**
     * Handle the heartbeat command from a device
     *
     * @param array $request The request data
     * @param string $sn The device serial number
     * @return array The response data
     */
    private function handleHeartbeat($request, $sn)
    {
        $this->info("Received heartbeat from device: {$sn}");

        try {
            // Extract signal strength if available
            $signalStrength = null;
            if (isset($request['data']) && isset($request['data']['csq'])) {
                $signalStrength = $request['data']['csq'];
            } elseif (isset($request['csq'])) {
                $signalStrength = $request['csq'];
            }

            // Update device in database
            $device = \App\Models\Device::where('sn', $sn)->first();
            if ($device) {
                $updateData = [
                    'status' => 1, // Online
                    'last_heartbeat_at' => now()
                ];

                if ($signalStrength !== null) {
                    $updateData['signal_strength'] = $signalStrength;
                }

                $device->update($updateData);
            } else {
                // Create new device record if not found
                $newDeviceData = [
                    'sn' => $sn,
                    'name' => "Device {$sn}",
                    'status' => 1, // Online
                    'last_heartbeat_at' => now()
                ];

                if ($signalStrength !== null) {
                    $newDeviceData['signal_strength'] = $signalStrength;
                }

                \App\Models\Device::create($newDeviceData);
            }

            // Get message ID from request if available
            $msgId = null;
            if (isset($request['msg'])) {
                $msgId = $request['msg'];
            } elseif (isset($request['data']) && isset($request['data']['msg'])) {
                $msgId = $request['data']['msg'];
            }

            // If no message ID found, generate a random one
            if ($msgId === null) {
                $msgId = rand(10000, 99999);
            }

            // Return standard heartbeat response
            return [
                'cmd' => 'heart',
                'data' => [
                    'msgack' => $msgId,
                    'int' => 45 // Heartbeat interval in seconds
                ]
            ];
        } catch (\Exception $e) {
            $this->error("Error processing heartbeat: " . $e->getMessage());

            // Return a standard response even if there was an error
            return [
                'cmd' => 'heart',
                'data' => [
                    'msgack' => rand(10000, 99999),
                    'int' => 45
                ]
            ];
        }
    }

    private function handleDetailQuery($request, $sn)
    {
        return [
            'cmd' => 'detail',
            'data' => [
                'msgack' => $request['msg'],
                'st' => 2,
                'd' => [
                    ['n' => 1, 's' => 1, 'sn' => 'zd190421002', 'e' => 1],
                    ['n' => 2, 's' => 1, 'sn' => 'zd190421001', 'e' => 2]
                ]
            ]
        ];
    }

    /**
     * Handle the rent command
     *
     * @param array $request The request data
     * @param string $sn The device serial number
     * @return array The response data
     */
    private function handleRent($request, $sn)
    {
        $this->info("Handling rent command for device: {$sn}");

        // Extract the AIMS value from the request
        $aims = $request['aims'] ?? 0;
        $messageId = $request['msg'] ?? $request['data']['msg'] ?? rand(10000, 99999);
        $slotNumber = $request['data']['n'] ?? 0;

        // Calculate the actual slot number based on AIMS
        $actualSlotNumber = $slotNumber + ($aims * 12);

        $this->info("Rent command for slot {$slotNumber} (actual: {$actualSlotNumber}) with AIMS {$aims}");

        try {
            // Get the device from database
            $device = \App\Models\Device::where('sn', $sn)->first();

            if (!$device) {
                $this->error("Device with SN {$sn} not found in database");
                return [
                    'cmd' => 'rent',
                    'data' => [
                        'msgack' => $messageId,
                        'n' => $slotNumber
                    ]
                ];
            }

            // Find the powerbank in the specified slot
            $powerbank = \App\Models\Powerbank::where('device_id', $device->id)
                ->where('slot_number', $actualSlotNumber)
                ->where('aims', $aims)
                ->where('status', 'available')
                ->first();

            if ($powerbank) {
                $this->info("Found powerbank {$powerbank->serial_number} in slot {$actualSlotNumber}");

                // Debug the powerbank before update
                $this->info("Before update - Powerbank ID: {$powerbank->id}, Slot: {$powerbank->slot_number}, Status: {$powerbank->status}");

                // Update powerbank status using direct DB query to bypass model casting
                \Illuminate\Support\Facades\DB::table('powerbanks')
                    ->where('id', $powerbank->id)
                    ->update([
                        'status' => 'rented',
                        'slot_number' => null
                    ]);

                // Refresh the model to see the changes
                $powerbank->refresh();

                // Debug the powerbank after update
                $this->info("After update - Powerbank ID: {$powerbank->id}, Slot: " .
                    (is_null($powerbank->slot_number) ? "NULL" : $powerbank->slot_number) .
                    ", Status: {$powerbank->status}");

                // Create rental record
                \App\Models\PowerbankRental::create([
                    'powerbank_id' => $powerbank->id,
                    'device_id' => $device->id,
                    'customer_id' => 'CUSTOMER1', // Default customer ID
                    'rented_at' => now(),
                    'initial_charge' => $powerbank->current_charge,
                    'slot_number' => $actualSlotNumber, // Store the original slot number in the rental record
                    'aims' => $aims
                ]);

                $this->info("Created rental record for powerbank {$powerbank->serial_number} from slot {$actualSlotNumber}");
            } else {
                $this->warn("No available powerbank found in slot {$actualSlotNumber}");
            }

            // Update the command status in the database
            \App\Models\DeviceCommand::where('device_sn', $sn)
                ->where('command', 'rent')
                ->where('message_id', $messageId)
                ->update([
                    'status' => 'completed',
                    'executed_at' => now(),
                    'response' => json_encode([
                        'cmd' => 'rent',
                        'data' => [
                            'msgack' => $messageId,
                            'n' => $slotNumber
                        ]
                    ])
                ]);
        } catch (\Exception $e) {
            $this->error("Error processing rent command: " . $e->getMessage());
        }

        // Return standard rent response
        return [
            'cmd' => 'rent',
            'data' => [
                'msgack' => $messageId,
                'n' => $slotNumber
            ]
        ];
    }

    private function handleForce($request, $sn)
    {
        return [
            'cmd' => 'force',
            'sn' => $sn,
            'data' => [
                //'r' => 0, // Success
                //'sn'=>'LC2306233899',
                'msgack' => $request['msg'],
                'n' => 0 //$request['data']['n']
            ]
        ];
    }


    /**
     * Handle the detailup command from a device
     *
     * @param array $request The request data
     * @param string $sn The device serial number
     * @return array The response data
     */
    private function handleDetailup($request, $sn)
    {
        // Get the AIMS value from the request (default to 0 if not provided)
        $aims = $request['aims'] ?? 0;
        $this->info("Handling detailup command for device {$sn} with AIMS {$aims}");

        // Process the powerbank details from the request
        $this->processPowerbankDetails($request, $sn);

        // Return the standard response
        return [
            'cmd' => 'detailup',
            'data' => [
                'msgack' => $request['msg'],
            ]
        ];
    }

    /**
     * Process powerbank details from detailup command and save to database
     */
    private function processPowerbankDetails($request, $deviceSn)
    {
        // Get the device from the database
        $device = \App\Models\Device::where('sn', $deviceSn)->first();

        if (!$device) {
            $this->error("Device with SN {$deviceSn} not found in database");
            return;
        }

        $this->info("Processing powerbank details for device {$deviceSn}");

        // Get the AIMS value from the request (default to 0 if not provided)
        $aims = $request['aims'] ?? 0;
        $this->info("AIMS value: {$aims}");

        // Get the slot details from the request
        $slots = $request['data']['d'] ?? [];

        if (empty($slots)) {
            $this->info("No slot data found in request");
            return;
        }

        // Process each slot
        foreach ($slots as $slot) {
            $slotNumber = $slot['n'] ?? null;
            $status = $slot['s'] ?? null;
            $serialNumber = $slot['sn'] ?? null;
            $chargeCode = $slot['e'] ?? null;

            // Skip if no slot number
            if (!$slotNumber) {
                continue;
            }

            $this->info("Processing slot {$slotNumber}: status={$status}, SN={$serialNumber}, chargeCode={$chargeCode}");

            // Calculate the actual slot number based on AIMS
            // AIMS 0 = slots 1-12, AIMS 1 = slots 13-24, AIMS 2 = slots 25-36, etc.
            $actualSlotNumber = $slotNumber + ($aims * 12);
            $this->info("Actual slot number: {$actualSlotNumber} (AIMS: {$aims})");

            // Skip empty slots or slots without a serial number
            if ($status === 0 || empty($serialNumber)) {
                // Check if there was a powerbank in this slot and mark it as removed
                $existingPowerbank = \App\Models\Powerbank::where('device_id', $device->id)
                    ->where('slot_number', $actualSlotNumber)
                    ->where('aims', $aims)
                    ->first();

                if ($existingPowerbank) {
                    $existingPowerbank->update([
                        'slot_number' => null,
                        'status' => 'maintenance'
                    ]);

                    $this->warn("Powerbank {$existingPowerbank->serial_number} was in slot {$actualSlotNumber} but is now missing");
                }

                $this->info("Slot {$actualSlotNumber} is empty or has no serial number");
                continue;
            }

            // Check if powerbank exists
            $powerbank = \App\Models\Powerbank::where('serial_number', $serialNumber)->first();

            if ($powerbank) {
                // Update existing powerbank
                $this->info("Updating existing powerbank {$serialNumber}");

                $chargeLevel = $slot['e'] ?? null;
                $currentCharge = $chargeLevel ? min(($chargeLevel * 10) + 5, 100) : 100; // Convert to percentage (midpoint of range)

                $powerbank->update([
                    'device_id' => $device->id,
                    'slot_number' => $actualSlotNumber,
                    'aims' => $aims,
                    'status' => $this->mapStatusCode($status, $chargeCode),
                    'current_charge' => $currentCharge
                ]);
            } else {
                // Create new powerbank
                $this->info("Creating new powerbank {$serialNumber}");

                \App\Models\Powerbank::create([
                    'serial_number' => $serialNumber,
                    'device_id' => $device->id,
                    'capacity' => 5000, // Default capacity
                    'current_charge' => $currentCharge,
                    'slot_number' => $actualSlotNumber,
                    'aims' => $aims,
                    'status' => $this->mapStatusCode($status, $chargeCode),
                    'charge_cycles' => 0,
                    'health' => 100, // Add default health value
                ]);
            }
        }
    }

    /**
     * Process powerbank details from detailup command and save to database
     */
    private function _processPowerbankDetails($request, $deviceSn)
    {
        // Get the device from the database
        $device = \App\Models\Device::where('sn', $deviceSn)->first();

        if (!$device) {
            $this->error("Device with SN {$deviceSn} not found in database");
            return;
        }

        $this->info("Processing powerbank details for device {$deviceSn}");

        // Get the AIMS value from the request (default to 0 if not provided)
        $aims = $request['aims'] ?? 0;
        $this->info("AIMS value: {$aims}");

        // Get the slot details from the request
        $slots = $request['data']['d'] ?? [];

        if (empty($slots)) {
            $this->info("No slot data found in request");
            return;
        }

        // Process each slot
        foreach ($slots as $slot) {
            $slotNumber = $slot['n'] ?? null;
            $status = $slot['s'] ?? null;
            $serialNumber = $slot['sn'] ?? null;
            $chargeCode = $slot['e'] ?? null;

            // Skip if no slot number
            if (!$slotNumber) {
                continue;
            }

            $this->info("Processing slot {$slotNumber}: status={$status}, SN={$serialNumber}, chargeCode={$chargeCode}");

            // Calculate the actual slot number based on AIMS
            // AIMS 0 = slots 1-12, AIMS 1 = slots 13-24, AIMS 2 = slots 25-36, etc.
            $actualSlotNumber = $slotNumber + ($aims * 12);
            $this->info("Actual slot number: {$actualSlotNumber} (AIMS: {$aims})");

            // Skip empty slots or slots without a serial number
            if ($status === 0 || empty($serialNumber)) {
                // Check if there was a powerbank in this slot and mark it as removed
                $existingPowerbank = \App\Models\Powerbank::where('device_id', $device->id)
                    ->where('slot_number', $actualSlotNumber)
                    ->where('aims', $aims)
                    ->first();

                if ($existingPowerbank) {
                    $existingPowerbank->update([
                        'slot_number' => null,
                        'status' => 'maintenance'
                    ]);

                    $this->warn("Powerbank {$existingPowerbank->serial_number} was in slot {$actualSlotNumber} but is now missing");
                }

                $this->info("Slot {$actualSlotNumber} is empty or has no serial number");
                continue;
            }

            // Check if powerbank exists
            $powerbank = \App\Models\Powerbank::where('serial_number', $serialNumber)->first();

            if ($powerbank) {
                // Update existing powerbank
                $this->info("Updating existing powerbank {$serialNumber}");

                $powerbank->update([
                    'device_id' => $device->id,
                    'slot_number' => $actualSlotNumber,
                    'aims' => $aims,
                    'status' => $this->mapStatusCode($status, $chargeCode),
                ]);
            } else {
                // Create new powerbank
                $this->info("Creating new powerbank {$serialNumber}");

                \App\Models\Powerbank::create([
                    'serial_number' => $serialNumber,
                    'device_id' => $device->id,
                    'capacity' => 5000, // Default capacity
                    'current_charge' => 100, // Default charge
                    'slot_number' => $actualSlotNumber,
                    'aims' => $aims,
                    'status' => $this->mapStatusCode($status, $chargeCode),
                    'charge_cycles' => 0,
                ]);
            }
        }

        // Log the successful processing
        $this->info("Successfully processed powerbank details for device {$deviceSn}");
    }

    /**
     * Map status code from device to powerbank status
     */
    private function mapStatusCode($status, $chargeLevel)
    {
        // Status 1 means the slot has a powerbank
        if ($status === 1) {
            // Convert charge level to percentage
            $currentCharge = $chargeLevel ? min(($chargeLevel * 10) + 5, 100) : 100;

            // If charge is less than 20%, mark as charging
            if ($currentCharge < 20) {
                return 'charging';
            }

            // Otherwise, it's available
            return 'available';
        }

        // Default to available
        return 'available';
    }

    /**
     * Handle the return command
     *
     * @param array $request The request data
     * @param string $sn The device serial number
     * @return array The response data
     */
    private function handleReturn($request, $sn)
    {
        $this->info("Handling return command for device: {$sn}");

        // Extract data from the request
        $messageId = $request['data']['msg'] ?? $request['msg'] ?? rand(10000, 99999);
        $slotNumber = $request['data']['n'] ?? 0;
        $aims = $request['aims'] ?? 0;
        $powerbankSn = $request['data']['sn'] ?? null;
        $chargeLevel = $request['data']['e'] ?? null;
        $currentCharge = $chargeLevel ? min(($chargeLevel * 10) + 5, 100) : 100; // Convert to percentage (midpoint of range)
        $slotData = $request['data']['d'] ?? [];
        $slotStatus = $request['data']['st'] ?? 0;

        // Calculate the actual slot number based on AIMS
        $actualSlotNumber = $slotNumber + ($aims * 12);

        $this->info("Return command for slot {$slotNumber} (actual: {$actualSlotNumber}) with AIMS {$aims}, powerbank SN: {$powerbankSn}, charge level: {$chargeLevel} ({$currentCharge}%)");

        try {
            // Get the device from database
            $device = \App\Models\Device::where('sn', $sn)->first();

            if (!$device) {
                $this->error("Device with SN {$sn} not found in database");
                return [
                    'cmd' => 'return',
                    'sn' => $sn,
                    'data' => [
                        'r' => 1, // Error
                        'n' => $slotNumber
                    ],
                    'aims' => $aims
                ];
            }

            // Find the powerbank by serial number
            $powerbank = \App\Models\Powerbank::where('serial_number', $powerbankSn)->first();

            if (!$powerbank) {
                // Create new powerbank if it doesn't exist
                $this->info("Creating new powerbank record for SN: {$powerbankSn}");

                $powerbank = \App\Models\Powerbank::create([
                    'serial_number' => $powerbankSn,
                    'device_id' => $device->id,
                    'capacity' => 5000, // Default capacity
                    'current_charge' => $currentCharge,
                    'slot_number' => $actualSlotNumber,
                    'aims' => $aims,
                    'status' => $currentCharge < 20 ? 'charging' : 'available',
                    'charge_cycles' => 0,
                    'health' => 100
                ]);
            } else {
                // Determine the status based on charge level
                $status = $currentCharge < 20 ? 'charging' : 'available';

                // Update existing powerbank
                $powerbank->update([
                    'device_id' => $device->id,
                    'slot_number' => $actualSlotNumber,
                    'aims' => $aims,
                    'status' => $status,
                    'current_charge' => $currentCharge,
                    'charge_cycles' => $powerbank->charge_cycles + 1
                ]);
            }

            // Find and close any open rental for this powerbank
            $rental = \App\Models\PowerbankRental::where('powerbank_id', $powerbank->id)
                ->whereNull('returned_at')
                ->latest('rented_at')
                ->first();

            if ($rental) {
                $this->info("Closing rental record for powerbank {$powerbank->serial_number}");

                // Calculate rental duration and fee
                $rentedAt = new \Carbon\Carbon($rental->rented_at);
                $now = now();
                $hours = $rentedAt->diffInHours($now) + 1; // Minimum 1 hour
                $rentalFee = $hours * 2.00; // $2 per hour

                $rental->update([
                    'returned_at' => $now,
                    'return_device_id' => $device->id,
                    'return_slot_number' => $actualSlotNumber,
                    'return_aims' => $aims,
                    'return_charge' => $currentCharge,
                    'rental_fee' => $rentalFee
                ]);

                $this->info("Updated rental record ID: {$rental->id} with return information");
                $this->info("Rental duration: {$hours} hours, Fee: \${$rentalFee}");
            } else {
                $this->warn("No active rental found for powerbank {$powerbankSn}");
            }

            // Process all slot data to update device inventory
            if (!empty($slotData)) {
                $this->processSlotData($device, $slotData, $aims);
            }

            // Update the command status in the database
            \App\Models\DeviceCommand::where('device_sn', $sn)
                ->where('command', 'return')
                ->where('message_id', $messageId)
                ->update([
                    'status' => 'completed',
                    'executed_at' => now(),
                    'response' => json_encode([
                        'cmd' => 'return',
                        'data' => [
                            'r' => 0,
                            'n' => $slotNumber
                        ],
                        'aims' => $aims
                    ])
                ]);
        } catch (\Exception $e) {
            $this->error("Error processing return command: " . $e->getMessage());
            $this->error($e->getTraceAsString());
        }

        // Return standard return response exactly matching the expected format
        return [
            'cmd' => 'return',
            'data' => [
                'r' => 0, // Success
                'n' => $slotNumber
            ],
            'aims' => $aims
        ];
    }

    /**
     * Process slot data from device
     *
     * @param \App\Models\Device $device The device
     * @param array $slotData The slot data
     * @param int $aims The AIMS value
     */
    private function processSlotData($device, $slotData, $aims = 0)
    {
        $this->info("Processing slot data for device {$device->sn} with AIMS {$aims}");

        foreach ($slotData as $slot) {
            $slotNumber = $slot['n'] ?? 0;
            $status = $slot['s'] ?? 0;
            $powerbankSn = $slot['sn'] ?? null;
            $chargeLevel = $slot['e'] ?? null;
            $currentCharge = $chargeLevel ? min(($chargeLevel * 10) + 5, 100) : 100; // Convert to percentage (midpoint of range)

            // Calculate actual slot number based on AIMS
            $actualSlotNumber = $slotNumber + ($aims * 12);

            $this->info("Slot {$slotNumber} (actual: {$actualSlotNumber}): Status {$status}, SN: {$powerbankSn}, Charge: {$currentCharge}%");

            if ($status == 1 && $powerbankSn) {
                // Occupied slot - find or create powerbank
                $powerbank = \App\Models\Powerbank::where('serial_number', $powerbankSn)->first();

                if (!$powerbank) {
                    // Create new powerbank record
                    $powerbank = \App\Models\Powerbank::create([
                        'serial_number' => $powerbankSn,
                        'device_id' => $device->id,
                        'slot_number' => $actualSlotNumber,
                        'aims' => $aims,
                        'status' => $currentCharge < 20 ? 'charging' : 'available',
                        'current_charge' => $currentCharge,
                        'health' => 100,
                        'charge_cycles' => 0
                    ]);

                    $this->info("Created new powerbank record: {$powerbankSn} in slot {$actualSlotNumber} with charge {$currentCharge}%");
                } else {
                    // Update existing powerbank
                    $powerbank->update([
                        'device_id' => $device->id,
                        'slot_number' => $actualSlotNumber,
                        'aims' => $aims,
                        'status' => $currentCharge < 20 ? 'charging' : 'available',
                        'current_charge' => $currentCharge
                    ]);

                    $this->info("Updated powerbank record: {$powerbankSn} to slot {$actualSlotNumber} with charge {$currentCharge}%");
                }
            } else if ($status == 0) {
                // Empty slot - check if any powerbank was previously in this slot
                $previousPowerbank = \App\Models\Powerbank::where('device_id', $device->id)
                    ->where('slot_number', $actualSlotNumber)
                    ->where('aims', $aims)
                    ->first();

                if ($previousPowerbank) {
                    $this->info("Slot {$actualSlotNumber} is now empty, previously had powerbank {$previousPowerbank->serial_number}");

                    // If the powerbank status is not 'rented', update its slot to null
                    if ($previousPowerbank->status !== 'rented') {
                        $previousPowerbank->update([
                            'slot_number' => null,
                            'status' => 'missing'
                        ]);

                        $this->warn("Powerbank {$previousPowerbank->serial_number} marked as missing");
                    }
                }
            }
        }
    }

    /**
     * Handle the force pop out command response from a device
     *
     * @param array $request The request data
     * @param string $sn The device serial number
     * @return array The response data
     */
    private function handleForcePopOut($request, $sn)
    {
        $this->info("Handling force pop out response from device: {$sn}");

        // Extract the AIMS value from the request
        $aims = $request['aims'] ?? 0;

        // Check if this is a response with slot data
        if (isset($request['data']['d']) && is_array($request['data']['d'])) {
            $this->info("Received force response with slot data from device: {$sn} for AIMS: {$aims}");

            try {
                // Get the device from database
                $device = \App\Models\Device::where('sn', $sn)->first();

                if (!$device) {
                    $this->error("Device with SN {$sn} not found in database");
                    return [
                        'cmd' => 'force',
                        'data' => [
                            'msgack' => $request['msg'] ?? 0,
                            'r' => 0 // Success
                        ]
                    ];
                }

                // Process the slot data
                $slotData = $request['data']['d'];
                $this->info("Processing " . count($slotData) . " slots for device {$sn} with AIMS {$aims}");

                foreach ($slotData as $slot) {
                    $slotNumber = $slot['n'];
                    $slotStatus = $slot['s']; // 0 = empty, 1 = occupied

                    // Calculate the actual slot number based on AIMS
                    $actualSlotNumber = $slotNumber + ($aims * 12);

                    $this->info("Slot {$actualSlotNumber} (AIMS {$aims}, physical slot {$slotNumber}) status: " .
                        ($slotStatus == 0 ? 'empty' : 'occupied'));

                    if ($slotStatus == 0) {
                        // Slot is empty, update any powerbank that was in this slot
                        $powerbank = \App\Models\Powerbank::where('device_id', $device->id)
                            ->where('slot_number', $actualSlotNumber)
                            ->where('aims', $aims)
                            ->first();

                        if ($powerbank) {
                            $this->info("Updating powerbank {$powerbank->serial_number} status to maintenance and clearing slot");

                            // Update powerbank status to maintenance with explicit null for slot_number
                            \Illuminate\Support\Facades\DB::table('powerbanks')
                                ->where('id', $powerbank->id)
                                ->update([
                                    'status' => 'maintenance',
                                    'slot_number' => null
                                ]);
                        }
                    }
                }

                // Update the command status in the database
                \App\Models\DeviceCommand::where('device_sn', $sn)
                    ->where('command', 'force')
                    ->where('message_id', $request['msg'] ?? 0)
                    ->update([
                        'status' => 'completed',
                        'executed_at' => now(),
                        'response' => json_encode($request)
                    ]);

                $this->info("Force command processed successfully for device: {$sn}");
            } catch (\Exception $e) {
                $this->error("Error processing force command response: " . $e->getMessage());
            }
        }

        // Return standard force response
        return [
            'cmd' => 'force',
            'data' => [
                'msgack' => $request['msg'] ?? 0,
                'r' => 0 // Success
            ]
        ];
    }

    private function handleReboot($request, $sn)
    {
        return [
            "cmd" => "reboot",
            "sn" => $sn,
            "data" => [
                "dev" => 1
            ]
        ];
    }

    private function handleVolumeControl($request, $sn)
    {
        return [
            "data" => "volume",
            "sn" => $sn,
            [
                "r" => 1, //0 =query , 1 = set
                "n" => 100 // 0 to 100 volume
            ]
        ];
    }

    private function handleVoiceRefresh($request, $sn)
    {
        return [
            "cmd" => "list_flash",
            "sn" => $sn,
            "data" => [
                "url" => ""
            ]
        ];
    }

    public static function oldsendCommandToAll($command)
    {
        foreach (self::$clients as $client) {
            //if (is_resource($client)) {
            fwrite($client, "#*" . json_decode($command) . "*#");
            //}
        }
    }

    public function sendCommandToAll($command)
    {
        foreach (self::$clients as $client) {
            if (is_resource($client)) {
                fwrite($client, "#*" . json_encode($command) . "*#");
            }
        }
    }

    /**
     * Send heartbeats to all connected devices with simplified approach
     */
    private function sendHeartbeats()
    {
        $this->info("Sending heartbeats to all connected devices...");
        $currentTime = time();
        $heartbeatsSent = 0;
        $heartbeatsFailed = 0;

        foreach (self::$clients as $clientId => $clientInfo) {
            // Skip if not a valid client with SN
            if (!isset($clientInfo['sn']) || empty($clientInfo['sn']) || !isset($clientInfo['conn'])) {
                continue;
            }

            $conn = $clientInfo['conn'];
            $sn = $clientInfo['sn'];

            // Skip if last heartbeat was sent less than 30 seconds ago
            if (
                isset($clientInfo['last_heartbeat_sent']) &&
                ($currentTime - $clientInfo['last_heartbeat_sent'] < 30)
            ) {
                continue;
            }

            // Skip if connection is not valid
            if (!is_resource($conn) || feof($conn)) {
                $this->error("Invalid connection for device {$sn}");
                continue;
            }

            // Simple heartbeat message
            $heartbeatMessage = [
                'cmd' => 'heart',
                'data' => [
                    'msgack' => rand(10000, 99999),
                    'int' => 45
                ]
            ];

            // Format the message with protocol markers
            $formattedMessage = "#*" . json_encode($heartbeatMessage) . "*#";

            try {
                // Simple write with basic error handling
                $result = @fwrite($conn, $formattedMessage);

                if ($result !== false) {
                    $this->info("Heartbeat sent to device {$sn}");
                    $heartbeatsSent++;

                    // Update last heartbeat timestamp
                    self::$clients[$clientId]['last_heartbeat_sent'] = $currentTime;

                    // Update device heartbeat timestamp in database
                    try {
                        $device = \App\Models\Device::where('sn', $sn)->first();
                        if ($device) {
                            $device->update([
                                'last_heartbeat_at' => now(),
                                'status' => 1 // Online
                            ]);
                        }
                    } catch (\Exception $e) {
                        $this->error("Error updating device in database: " . $e->getMessage());
                    }
                } else {
                    $this->error("Failed to send heartbeat to device {$sn}");
                    $heartbeatsFailed++;
                }
            } catch (\Exception $e) {
                $this->error("Error sending heartbeat to device {$sn}: " . $e->getMessage());
                $heartbeatsFailed++;
            }
        }

        $this->info("Heartbeat summary: {$heartbeatsSent} sent, {$heartbeatsFailed} failed");
    }

    /**
     * Send a heartbeat to a specific device (simplified)
     */
    private function sendHeartbeatToDevice($sn)
    {
        // Find the client connection for this device
        $conn = null;
        foreach (self::$clients as $clientId => $clientInfo) {
            if (isset($clientInfo['sn']) && $clientInfo['sn'] === $sn && isset($clientInfo['conn'])) {
                $conn = $clientInfo['conn'];
                break;
            }
        }

        if (!$conn || !is_resource($conn)) {
            $this->error("Cannot send heartbeat to device {$sn}: Connection not found or invalid");
            return false;
        }

        // Simple heartbeat message
        $heartbeatMessage = [
            'cmd' => 'heart',
            'data' => [
                'msgack' => rand(10000, 99999),
                'int' => 45
            ]
        ];

        // Format the message with protocol markers
        $formattedMessage = "#*" . json_encode($heartbeatMessage) . "*#";

        $this->info("Sending heartbeat to device: {$sn}");

        try {
            // Simple write with basic error handling
            $result = @fwrite($conn, $formattedMessage);

            if ($result !== false) {
                $this->info("Heartbeat sent successfully to device {$sn}");
                return true;
            } else {
                $this->error("Failed to send heartbeat to device {$sn}");
                return false;
            }
        } catch (\Exception $e) {
            $this->error("Error sending heartbeat to device {$sn}: " . $e->getMessage());
            return false;
        }
    }


    /**
     * Check if a connection is stable
     *
     * @param resource $conn The connection resource
     * @param string $sn The device serial number
     * @return bool True if connection is stable, false otherwise
     */
    private function isConnectionStable($conn, $sn)
    {
        // Check if connection is still a resource
        if (!is_resource($conn)) {
            return false;
        }

        // Check if connection is at EOF
        if (feof($conn)) {
            return false;
        }

        // Try to write a small amount of data to check connection
        try {
            // Only do this for devices with SN to avoid unnecessary checks
            if ($sn && $sn !== "unknown") {
                $result = @fwrite($conn, " ", 1);
                if ($result === false || $result === 0) {
                    return false;
                }
            }
        } catch (\Exception $e) {
            return false;
        }

        return true;
    }
    /**
     * Clean up unstable connections
     */
    private function cleanupUnstableConnections()
    {
        $this->info("Checking for unstable connections...");
        $unstableCount = 0;

        foreach (self::$clients as $index => $clientInfo) {
            if (!is_array($clientInfo) || !isset($clientInfo['conn'])) {
                unset(self::$clients[$index]);
                $unstableCount++;
                continue;
            }

            $conn = $clientInfo['conn'];
            $sn = $clientInfo['sn'] ?? "unknown";

            if (!is_resource($conn) || !$this->isConnectionStable($conn, $sn)) {
                $this->warn("Removing unstable connection for device: {$sn}");

                if (is_resource($conn)) {
                    @fclose($conn);
                }

                unset(self::$clients[$index]);
                $unstableCount++;

                // If this is a device with SN, update its status in the database
                if ($sn && $sn !== "unknown") {
                    try {
                        $device = \App\Models\Device::where('sn', $sn)->first();
                        if ($device) {
                            $device->update(['status' => 0]); // Mark as offline
                            $this->info("Updated device {$sn} status to offline");
                        }
                    } catch (\Exception $e) {
                        $this->error("Error updating device status: " . $e->getMessage());
                    }
                }
            }
        }

        $this->info("Connection cleanup complete. Removed {$unstableCount} unstable connections.");
        $this->info("Remaining connections: " . count(self::$clients));
    }

    /**
     * Clean up inactive connections
     */
    private function cleanupInactiveConnections()
    {
        $inactiveTimeout = 300; // 5 minutes
        $currentTime = time();
        $removed = 0;

        foreach (self::$clients as $clientId => $clientInfo) {
            if (!isset($clientInfo['last_activity'])) {
                continue;
            }

            $conn = $clientInfo['conn'];
            $lastActivity = $clientInfo['last_activity'];
            $sn = $clientInfo['sn'] ?? 'unknown';

            // Check if connection is inactive
            if (($currentTime - $lastActivity) > $inactiveTimeout) {
                $this->warn("Removing inactive connection for device {$sn} (inactive for " .
                    ($currentTime - $lastActivity) . " seconds)");

                // Close the connection
                if (is_resource($conn)) {
                    @fclose($conn);
                }

                // Remove from clients array
                unset(self::$clients[$clientId]);
                $removed++;

                // Update device status in database
                try {
                    if ($sn !== 'unknown') {
                        $device = \App\Models\Device::where('sn', $sn)->first();
                        if ($device) {
                            $device->update([
                                'status' => 0, // Offline
                            ]);

                            // Update powerbanks in this device to maintenance status
                            \App\Models\Powerbank::where('device_id', $device->id)
                                ->update(['status' => 'maintenance']);
                        }
                    }
                } catch (\Exception $e) {
                    $this->error("Error updating device status: " . $e->getMessage());
                }
            }
        }

        if ($removed > 0) {
            $this->info("Removed {$removed} inactive connections");
        }
    }

    /**
     * Check all connections for stability
     */
    private function checkConnectionsStability()
    {
        $unstableCount = 0;

        foreach (self::$clients as $index => $clientInfo) {
            if (!isset($clientInfo['conn'])) {
                unset(self::$clients[$index]);
                continue;
            }

            $conn = $clientInfo['conn'];
            $sn = $clientInfo['sn'] ?? "unknown";

            if (!is_resource($conn) || !$this->isConnectionStable($conn, $sn)) {
                $this->warn("Removing unstable connection for device: {$sn}");

                if (is_resource($conn)) {
                    @fclose($conn);
                }

                unset(self::$clients[$index]);
                $unstableCount++;

                // If this is a device with SN, update its status in the database
                if ($sn && $sn !== "unknown") {
                    try {
                        $device = \App\Models\Device::where('sn', $sn)->first();
                        if ($device) {
                            $device->update(['status' => 0]); // Mark as offline

                            // Update powerbanks in this device to maintenance status
                            \App\Models\Powerbank::where('device_id', $device->id)
                                ->update(['status' => 'maintenance']);

                            $this->info("Updated device {$sn} status to offline");
                        }
                    } catch (\Exception $e) {
                        $this->error("Error updating device status: " . $e->getMessage());
                    }
                }
            }
        }

        if ($unstableCount > 0) {
            $this->info("Removed {$unstableCount} unstable connections");
        }
    }



    /**
     * Handle the rent response from a device with slot data
     *
     * @param array $request The request data
     * @param string $sn The device serial number
     * @return array The response data
     */
    private function handleRentResponse($request, $sn)
    {
        $this->info("Processing rent response with slot data from device: {$sn}");

        // Extract data from the request
        $slotNumber = $request['data']['n'] ?? null;
        $result = $request['data']['r'] ?? null;
        $messageId = $request['data']['msgack'] ?? $request['msg'] ?? null;
        $slotData = $request['data']['d'] ?? [];
        $slotStatus = $request['data']['st'] ?? 0;
        $aims = $request['aims'] ?? 0;

        $this->info("Rent response for slot {$slotNumber}, result: {$result}, aims: {$aims}");

        try {
            // Get the device from database
            $device = \App\Models\Device::where('sn', $sn)->first();

            if (!$device) {
                $this->error("Device with SN {$sn} not found in database");
                return [
                    'cmd' => 'rent',
                    'data' => [
                        'msgack' => $messageId ?? rand(10000, 99999),
                    ]
                ];
            }

            // Process the slot data to update device inventory
            if (!empty($slotData)) {
                $this->processSlotData($device, $slotData, $aims);
            }

            // If the rent was successful (r=0), update the command status
            if ($result === 0) {
                // No need to calculate actualSlotNumber since we're using aims directly
                $actualSlotNumber = $slotNumber; // We'll use the original slot number with aims

                // Update the command status in the database
                $command = \App\Models\DeviceCommand::where('device_sn', $sn)
                    ->where('command', 'rent')
                    ->where(function ($query) use ($messageId) {
                        $query->where('message_id', $messageId)
                            ->orWhere('created_at', '>=', now()->subMinutes(5)); // Fallback to recent commands
                    })
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($command) {
                    $command->update([
                        'status' => 'completed',
                        'executed_at' => now(),
                        'response' => json_encode($request)
                    ]);

                    $this->info("Updated command status to completed for message ID: {$messageId}");
                } else {
                    $this->warn("No matching command found for message ID: {$messageId}");
                }

                // Check if we need to create a rental record
                $powerbank = \App\Models\Powerbank::where('device_id', $device->id)
                    ->where(function ($query) use ($actualSlotNumber) {
                        $query->where('slot_number', $actualSlotNumber)
                            ->orWhereNull('slot_number'); // Also check for powerbanks not in a slot
                    })
                    ->where('status', '!=', 'rented')
                    ->first();

                if ($powerbank) {
                    // Check if a rental record already exists
                    $existingRental = \App\Models\PowerbankRental::where('powerbank_id', $powerbank->id)
                        ->whereNull('returned_at')
                        ->first();

                    if (!$existingRental) {
                        // Create rental record
                        \App\Models\PowerbankRental::create([
                            'powerbank_id' => $powerbank->id,
                            'device_id' => $device->id,
                            'customer_id' => 'CUSTOMER1', // Default customer ID
                            'rented_at' => now(),
                            'initial_charge' => $powerbank->current_charge,
                            'slot_number' => $actualSlotNumber, // Store the original slot number in the rental record
                            'aims' => $aims
                        ]);

                        // Debug the powerbank before update
                        $this->info("Before update - Powerbank ID: {$powerbank->id}, Slot: {$powerbank->slot_number}, Status: {$powerbank->status}");

                        // Update powerbank status using direct DB query to bypass model casting
                        \Illuminate\Support\Facades\DB::table('powerbanks')
                            ->where('id', $powerbank->id)
                            ->update([
                                'status' => 'rented',
                                'slot_number' => null
                            ]);

                        // Refresh the model to see the changes
                        $powerbank->refresh();

                        // Debug the powerbank after update
                        $this->info("After update - Powerbank ID: {$powerbank->id}, Slot: " .
                            (is_null($powerbank->slot_number) ? "NULL" : $powerbank->slot_number) .
                            ", Status: {$powerbank->status}");

                        $this->info("Created rental record for powerbank: {$powerbank->serial_number}");
                    } else {
                        $this->info("Rental record already exists for powerbank: {$powerbank->serial_number}");
                    }
                } else {
                    $this->warn("No available powerbank found for slot: {$actualSlotNumber}");
                }
            } else {
                // Rent failed
                $this->error("Rent command failed with result code: {$result}");

                // Update the command status in the database
                \App\Models\DeviceCommand::where('device_sn', $sn)
                    ->where('command', 'rent')
                    ->where(function ($query) use ($messageId) {
                        $query->where('message_id', $messageId)
                            ->orWhere('created_at', '>=', now()->subMinutes(5)); // Fallback to recent commands
                    })
                    ->update([
                        'status' => 'failed',
                        'executed_at' => now(),
                        'response' => json_encode($request)
                    ]);

                // Log the failure reason
                \Illuminate\Support\Facades\Log::error("Rent command failed", [
                    'device_sn' => $sn,
                    'message_id' => $messageId,
                    'error_code' => $result,
                    'response' => $request
                ]);
            }

            // Return acknowledgment response
            return [
                'cmd' => 'rent',
                'data' => [
                    'msgack' => $messageId ?? rand(10000, 99999),
                ]
            ];
        } catch (\Exception $e) {
            $this->error("Error processing rent response: " . $e->getMessage());

            // Log the error
            \Illuminate\Support\Facades\Log::error("Rent response error for device {$sn}: " . $e->getMessage());

            // Return acknowledgment response
            return [
                'cmd' => 'rent',
                'data' => [
                    'msgack' => $messageId ?? rand(10000, 99999),
                ]
            ];
        }
    }
}
