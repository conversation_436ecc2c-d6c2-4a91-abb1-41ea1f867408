<?php

namespace App\Exports;

use App\Models\AuditTrail;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AuditTrailExport implements FromQuery, WithHeadings, WithMapping
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function query()
    {
        $query = AuditTrail::with('user')->latest();

        // Apply filters if provided
        if ($this->request->filled('user_id')) {
            $query->where('user_id', $this->request->user_id);
        }

        if ($this->request->filled('action')) {
            $query->where('action', $this->request->action);
        }

        if ($this->request->filled('module')) {
            $query->where('module', $this->request->module);
        }

        if ($this->request->filled('severity')) {
            $query->where('severity', $this->request->severity);
        }

        if ($this->request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $this->request->date_from);
        }

        if ($this->request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $this->request->date_to);
        }

        return $query;
    }

    public function headings(): array
    {
        return [
            'ID',
            'User',
            'Action',
            'Module',
            'Record Type',
            'Record ID',
            'Severity',
            'Notes',
            'IP Address',
            'Date/Time',
        ];
    }

    public function map($auditTrail): array
    {
        return [
            $auditTrail->id,
            $auditTrail->user ? $auditTrail->user->name : 'System',
            $auditTrail->action,
            $auditTrail->module,
            class_basename($auditTrail->auditable_type ?? ''),
            $auditTrail->auditable_id,
            $auditTrail->severity,
            $auditTrail->notes,
            $auditTrail->ip_address,
            $auditTrail->created_at->format('Y-m-d H:i:s'),
        ];
    }
}