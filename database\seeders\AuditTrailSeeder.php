<?php

namespace Database\Seeders;

use App\Models\AuditTrail;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Faker\Factory as Faker;

class AuditTrailSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();
        $users = User::all()->pluck('id')->toArray();

        if (empty($users)) {
            // Create a default user if none exists
            $user = User::factory()->create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
            ]);
            $users = [$user->id];
        }

        $actions = ['created', 'updated', 'deleted', 'viewed', 'login', 'logout', 'failed_login'];
        $modules = ['user', 'role', 'permission', 'setting', 'product', 'order', 'customer', 'invoice', 'payment'];
        $severities = ['low', 'medium', 'high'];
        $modelTypes = [
            'App\\Models\\User',
            'App\\Models\\Role',
            'App\\Models\\Permission',
            'App\\Models\\Setting',
        ];

        // Create 100 sample audit trail records
        for ($i = 0; $i < 100; $i++) {
            $action = $faker->randomElement($actions);
            $module = $faker->randomElement($modules);
            $severity = $faker->randomElement($severities);
            $modelType = $faker->randomElement($modelTypes);
            $modelId = $faker->numberBetween(1, 10);

            $createdAt = Carbon::now()->subDays($faker->numberBetween(0, 30))
                ->subHours($faker->numberBetween(0, 23))
                ->subMinutes($faker->numberBetween(0, 59));

            $oldValues = null;
            $newValues = null;

            if ($action === 'created') {
                $newValues = json_encode([
                    'name' => $faker->name,
                    'email' => $faker->email,
                    'status' => 'active',
                ]);
            } elseif ($action === 'updated') {
                $oldValues = json_encode([
                    'name' => $faker->name,
                    'email' => $faker->email,
                    'status' => 'inactive',
                ]);
                $newValues = json_encode([
                    'name' => $faker->name,
                    'email' => $faker->email,
                    'status' => 'active',
                ]);
            } elseif ($action === 'deleted') {
                $oldValues = json_encode([
                    'name' => $faker->name,
                    'email' => $faker->email,
                    'status' => 'active',
                ]);
            }

            AuditTrail::create([
                'user_id' => $faker->randomElement($users),
                'action' => $action,
                'module' => $module,
                'auditable_type' => $modelType,
                'auditable_id' => $modelId,
                'old_values' => $oldValues,
                'new_values' => $newValues,
                'severity' => $severity,
                'notes' => "User {$action} a {$module} record",
                'ip_address' => $faker->ipv4,
                'user_agent' => $faker->userAgent,
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
        }
    }
}
