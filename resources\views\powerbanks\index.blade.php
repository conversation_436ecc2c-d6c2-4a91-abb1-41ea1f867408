@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title m-0">All Powerbanks</h4>
                        <div>
                            <a href="{{ route('powerbanks.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus mr-1"></i> Add Powerbank
                            </a>
                            <button type="button" class="btn btn-warning btn-sm" data-toggle="modal"
                                data-target="#ejectAllModal">
                                <i class="fas fa-eject mr-1"></i> Force Eject All
                            </button>
                            <button type="button" class="btn btn-info btn-sm" data-toggle="modal"
                                data-target="#rebootDeviceModal">
                                <i class="fas fa-sync-alt mr-1"></i> Reboot Device
                            </button>
                        </div>
                    </div>

                    @if (count($powerbanks) > 0)
                        <div class="table-responsive">
                            <table id="datatable-buttons" class="table table-striped table-bordered dt-responsive nowrap"
                                style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Serial Number</th>
                                        <th>Device</th>
                                        <th>Capacity</th>
                                        <th>Current Charge</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($powerbanks as $powerbank)
                                        <tr>
                                            <td>{{ $powerbank->id }}</td>
                                            <td>{{ $powerbank->serial_number }}</td>
                                            <td>{{ $powerbank->device ? $powerbank->device->sn : 'Not assigned' }}</td>
                                            <td>{{ $powerbank->capacity }} mAh</td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar
                                                    @if ($powerbank->current_charge < 20) bg-danger
                                                    @elseif($powerbank->current_charge < 50) bg-warning
                                                    @else bg-success @endif"
                                                        role="progressbar" style="width: {{ $powerbank->current_charge }}%;"
                                                        aria-valuenow="{{ $powerbank->current_charge }}" aria-valuemin="0"
                                                        aria-valuemax="100">{{ $powerbank->current_charge }}%</div>
                                                </div>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge badge-{{ $powerbank->status == 'available'
                                                        ? 'success'
                                                        : ($powerbank->status == 'rented'
                                                            ? 'warning'
                                                            : ($powerbank->status == 'charging'
                                                                ? 'info'
                                                                : 'danger')) }}">
                                                    {{ ucfirst($powerbank->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ route('powerbanks.show', $powerbank->id) }}"
                                                        class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('powerbanks.edit', $powerbank->id) }}"
                                                        class="btn btn-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-warning btn-sm eject-btn"
                                                        data-toggle="modal" data-target="#ejectPowerbankModal"
                                                        data-id="{{ $powerbank->id }}"
                                                        data-sn="{{ $powerbank->serial_number }}"
                                                        data-device-sn="{{ $powerbank->device->sn ?? '' }}"
                                                        data-slot-number="{{ $powerbank->slot_number ?? 1 }}">
                                                        <i class="fas fa-eject"></i>
                                                    </button>
                                                    @if ($powerbank->status == 'reported' || $powerbank->status == 'investigating')
                                                        <a href="{{ route('powerbanks.show', ['id' => $powerbank->id, 'resolve_issue' => $powerbank->id]) }}"
                                                            class="btn btn-success btn-sm">
                                                            <i class="fas fa-check"></i>
                                                        </a>
                                                    @endif
                                                    <form action="{{ route('powerbanks.destroy', $powerbank->id) }}"
                                                        method="POST" style="display: inline-block">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-sm"
                                                            onclick="return confirm('Are you sure you want to delete this powerbank?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-battery-empty text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">No Powerbanks Available</h5>
                            <p class="text-muted">There are no powerbanks in the system yet.</p>
                            <a href="{{ route('powerbanks.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i> Add Powerbank
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>


    <!-- Eject All Powerbanks Modal (Force Command) -->
    <div class="modal fade" id="ejectAllModal" tabindex="-1" role="dialog" aria-labelledby="ejectAllModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ejectAllModalLabel">Force Eject All Powerbanks</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to force eject all powerbanks from the selected device?</p>
                    <div class="form-group">
                        <label for="deviceSelect">Select Device</label>
                        <select class="form-control" id="deviceSelect">
                            <option value="">All Devices</option>
                            @foreach ($devices as $device)
                                <option value="{{ $device->id }}">{{ $device->sn }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div id="ejectAllStatus" class="alert alert-info d-none">
                        <span id="ejectAllMessage">Sending force command to device...</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <form id="ejectAllForm" action="{{ route('devices.eject-all') }}" method="POST">
                        @csrf
                        <input type="hidden" name="device_id" id="selectedDeviceId">
                        <button type="submit" class="btn btn-warning">Force Eject All</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Reboot Device Modal -->
    <div class="modal fade" id="rebootDeviceModal" tabindex="-1" role="dialog"
        aria-labelledby="rebootDeviceModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rebootDeviceModalLabel">Reboot Device</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Select the device you want to reboot:</p>
                    <div class="form-group">
                        <label for="rebootDeviceSelect">Select Device</label>
                        <select class="form-control" id="rebootDeviceSelect">
                            @foreach ($devices as $device)
                                <option value="{{ $device->id }}" data-sn="{{ $device->sn }}">{{ $device->sn }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div id="rebootCommandStatus" class="alert alert-info d-none">
                        <span id="rebootCommandMessage">Sending reboot command to device...</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" id="confirmRebootBtn" class="btn btn-info">Reboot Device</button>
                    <form id="rebootDeviceForm" action="{{ route('devices.reboot') }}" method="POST"
                        style="display:none;">
                        @csrf
                        <input type="hidden" name="device_id" id="selectedRebootDeviceId">
                        <input type="hidden" name="device_sn" id="rebootDeviceSn">
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Eject Single Powerbank Modal (Rent Command) -->
    <div class="modal fade" id="ejectPowerbankModal" tabindex="-1" role="dialog"
        aria-labelledby="ejectPowerbankModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ejectPowerbankModalLabel">Eject Powerbank</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="ejectForm" action="{{ route('powerbanks.eject') }}" method="POST">
                        @csrf
                        <input type="hidden" name="powerbank_id" id="ejectPowerbankId">
                        <input type="hidden" name="device_sn" id="ejectDeviceSn">
                        <input type="hidden" name="slot_number" id="ejectSlotNumber">
                        <input type="hidden" name="aims" id="ejectAims">

                        <p>Are you sure you want to eject this powerbank?</p>

                        <div id="ejectCommandStatus" class="alert alert-info d-none">
                            <span id="ejectCommandMessage">Sending command to device...</span>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" id="confirmEjectBtn">Eject</button>
                </div>
            </div>
        </div>
    </div>

@endsection


@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#powerbanks-datatable').DataTable({
                responsive: true,
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                pageLength: 10,
                buttons: [
                    'copy', 'excel', 'pdf'
                ]
            });

            // Handle eject powerbank button click - this only sets up the modal
            $(document).on('click', '.eject-btn', function() {
                var powerbankId = $(this).data('id');
                var deviceSn = $(this).data('device-sn');
                var slotNumber = $(this).data('slot');
                var aims = $(this).data('aims') || 0;

                $('#ejectPowerbankId').val(powerbankId);
                $('#ejectDeviceSn').val(deviceSn);
                $('#ejectSlotNumber').val(slotNumber);
                $('#ejectAims').val(aims);

                // Reset status message
                $('#ejectCommandStatus').addClass('d-none');
                $('#ejectCommandMessage').text('');
            });

            // Handle confirm eject button click inside the modal (for single powerbank)
            $('#confirmEjectBtn').on('click', function() {
                var deviceSn = $('#ejectDeviceSn').val();
                var slotNumber = $('#ejectSlotNumber').val();

                if (deviceSn) {
                    // Show status message
                    $('#ejectCommandStatus').removeClass('d-none');

                    // Send only the rent command for a specific slot
                    var messageId = Math.floor(Math.random() * 90000) + 10000;
                    var slotNum = slotNumber || 1;
                    var rentCommand = "#*{\"cmd\":\"rent\",\"sn\":\"" + deviceSn + "\"",
                        \"data\":{\"msg\":" +
                        messageId + ",\"n\":" + slotNum + "}}*#";

                    console.log("Sending rent command for slot " + slotNum + ":", rentCommand);
                    $('#ejectCommandMessage').text('Sending rent command to device...');

                    $.ajax({
                        url: "{{ route('device-commands.store') }}",
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            device_sn: deviceSn,
                            command: "rent",
                            message_id: messageId,
                            data: JSON.stringify({
                                n: parseInt(slotNum)
                            }),
                            raw_command: rentCommand
                        },
                        success: function(response) {
                            console.log("Rent command saved successfully:", response);
                            $('#ejectCommandMessage').text(
                                'Rent command sent successfully. Updating database...');

                            // Submit the form to update the database
                            $('#ejectPowerbankForm').submit();
                        },
                        error: function(xhr) {
                            console.error("Error saving rent command:", xhr.responseText);
                            $('#ejectCommandMessage').text(
                                'Error sending rent command. Updating database anyway...');

                            // Submit the form anyway
                            $('#ejectPowerbankForm').submit();
                        }
                    });
                } else {
                    console.warn("No device serial number provided for powerbank");
                    $('#ejectCommandMessage').text(
                        'No device serial number provided. Updating database only...');

                    // Submit the form without sending commands
                    $('#ejectPowerbankForm').submit();
                }
            });

            // Handle device selection for eject all
            $('#deviceSelect').on('change', function() {
                $('#selectedDeviceId').val($(this).val());
            });

            // Handle reboot device selection
            $('#rebootDeviceSelect').on('change', function() {
                var deviceId = $(this).val();
                var deviceSn = $(this).find('option:selected').data('sn');
                $('#selectedRebootDeviceId').val(deviceId);
                $('#rebootDeviceSn').val(deviceSn);
            });

            // Set initial values for reboot device
            var initialDeviceId = $('#rebootDeviceSelect').val();
            var initialDeviceSn = $('#rebootDeviceSelect').find('option:selected').data('sn');
            $('#selectedRebootDeviceId').val(initialDeviceId);
            $('#rebootDeviceSn').val(initialDeviceSn);

            // Handle confirm reboot button click
            $('#confirmRebootBtn').on('click', function() {
                var deviceId = $('#selectedRebootDeviceId').val();
                var deviceSn = $('#rebootDeviceSn').val();

                if (deviceSn) {
                    // Show status message
                    $('#rebootCommandStatus').removeClass('d-none');
                    $('#rebootCommandMessage').text('Sending reboot command to device...');

                    // Generate message ID and create reboot command
                    var messageId = Math.floor(Math.random() * 90000) + 10000;
                    var rebootCommand = "#*{\"cmd\":\"reboot\",\"sn\":\"" + deviceSn +
                        "\",\"data\":{\"msg\":" + messageId + ",\"dev\":1}}*#";

                    console.log("Sending reboot command:", rebootCommand);

                    // Send AJAX request to save the command
                    $.ajax({
                        url: "{{ route('device-commands.store') }}",
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            device_sn: deviceSn,
                            command: "reboot",
                            message_id: messageId,
                            data: JSON.stringify({
                                dev: 1
                            }),
                            raw_command: rebootCommand
                        },
                        success: function(response) {
                            console.log("Reboot command saved successfully:", response);
                            $('#rebootCommandMessage').text(
                                'Reboot command sent successfully. Updating database...');

                            // Submit the form to update the database
                            $('#rebootDeviceForm').submit();
                        },
                        error: function(xhr) {
                            console.error("Error saving reboot command:", xhr.responseText);
                            $('#rebootCommandMessage').text(
                                'Error sending reboot command. Updating database anyway...');

                            // Submit the form anyway
                            $('#rebootDeviceForm').submit();
                        }
                    });
                } else {
                    console.warn("No device serial number provided for reboot");
                    $('#rebootCommandMessage').text('No device serial number provided. Cannot reboot.');
                }
            });

            // Handle eject all form submission (using force command)
            $('#ejectAllForm').on('submit', function(e) {
                var deviceId = $('#selectedDeviceId').val();
                var deviceSn = $('#deviceSelect option:selected').text();

                // Only proceed with AJAX if we have a specific device selected
                if (deviceId && deviceSn && deviceSn !== 'All Devices') {
                    e.preventDefault(); // Prevent form submission until AJAX completes

                    // Show status message
                    $('#ejectAllStatus').removeClass('d-none');
                    $('#ejectAllMessage').text('Sending force command to device...');

                    // Send only the force command for all slots
                    var forceMessageId = Math.floor(Math.random() * 9000) + 1000;
                    var forceCommand = "#*{\"cmd\":\"force\",\"sn\":" + deviceSn + ",\"msg\":" +
                        forceMessageId + ",\"data\":{\"n\":0},\"aims\":0}*#";

                    console.log("Sending force command for all slots:", forceCommand);

                    $.ajax({
                        url: "{{ route('device-commands.store') }}",
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            device_sn: deviceSn,
                            command: "force",
                            message_id: forceMessageId,
                            data: JSON.stringify({
                                n: 0
                            }),
                            raw_command: forceCommand
                        },
                        success: function(response) {
                            console.log("Force command saved successfully:", response);
                            $('#ejectAllMessage').text(
                                'Force command sent successfully. Updating database...');

                            // Now submit the form
                            $('#ejectAllForm')[0].submit();
                        },
                        error: function(xhr) {
                            console.error("Error saving force command:", xhr.responseText);
                            $('#ejectAllMessage').text(
                                'Error sending force command. Updating database anyway...');

                            // Submit the form anyway
                            $('#ejectAllForm')[0].submit();
                        }
                    });
                }
                // If no device selected or "All Devices" is selected, let the form submit normally
            });
        });
    </script>
@endsection
