@extends('layouts.master')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="card-title">Database Backups</h4>
                        <a href="{{ route('admin.backups.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Backup
                        </a>
                    </div>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Filename</th>
                                        <th>Size</th>
                                        <th>Created By</th>
                                        <th>Created At</th>
                                        <th>Last Downloaded</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($backups as $backup)
                                        <tr>
                                            <td>{{ $backup->id }}</td>
                                            <td>{{ $backup->filename }}</td>
                                            <td>{{ $backup->formatted_size }}</td>
                                            <td>{{ $backup->creator ? $backup->creator->name : 'System' }}</td>
                                            <td>{{ $backup->created_at->format('Y-m-d H:i:s') }}</td>
                                            <td>{{ $backup->downloaded_at ? $backup->downloaded_at->format('Y-m-d H:i:s') : 'Never' }}
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.backups.download', $backup->id) }}"
                                                    class="btn btn-sm btn-success">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                                <form action="{{ route('admin.backups.destroy', $backup->id) }}"
                                                    method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger"
                                                        onclick="return confirm('Are you sure you want to delete this backup?')">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="7" class="text-center">No backups found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4">
                            {{ $backups->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
