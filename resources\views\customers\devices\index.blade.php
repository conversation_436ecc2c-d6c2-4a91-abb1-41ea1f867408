@extends('layouts.customer')

@section('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: rgba(24, 102, 247, 0.918);
            --secondary-color: rgb(236, 165, 33);
            --accent-color: #4895ef;
            --success-color: #4cc9f0;
            --danger-color: #f72585;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --gray-color: #6c757d;
        }

        /* Map Section Styles */
        .map-section {
            margin-bottom: 2rem;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .map-container {
            position: relative;
            height: 500px;
            border-radius: 10px;
            overflow: hidden;
            background: #f8f9fa;
        }

        #map {
            width: 100%;
            height: 100%;
            min-height: 500px;
        }

        .map-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: var(--gray-color);
        }

        .map-error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .map-overlay {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .map-legend h3 {
            font-size: 14px;
            margin-bottom: 10px;
            color: var(--dark-color);
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .legend-dot.available {
            background-color: var(--success-color);
        }

        .legend-dot.low {
            background-color: var(--secondary-color);
        }

        .legend-dot.empty {
            background-color: var(--danger-color);
        }

        /* Table Section Styles */
        .table-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .table-controls {
            display: flex;
            gap: 15px;
        }

        .search-input {
            padding: 8px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 250px;
        }

        .filter-select {
            padding: 8px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }

        .devices-table {
            width: 100%;
            border-collapse: collapse;
        }

        .devices-table th {
            background-color: var(--light-color);
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            color: var(--dark-color);
        }

        .devices-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .devices-table tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 50px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-available {
            background-color: rgba(76, 201, 240, 0.1);
            color: var(--success-color);
        }

        .status-low {
            background-color: rgba(236, 165, 33, 0.1);
            color: var(--secondary-color);
        }

        .status-empty {
            background-color: rgba(247, 37, 133, 0.1);
            color: var(--danger-color);
        }

        .action-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .action-btn:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .how-to-order {
            background: #fff;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .step-card {
            padding: 1.5rem;
            height: 100%;
            position: relative;
            transition: transform 0.3s ease;
        }

        .step-card:hover {
            transform: translateY(-5px);
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: #4361ee;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0 auto 1rem;
        }

        .step-card h5 {
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .step-card p {
            color: #718096;
            margin-bottom: 0;
        }
    </style>
@endsection

@section('content')
    <div class="container">
        <!-- Page Header -->
        <div class="page-header mb-4">
            <h3>Device Locations</h3>
            <p>Power bank devices across all locations</p>
        </div>

        <!-- How to Order Section -->
        <!-- <div class="how-to-order mb-4">
            <h4 class="text-center mb-4">How to Order</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="step-card text-center">
                        <div class="step-number">1</div>
                        <h5>Find the Device</h5>
                        <p>Locate the nearest power bank device using our interactive map</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="step-card text-center">
                        <div class="step-number">2</div>
                        <h5>Scan & Register</h5>
                        <p>Scan the QR code on the device and complete the registration process</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="step-card text-center">
                        <div class="step-number">3</div>
                        <h5>Use & Return</h5>
                        <p>Get your power bank, use it, and return it to any available device</p>
                    </div>
                </div>
            </div>
        </div> -->



        <!-- Map Section -->
        <section class="map-section">
            <!-- Social Login Buttons -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <a href="{{ route('social.login', 'google') }}" class="btn btn-outline-danger w-100">
                        <i class="fab fa-google me-2"></i> Sign in with Google
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="{{ route('social.login', 'facebook') }}" class="btn btn-outline-primary w-100">
                        <i class="fab fa-facebook me-2"></i> Sign in with Facebook
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="{{ route('customer.register', 'email') }}" class="btn btn-outline-success w-100">
                        <i class="fab fa-whatsapp me-2"></i> Sign in with WhatsApp
                    </a>
                </div>
            </div>
            <div class="map-container">
                <div id="map"></div>
                <div class="map-loading">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p>Loading map...</p>
                </div>
                
                <!-- <div class="map-overlay">
                    <div class="map-legend">
                        <h3>Legend</h3>
                        <div class="legend-item">
                            <span class="legend-dot available"></span>
                            <span>Available</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-dot low"></span>
                            <span>Low Stock</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-dot empty"></span>
                            <span>Empty</span>
                        </div>
                    </div>
                </div> -->
            </div>
        </section>

        {{-- <!-- Table Section -->
        <section class="table-section">
            <div class="table-header">
                <h4>List of device</h4>
                <div class="table-controls">
                    <input type="text" id="searchInput" placeholder="Search devices..." class="search-input">
                    <!-- <select name="filterSelect" id="filterSelect" class="filter-select">
                        <option value="">All Areas</option>
                        @foreach ($devices->pluck('location_name')->unique() as $location_name)
                            <option value="{{ $location_name }}">{{ $location_name }}</option>
                        @endforeach
                    </select> -->
                </div>
            </div>

            <div class="table-container">
                <table class="devices-table" id="devicesTable">
                    <thead>
                        <tr>
                            <th>Location</th>
                            <th>Available Power Banks</th>
                            <th>Status</th>
                            <th>Last Updated</th>
                        </tr>
                    </thead>
                    <tbody id="devicesTableBody">
                        @foreach ($devices as $device)
                            <tr>
                                <td>{{ $device->location_name }}</td>
                                <td>{{ $device->available_powerbanks_count }}</td>
                                <td>
                                    @if ($device->is_available)
                                        <span class="status-badge status-available">Available</span>
                                    @elseif($device->available_powerbanks_count > 0)
                                        <span class="status-badge status-low">Low Stock</span>
                                    @else
                                        <span class="status-badge status-empty">Empty</span>
                                    @endif
                                </td>
                                <td>{{ $device->updated_at->diffForHumans() }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </section> --}}
    </div>
@endsection

@section('scripts')
    <script>
        let map;
        let markers = [];
        let infoWindow;

        function initMap() {
            // Hide loading indicator
            document.querySelector('.map-loading').style.display = 'none';

            // Default center (Karachi, Pakistan coordinates)
            const defaultCenter = {
                lat: 24.8607,
                lng: 67.0011
            };

            // Initialize map
            map = new google.maps.Map(document.getElementById("map"), {
                zoom: 12,
                center: defaultCenter,
                mapTypeControl: false,
                streetViewControl: false,
                fullscreenControl: true,
                zoomControl: true,
                styles: [{
                    "featureType": "poi",
                    "stylers": [{
                        "visibility": "off"
                    }]
                }]
            });

            // Create info window
            infoWindow = new google.maps.InfoWindow();

            // Add device markers
            addDeviceMarkers();
        }

        function addDeviceMarkers() {
            const devices = @json($devices);

            devices.forEach(device => {
                if (device.latitude && device.longitude) {
                    const position = {
                        lat: parseFloat(device.latitude),
                        lng: parseFloat(device.longitude)
                    };

                    // Skip if invalid coordinates
                    if (isNaN(position.lat) || isNaN(position.lng)) {
                        return;
                    }

                    // Determine marker icon based on device status
                    let markerIcon;
                    if (device.is_available && device.available_powerbanks_count > 5) {
                        // Green marker for well-stocked devices
                        markerIcon = {
                            url: "data:image/svg+xml;charset=UTF-8," + encodeURIComponent(`
                                <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="20" cy="20" r="18" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
                                    <circle cx="20" cy="20" r="8" fill="white"/>
                                    <text x="20" y="24" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="#4CAF50">${device.available_powerbanks_count}</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(40, 40),
                            origin: new google.maps.Point(0, 0),
                            anchor: new google.maps.Point(20, 40)
                        };
                    } else if (device.is_available && device.available_powerbanks_count > 0) {
                        // Orange marker for low stock devices
                        markerIcon = {
                            url: "data:image/svg+xml;charset=UTF-8," + encodeURIComponent(`
                                <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="20" cy="20" r="18" fill="#FF9800" stroke="#E65100" stroke-width="2"/>
                                    <circle cx="20" cy="20" r="8" fill="white"/>
                                    <text x="20" y="24" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="#FF9800">${device.available_powerbanks_count}</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(40, 40),
                            origin: new google.maps.Point(0, 0),
                            anchor: new google.maps.Point(20, 40)
                        };
                    } else {
                        // Red marker for empty devices
                        markerIcon = {
                            url: "data:image/svg+xml;charset=UTF-8," + encodeURIComponent(`
                                <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="20" cy="20" r="18" fill="#F44336" stroke="#C62828" stroke-width="2"/>
                                    <circle cx="20" cy="20" r="8" fill="white"/>
                                    <text x="20" y="24" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="#F44336">0</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(40, 40),
                            origin: new google.maps.Point(0, 0),
                            anchor: new google.maps.Point(20, 40)
                        };
                    }

                    // Create marker
                    const marker = new google.maps.Marker({
                        position: position,
                        map: map,
                        title: device.location_name,
                        icon: markerIcon,
                        animation: google.maps.Animation.DROP
                    });

                    // Add click listener to open info window
                    marker.addListener('click', () => {
                        const content = `
                            <div style="font-family: Arial, sans-serif; font-size: 14px; max-width: 250px;">
                                <div style="text-align: center; margin-bottom: 10px;">
                                    <img src="data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
                                        <svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
                                            <rect width="60" height="60" rx="8" fill="#f8f9fa"/>
                                            <circle cx="30" cy="30" r="20" fill="${device.is_available && device.available_powerbanks_count > 5 ? '#4CAF50' : device.is_available && device.available_powerbanks_count > 0 ? '#FF9800' : '#F44336'}"/>
                                            <circle cx="30" cy="30" r="12" fill="white"/>
                                            <text x="30" y="35" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="${device.is_available && device.available_powerbanks_count > 5 ? '#4CAF50' : device.is_available && device.available_powerbanks_count > 0 ? '#FF9800' : '#F44336'}">${device.available_powerbanks_count}</text>
                                        </svg>
                                    `)}" alt="Device Status" style="width: 60px; height: 60px;">
                                </div>
                                <h6 style="margin: 0 0 5px 0; font-weight: bold; color: #333;">${device.location_name}</h6>
                                <p style="margin: 0 0 10px 0; color: #666;">${device.location_details || 'Power bank device location'}</p>
                                <p style="margin: 0 0 10px 0;">
                                    <strong>Available:</strong> ${device.available_powerbanks_count} power banks
                                </p>
                                <p style="margin: 0 0 15px 0; font-size: 12px; color: #888;">
                                    <i class="fas fa-map-marker-alt"></i> Device ID: ${device.sn}
                                </p>
                                <a href="/lease?t=${device.sn}" class="btn btn-primary btn-sm" style="background-color: #4285F4; color: white; text-decoration: none; padding: 8px 15px; border-radius: 4px; display: inline-block; width: 100%; text-align: center;">
                                    <i class="fas fa-bolt"></i> Rent Power Bank
                                </a>
                            </div>`;
                        infoWindow.setContent(content);
                        infoWindow.open(map, marker);
                    });

                    markers.push(marker);
                }
            });
        }

        function handleMapError() {
            document.querySelector('.map-loading').style.display = 'none';
            document.getElementById('map').innerHTML =
                '<div class="map-error">' +
                '<h5><i class="fas fa-exclamation-triangle text-danger"></i> Map Error</h5>' +
                '<p>Could not load Google Maps. Please check your internet connection and API key.</p>' +
                '</div>';
        }
    </script>

    <!-- Google Maps API with callback -->
    <script async
        src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&callback=initMap"
        onerror="handleMapError()">
    </script>

    <script>
        // Table search and filter functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchText = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('#devicesTableBody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });

        document.getElementById('filterSelect').addEventListener('change', function(e) {
            const filterValue = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('#devicesTableBody tr');

            rows.forEach(row => {
                const location_name = row.children[1].textContent.toLowerCase();
                row.style.display = !filterValue || location_name === filterValue ? '' : 'none';
            });
        });
    </script>
@endsection
