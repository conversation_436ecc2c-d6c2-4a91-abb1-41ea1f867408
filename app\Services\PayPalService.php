<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PayPalService
{
    protected $baseUrl;
    protected $clientId;
    protected $clientSecret;
    protected $accessToken;

    public function __construct()
    {
        $this->baseUrl = env('PAYPAL_MODE', 'sandbox') === 'sandbox'
            ? 'https://api-m.sandbox.paypal.com'
            : 'https://api-m.paypal.com';

        $this->clientId = config('services.paypal.client_id');
        $this->clientSecret = config('services.paypal.client_secret');
    }

    /**
     * Get PayPal access token
     */
    protected function getAccessToken()
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        $response = Http::withBasicAuth($this->clientId, $this->clientSecret)
            ->asForm()
            ->post("{$this->baseUrl}/v1/oauth2/token", [
                'grant_type' => 'client_credentials'
            ]);

        if ($response->successful()) {
            $this->accessToken = $response->json()['access_token'];
            return $this->accessToken;
        }

        Log::error('PayPal access token error', $response->json());
        throw new \Exception('Failed to get PayPal access token');
    }

    /**
     * Create a PayPal order for payment verification
     */
    public function createOrder()
    {
        $accessToken = $this->getAccessToken();

        $response = Http::withToken($accessToken)
            ->post("{$this->baseUrl}/v2/checkout/orders", [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'amount' => [
                            'currency_code' => 'USD',
                            'value' => '1.00' // Small amount for verification
                        ],
                        'description' => 'Payment method verification'
                    ]
                ],
                'application_context' => [
                    'return_url' => route('customer.verify-payment.callback'),
                    'cancel_url' => route('customer.verify-payment')
                ]
            ]);

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('PayPal create order error', $response->json());
        throw new \Exception('Failed to create PayPal order: ' . ($response->json()['message'] ?? 'Unknown error'));
    }

    /**
     * Capture a PayPal order payment
     */
    public function captureOrder($orderId)
    {
        $accessToken = $this->getAccessToken();

        $response = Http::withToken($accessToken)
            ->post("{$this->baseUrl}/v2/checkout/orders/{$orderId}/capture");

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('PayPal capture order error', $response->json());
        throw new \Exception('Failed to capture PayPal payment: ' . ($response->json()['message'] ?? 'Unknown error'));
    }
}
