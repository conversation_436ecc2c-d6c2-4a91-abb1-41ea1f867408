@extends('layouts.customer')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Registration Complete!</h4>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        </div>
                        <h5 class="mb-3">Welcome, {{ $customer->name }}!</h5>
                        <p class="mb-4">Your account has been successfully created. You can now start using our power bank rental service.</p>
                        
                        <div class="mt-4 gap-2">
                            <button id="rentPowerbankBtn" class="btn btn-primary btn-lg">
                                <i class="fas fa-bolt me-2"></i>Rent a Power Bank
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const rentButton = document.getElementById('rentPowerbankBtn');
            
            if (rentButton) {
                rentButton.addEventListener('click', async function(e) {
                    e.preventDefault();
                    try {
                        // Show loading state
                        rentButton.disabled = true;
                        rentButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Checking Availability...';
                        
                        // Call the available powerbanks endpoint
                        const response = await fetch('{{ route("customer.available-powerbanks") }}', {
                            method: 'GET',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                                'Accept': 'application/json',
                                'Content-Type': 'application/json'
                            },
                            credentials: 'same-origin'
                        });
                        
                        const data = await response.json();
                        
                        if (data.success) {
                            console.log(data.powerbanks);
                            // If we have an available powerbank, redirect to the rental page
                            if (data.powerbanks) {
                                window.location.href = '{{ route("powerbanks.available") }}';
                            } else {
                                alert('No power banks are currently available. Please try again later!!');
                            }
                        } else {
                            throw new Error(data.message || 'Failed to check power bank availability');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        alert('An error occurred while checking power bank availability. Please try again.');
                    } finally {
                        // Reset button state
                        rentButton.disabled = false;
                        rentButton.innerHTML = '<i class="fas fa-bolt me-2"></i>Rent a Power Bank';
                    }
                });
            } else {
                console.error('Rent button not found');
            }
        });
    </script>
@endsection

@push('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .card-body {
            padding: 2rem;
        }

        .text-success {
            color: #28a745;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }

        #rentalProgressBar {
            font-weight: bold;
            font-size: 16px;
        }
    </style>
@endpush

