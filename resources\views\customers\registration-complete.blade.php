@extends('layouts.customer')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Registration Complete</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 64px;"></i>
                        </div>

                        <h3 class="mb-3">Welcome, {{ $customer->name }}!</h3>
                        <p class="lead">Your account has been successfully created.</p>

                        <div class="alert alert-success mt-4">
                            <p>You can now rent powerbanks from any of our devices.</p>
                        </div>

                        <div class="mt-4">
                            <button id="rentPowerbankBtn" class="btn btn-primary">
                                <i class="fas fa-bolt"></i> Rent a Powerbank
                            </button>
                        </div>

                        <!-- Available Powerbanks Modal -->
                        <div class="modal fade" id="availablePowerbanksModal" tabindex="-1"
                            aria-labelledby="availablePowerbanksModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="availablePowerbanksModalLabel">Available Powerbanks</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                            aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="loadingSpinner" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">Checking for available powerbanks...</p>
                                        </div>
                                        <div id="noPowerbanksMessage" class="alert alert-info d-none">
                                            No available powerbanks found.
                                        </div>
                                        <div id="powerbanksContainer" class="d-none">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Device</th>
                                                        <th>Slot</th>
                                                        <th>Charge</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="powerbanksTableBody">
                                                    <!-- Powerbanks will be loaded here -->
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- Rental Progress Section -->
                                        <div id="rentalProgressContainer" class="text-center d-none">
                                            <h4 class="mb-3">Renting Powerbank</h4>
                                            <div class="progress mb-3" style="height: 25px;">
                                                <div id="rentalProgressBar"
                                                    class="progress-bar progress-bar-striped progress-bar-animated"
                                                    role="progressbar" style="width: 0%;" aria-valuenow="0"
                                                    aria-valuemin="0" aria-valuemax="100">
                                                    0%
                                                </div>
                                            </div>
                                            <p id="rentalStatusMessage">Sending rent command to device...</p>
                                            <p class="mt-3">Please wait <span id="countdownTimer">30</span> seconds for
                                                the device to process your request.</p>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary"
                                            data-bs-dismiss="modal">Close</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .card-body {
            padding: 2rem;
        }

        .text-success {
            color: #28a745;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }

        #rentalProgressBar {
            font-weight: bold;
            font-size: 16px;
        }
    </style>
@endpush

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const rentPowerbankBtn = document.getElementById('rentPowerbankBtn');
            const availablePowerbanksModal = new bootstrap.Modal(document.getElementById(
                'availablePowerbanksModal'));
            const loadingSpinner = document.getElementById('loadingSpinner');
            const noPowerbanksMessage = document.getElementById('noPowerbanksMessage');
            const powerbanksContainer = document.getElementById('powerbanksContainer');
            const powerbanksTableBody = document.getElementById('powerbanksTableBody');
            const rentalProgressContainer = document.getElementById('rentalProgressContainer');
            const rentalProgressBar = document.getElementById('rentalProgressBar');
            const rentalStatusMessage = document.getElementById('rentalStatusMessage');
            const countdownTimer = document.getElementById('countdownTimer');

            // Function to generate a random message ID
            function generateMessageId() {
                return Math.floor(Math.random() * 90000) + 10000;
            }

            rentPowerbankBtn.addEventListener('click', function() {
                // Reset modal content
                loadingSpinner.classList.remove('d-none');
                noPowerbanksMessage.classList.add('d-none');
                powerbanksContainer.classList.add('d-none');
                rentalProgressContainer.classList.add('d-none');
                powerbanksTableBody.innerHTML = '';

                // Show modal
                availablePowerbanksModal.show();

                // Fetch available powerbanks
                $.ajax({
                    url: '{{ route('customer.available-powerbanks') }}',
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        loadingSpinner.classList.add('d-none');

                        if (data.powerbanks.length === 0) {
                            noPowerbanksMessage.classList.remove('d-none');
                        } else {
                            powerbanksContainer.classList.remove('d-none');

                            // Populate table with powerbanks
                            data.powerbanks.forEach(powerbank => {
                                const row = document.createElement('tr');

                                // Device column
                                const deviceCell = document.createElement('td');
                                deviceCell.textContent = powerbank.device.sn;
                                row.appendChild(deviceCell);

                                // Slot column
                                const slotCell = document.createElement('td');
                                slotCell.textContent = powerbank.slot_number;
                                row.appendChild(slotCell);

                                // Charge column
                                const chargeCell = document.createElement('td');
                                const chargeProgress = document.createElement('div');
                                chargeProgress.className = 'progress';
                                chargeProgress.style.height = '20px';

                                const chargeBar = document.createElement('div');
                                chargeBar.className =
                                    `progress-bar ${powerbank.current_charge < 20 ? 'bg-danger' : powerbank.current_charge < 50 ? 'bg-warning' : 'bg-success'}`;
                                chargeBar.style.width = `${powerbank.current_charge}%`;
                                chargeBar.textContent = `${powerbank.current_charge}%`;

                                chargeProgress.appendChild(chargeBar);
                                chargeCell.appendChild(chargeProgress);
                                row.appendChild(chargeCell);

                                // Action column
                                const actionCell = document.createElement('td');
                                const rentButton = document.createElement('button');
                                rentButton.className = 'btn btn-primary btn-sm';
                                rentButton.innerHTML =
                                    '<i class="fas fa-bolt"></i> Rent';
                                rentButton.dataset.powerbankId = powerbank.id;
                                rentButton.dataset.deviceSn = powerbank.device.sn;
                                rentButton.dataset.slotNumber = powerbank.slot_number;
                                rentButton.dataset.aims = powerbank.aims || 0;

                                rentButton.addEventListener('click', function() {
                                    rentPowerbank(
                                        this.dataset.deviceSn,
                                        this.dataset.slotNumber,
                                        this.dataset.powerbankId,
                                        this.dataset.aims
                                    );
                                });

                                actionCell.appendChild(rentButton);
                                row.appendChild(actionCell);

                                powerbanksTableBody.appendChild(row);
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching powerbanks:', error);
                        loadingSpinner.classList.add('d-none');
                        noPowerbanksMessage.classList.remove('d-none');
                        noPowerbanksMessage.textContent =
                            'Error loading powerbanks. Please try again.';
                    }
                });
            });

            function rentPowerbank(deviceSn, slotNumber, powerbankId, aims) {
                // Hide powerbanks list and show rental progress
                powerbanksContainer.classList.add('d-none');
                rentalProgressContainer.classList.remove('d-none');

                // Calculate actual slot number based on AIMS
                const actualSlotNumber = parseInt(slotNumber);
                aims = parseInt(aims || 0);
                const slotNum = aims > 0 ? actualSlotNumber % 12 || 12 : actualSlotNumber;

                // Generate a random message ID
                const messageId = generateMessageId();

                // Create the rent command
                const rentCommand =
                    `#*{"cmd":"rent","sn":"${deviceSn}","aims":${aims},"data":{"msg":${messageId},"n":${slotNum}}}*#`;

                // Update status message
                rentalStatusMessage.textContent = 'Sending rent command to device...';

                // Start the countdown timer
                let secondsLeft = 30;
                countdownTimer.textContent = secondsLeft;

                // Update progress bar
                rentalProgressBar.style.width = '0%';
                rentalProgressBar.textContent = '0%';

                // Save the command to the device_commands table using AJAX
                $.ajax({
                    url: '{{ route('device-commands.store') }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        device_sn: deviceSn,
                        command: 'rent',
                        message_id: messageId,
                        data: JSON.stringify({
                            n: parseInt(slotNum),
                            aims: parseInt(aims),
                            customer_id: '{{ $customer->id }}'
                        }),
                        raw_command: rentCommand,
                        status: 'pending'
                    },
                    success: function(response) {
                        console.log('Rent command saved successfully:', response);
                        rentalStatusMessage.textContent =
                            'Rent command sent successfully. Waiting for device to process...';

                        // Start progress bar animation
                        let progress = 0;
                        const progressInterval = setInterval(function() {
                            progress +=
                                3.33; // Increase by approximately 3.33% every second (100% / 30 seconds)
                            if (progress > 100) progress = 100;

                            rentalProgressBar.style.width = `${progress}%`;
                            rentalProgressBar.textContent = `${Math.round(progress)}%`;
                            rentalProgressBar.setAttribute('aria-valuenow', Math.round(
                                progress));
                        }, 1000);

                        // Start countdown timer
                        const countdownInterval = setInterval(function() {
                            secondsLeft--;
                            countdownTimer.textContent = secondsLeft;

                            if (secondsLeft <= 0) {
                                clearInterval(countdownInterval);
                                clearInterval(progressInterval);

                                // Update UI to show completion
                                rentalProgressBar.style.width = '100%';
                                rentalProgressBar.textContent = '100%';
                                rentalStatusMessage.textContent =
                                    'Rental process complete! Redirecting to success page...';

                                // Submit form to update database and redirect
                                setTimeout(function() {
                                    // Create a form to submit
                                    const form = document.createElement('form');
                                    form.method = 'POST';
                                    form.action =
                                        '{{ route('customer.rent-powerbank') }}';

                                    // Add CSRF token
                                    const csrfToken = document.createElement('input');
                                    csrfToken.type = 'hidden';
                                    csrfToken.name = '_token';
                                    csrfToken.value = '{{ csrf_token() }}';
                                    form.appendChild(csrfToken);

                                    // Add powerbank ID
                                    const powerbankIdInput = document.createElement(
                                        'input');
                                    powerbankIdInput.type = 'hidden';
                                    powerbankIdInput.name = 'powerbank_id';
                                    powerbankIdInput.value = powerbankId;
                                    form.appendChild(powerbankIdInput);

                                    // Add device SN
                                    const deviceSnInput = document.createElement(
                                        'input');
                                    deviceSnInput.type = 'hidden';
                                    deviceSnInput.name = 'device_sn';
                                    deviceSnInput.value = deviceSn;
                                    form.appendChild(deviceSnInput);

                                    // Add slot number
                                    const slotNumberInput = document.createElement(
                                        'input');
                                    slotNumberInput.type = 'hidden';
                                    slotNumberInput.name = 'slot_number';
                                    slotNumberInput.value = slotNum;
                                    form.appendChild(slotNumberInput);

                                    // Add AIMS
                                    const aimsInput = document.createElement('input');
                                    aimsInput.type = 'hidden';
                                    aimsInput.name = 'aims';
                                    aimsInput.value = aims;
                                    form.appendChild(aimsInput);

                                    // Add customer ID
                                    const customerIdInput = document.createElement(
                                        'input');
                                    customerIdInput.type = 'hidden';
                                    customerIdInput.name = 'customer_id';
                                    customerIdInput.value = '{{ $customer->id }}';
                                    form.appendChild(customerIdInput);

                                    // Add command sent flag
                                    const commandSentInput = document.createElement(
                                        'input');
                                    commandSentInput.type = 'hidden';
                                    commandSentInput.name = 'command_sent';
                                    commandSentInput.value = 'true';
                                    form.appendChild(commandSentInput);

                                    // Append form to body and submit
                                    document.body.appendChild(form);
                                    form.submit();
                                }, 1000);
                            }
                        }, 1000);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error saving rent command:', error);
                        rentalStatusMessage.textContent =
                            'Error sending rent command. Please try again.';

                        // Show error for 3 seconds, then go back to powerbanks list
                        setTimeout(function() {
                            rentalProgressContainer.classList.add('d-none');
                            powerbanksContainer.classList.remove('d-none');
                        }, 3000);
                    }
                });
            }
        });
    </script>
@endpush
