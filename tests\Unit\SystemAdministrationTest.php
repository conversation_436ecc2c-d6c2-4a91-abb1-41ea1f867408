<?php

namespace Tests\Unit;

use Tests\TestCase;

class SystemAdministrationTest extends TestCase
{
    /** @test */
    public function user_activity_controller_exists()
    {
        $this->assertTrue(class_exists('App\Http\Controllers\Admin\UserActivityController'));
    }

    /** @test */
    public function audit_trail_controller_exists()
    {
        $this->assertTrue(class_exists('App\Http\Controllers\Admin\AuditTrailController'));
    }

    /** @test */
    public function database_backup_controller_exists()
    {
        $this->assertTrue(class_exists('App\Http\Controllers\Admin\DatabaseBackupController'));
    }

    /** @test */
    public function user_activity_view_exists()
    {
        $viewPath = resource_path('views/admin/user-activity/index.blade.php');
        $this->assertFileExists($viewPath);
    }

    /** @test */
    public function audit_trail_view_exists()
    {
        $viewPath = resource_path('views/admin/audit-trail/index.blade.php');
        $this->assertFileExists($viewPath);
    }

    /** @test */
    public function database_backup_view_exists()
    {
        $viewPath = resource_path('views/admin/backups/index.blade.php');
        $this->assertFileExists($viewPath);
    }

    /** @test */
    public function user_model_has_activities_relationship()
    {
        $user = new \App\Models\User();
        $this->assertTrue(method_exists($user, 'activities'));
    }

    /** @test */
    public function user_model_has_audit_trails_relationship()
    {
        $user = new \App\Models\User();
        $this->assertTrue(method_exists($user, 'auditTrails'));
    }

    /** @test */
    public function user_model_has_database_backups_relationship()
    {
        $user = new \App\Models\User();
        $this->assertTrue(method_exists($user, 'databaseBackups'));
    }

    /** @test */
    public function admin_routes_are_defined()
    {
        $routes = [
            'admin.user-activity.index',
            'admin.user-activity.show',
            'admin.audit-trail.index',
            'admin.audit-trail.show',
            'admin.backups.index',
            'admin.backups.create',
            'admin.backups.download',
            'admin.backups.destroy',
        ];

        foreach ($routes as $routeName) {
            try {
                $url = route($routeName, ['id' => 1]); // Use dummy ID for routes that need it
                $this->assertNotEmpty($url);
            } catch (\Exception $e) {
                if (strpos($e->getMessage(), 'Missing required parameter') !== false) {
                    // This is expected for routes that require parameters
                    $this->assertTrue(true);
                } else {
                    $this->fail("Route {$routeName} is not defined: " . $e->getMessage());
                }
            }
        }
    }

    /** @test */
    public function sidebar_contains_system_administration_menu()
    {
        $sidebarPath = resource_path('views/include/sidebar.blade.php');
        $this->assertFileExists($sidebarPath);
        
        $sidebarContent = file_get_contents($sidebarPath);
        $this->assertStringContainsString('System Administration', $sidebarContent);
        $this->assertStringContainsString('User Activity', $sidebarContent);
        $this->assertStringContainsString('Audit Trail', $sidebarContent);
        $this->assertStringContainsString('Database Backups', $sidebarContent);
    }

    /** @test */
    public function permission_middleware_is_registered()
    {
        $kernel = app(\Illuminate\Contracts\Http\Kernel::class);
        
        // Use reflection to access protected property
        $reflection = new \ReflectionClass($kernel);
        $property = $reflection->getProperty('middlewareAliases');
        $property->setAccessible(true);
        $middlewareAliases = $property->getValue($kernel);
        
        $this->assertArrayHasKey('permission', $middlewareAliases);
        $this->assertEquals(\App\Http\Middleware\CheckPermission::class, $middlewareAliases['permission']);
    }

    /** @test */
    public function models_exist()
    {
        $models = [
            'App\Models\UserActivity',
            'App\Models\AuditTrail',
            'App\Models\DatabaseBackup',
        ];

        foreach ($models as $model) {
            $this->assertTrue(class_exists($model), "Model {$model} does not exist");
        }
    }

    /** @test */
    public function controllers_have_required_methods()
    {
        $controllers = [
            'App\Http\Controllers\Admin\UserActivityController' => ['index', 'show'],
            'App\Http\Controllers\Admin\AuditTrailController' => ['index', 'show'],
            'App\Http\Controllers\Admin\DatabaseBackupController' => ['index', 'create', 'download', 'destroy'],
        ];

        foreach ($controllers as $controller => $methods) {
            $this->assertTrue(class_exists($controller), "Controller {$controller} does not exist");
            
            $reflection = new \ReflectionClass($controller);
            foreach ($methods as $method) {
                $this->assertTrue($reflection->hasMethod($method), "Method {$method} does not exist in {$controller}");
            }
        }
    }
}
