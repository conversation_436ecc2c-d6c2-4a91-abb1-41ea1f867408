<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('powerbank_rentals', function (Blueprint $table) {
            if (!Schema::hasColumn('powerbank_rentals', 'aims')) {
                $table->integer('aims')->default(0)->after('rental_fee');
            }
            if (!Schema::hasColumn('powerbank_rentals', 'slot_number')) {
                $table->integer('slot_number')->nullable()->after('aims');
            }
            if (!Schema::hasColumn('powerbank_rentals', 'device_id')) {
                $table->foreignId('device_id')->nullable()->constrained()->onDelete('set null')->after('powerbank_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('powerbank_rentals', function (Blueprint $table) {
            if (Schema::hasColumn('powerbank_rentals', 'aims')) {
                $table->dropColumn('aims');
            }
            if (Schema::hasColumn('powerbank_rentals', 'slot_number')) {
                $table->dropColumn('slot_number');
            }
            if (Schema::hasColumn('powerbank_rentals', 'device_id')) {
                $table->dropForeign(['device_id']);
                $table->dropColumn('device_id');
            }
        });
    }
};
