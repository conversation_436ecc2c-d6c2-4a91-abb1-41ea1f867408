<?php

namespace Database\Seeders;

use App\Models\AdPlan;
use App\Models\AdMaterial;
use Illuminate\Database\Seeder;

class AdSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 10 ad materials (6 images, 4 videos)
        AdMaterial::factory()->image()->count(6)->create();
        AdMaterial::factory()->video()->count(4)->create();

        // Create 5 ad plans
        AdPlan::factory()->count(5)->create()->each(function ($adPlan) {
            // Get random materials (between 2 and 5)
            $materials = AdMaterial::inRandomOrder()
                ->limit(fake()->numberBetween(2, 5))
                ->get();

            // Attach materials to the plan with display order and time
            $displayOrder = 1;
            foreach ($materials as $material) {
                $adPlan->adMaterials()->attach($material->id, [
                    'display_order' => $displayOrder++,
                    'display_time' => $material->type === 'image'
                        ? fake()->numberBetween(5, 15)
                        : 0, // Use 0 for videos instead of null
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        });

        // Create one active plan for current time
        $currentHour = now()->format('H');
        $adPlan = AdPlan::factory()->create([
            'name' => 'Current Active Campaign',
            'start_time' => sprintf('%02d:00', $currentHour - 1 < 0 ? 0 : $currentHour - 1),
            'end_time' => sprintf('%02d:59', $currentHour + 1 > 23 ? 23 : $currentHour + 1),
            'is_active' => true,
            'priority' => 10,
        ]);

        // Attach materials to the current active plan
        $materials = AdMaterial::inRandomOrder()->limit(3)->get();
        $displayOrder = 1;
        foreach ($materials as $material) {
            $adPlan->adMaterials()->attach($material->id, [
                'display_order' => $displayOrder++,
                'display_time' => $material->type === 'image' ? 8 : 0, // Use 0 for videos
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
