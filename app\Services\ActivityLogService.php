<?php

namespace App\Services;

use App\Models\UserActivity;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class ActivityLogService
{
    /**
     * Log a user activity
     *
     * @param string $action The action performed (e.g., 'create', 'update', 'delete')
     * @param string $module The module or entity affected (e.g., 'user', 'role', 'product')
     * @param string $description A human-readable description of the activity
     * @param array $properties Additional properties to store with the activity
     * @return UserActivity
     */
    public static function log(string $action, string $module, string $description, array $properties = []): UserActivity
    {
        return UserActivity::create([
            'user_id' => Auth::id(),
            'action' => $action,
            'module' => $module,
            'description' => $description,
            'properties' => !empty($properties) ? json_encode($properties) : null,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
        ]);
    }

    /**
     * Log a user login
     *
     * @return UserActivity
     */
    public static function logLogin(): UserActivity
    {
        $user = Auth::user();
        return self::log(
            'login',
            'authentication',
            "User {$user->name} logged in",
            [
                'email' => $user->email,
            ]
        );
    }

    /**
     * Log a user logout
     *
     * @return UserActivity
     */
    public static function logLogout(): UserActivity
    {
        $user = Auth::user();
        return self::log(
            'logout',
            'authentication',
            "User {$user->name} logged out",
            [
                'email' => $user->email,
            ]
        );
    }

    /**
     * Log a failed login attempt
     *
     * @param string $email The email that was used in the failed attempt
     * @return UserActivity
     */
    public static function logFailedLogin(string $email): UserActivity
    {
        return UserActivity::create([
            'user_id' => null, // No user ID since login failed
            'action' => 'failed_login',
            'module' => 'authentication',
            'description' => "Failed login attempt for email: {$email}",
            'properties' => json_encode(['email' => $email]),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
        ]);
    }

    /**
     * Log a model creation
     *
     * @param string $modelName The name of the model
     * @param mixed $model The model instance
     * @param string $identifierField The field to use as identifier (default: 'id')
     * @return UserActivity
     */
    public static function logCreated(string $modelName, $model, string $identifierField = 'id'): UserActivity
    {
        $identifier = $model->{$identifierField};
        return self::log(
            'create',
            strtolower(class_basename($model)),
            "Created new {$modelName}: {$identifier}",
            [
                'model_id' => $model->id,
                'model_type' => get_class($model),
                'attributes' => $model->getAttributes(),
            ]
        );
    }

    /**
     * Log a model update
     *
     * @param string $modelName The name of the model
     * @param mixed $model The model instance
     * @param array $changed The changed attributes
     * @param string $identifierField The field to use as identifier (default: 'id')
     * @return UserActivity
     */
    public static function logUpdated(string $modelName, $model, array $changed, string $identifierField = 'id'): UserActivity
    {
        $identifier = $model->{$identifierField};
        return self::log(
            'update',
            strtolower(class_basename($model)),
            "Updated {$modelName}: {$identifier}",
            [
                'model_id' => $model->id,
                'model_type' => get_class($model),
                'changed' => $changed,
            ]
        );
    }

    /**
     * Log a model deletion
     *
     * @param string $modelName The name of the model
     * @param mixed $model The model instance
     * @param string $identifierField The field to use as identifier (default: 'id')
     * @return UserActivity
     */
    public static function logDeleted(string $modelName, $model, string $identifierField = 'id'): UserActivity
    {
        $identifier = $model->{$identifierField};
        return self::log(
            'delete',
            strtolower(class_basename($model)),
            "Deleted {$modelName}: {$identifier}",
            [
                'model_id' => $model->id,
                'model_type' => get_class($model),
                'attributes' => $model->getAttributes(),
            ]
        );
    }
}
