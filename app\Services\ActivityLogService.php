<?php

namespace App\Services;

use App\Models\UserActivity;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class ActivityLogService
{
    /**
     * Log user activity
     *
     * @param string $action
     * @param string $module
     * @param string|null $description
     * @param array|null $properties
     * @return UserActivity
     */
    public static function log($action, $module, $description = null, $properties = null)
    {
        return UserActivity::create([
            'user_id' => Auth::id(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'action' => $action,
            'module' => $module,
            'description' => $description,
            'properties' => $properties,
        ]);
    }

    /**
     * Log login activity
     *
     * @param string $description
     * @return UserActivity
     */
    public static function logLogin($description = 'User logged in')
    {
        return self::log('login', 'authentication', $description);
    }

    /**
     * Log logout activity
     *
     * @param string $description
     * @return UserActivity
     */
    public static function logLogout($description = 'User logged out')
    {
        return self::log('logout', 'authentication', $description);
    }

    /**
     * Log create activity
     *
     * @param string $module
     * @param string $description
     * @param array|null $properties
     * @return UserActivity
     */
    public static function logCreate($module, $description, $properties = null)
    {
        return self::log('create', $module, $description, $properties);
    }

    /**
     * Log update activity
     *
     * @param string $module
     * @param string $description
     * @param array|null $properties
     * @return UserActivity
     */
    public static function logUpdate($module, $description, $properties = null)
    {
        return self::log('update', $module, $description, $properties);
    }

    /**
     * Log delete activity
     *
     * @param string $module
     * @param string $description
     * @param array|null $properties
     * @return UserActivity
     */
    public static function logDelete($module, $description, $properties = null)
    {
        return self::log('delete', $module, $description, $properties);
    }
}