<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});


// Customer Registration Routes (accessible without authentication)
Route::get('/customer/register', [App\Http\Controllers\CustomerController::class, 'showRegistrationForm'])->name('customer.register');
Route::post('/customer/register', [App\Http\Controllers\CustomerController::class, 'register'])->name('customer.register.post');
Route::get('/customer/verify-otp', [App\Http\Controllers\CustomerController::class, 'showOtpVerificationForm'])->name('customer.verify-otp');
Route::post('/customer/verify-otp', [App\Http\Controllers\CustomerController::class, 'verifyOtp'])->name('customer.verify-otp.submit');
Route::post('/customer/resend-otp', [App\Http\Controllers\CustomerController::class, 'resendOtp'])->name('customer.resend-otp');
Route::get('/customer/payment-verification', [App\Http\Controllers\CustomerController::class, 'showPaymentVerificationForm'])->name('customer.payment-verification');
Route::post('/customer/payment-verification', [App\Http\Controllers\CustomerController::class, 'verifyPayment'])->name('customer.verify-payment');
Route::get('/customer/verify-payment/complete', [App\Http\Controllers\CustomerController::class, 'completePaymentVerification'])->name('customer.verify-payment.complete');
Route::get('/customer/registration-complete', [App\Http\Controllers\CustomerController::class, 'showRegistrationComplete'])->name('customer.registration-complete');
Route::get('/customer/direct-register', [App\Http\Controllers\CustomerController::class, 'directRegister'])->name('customer.direct-register');

// Customer Login Routes (accessible without authentication)
Route::get('/customer/login', [App\Http\Controllers\CustomerController::class, 'showLoginForm'])->name('customer.login');
Route::post('/customer/login', [App\Http\Controllers\CustomerController::class, 'login'])->name('customer.login.submit');
Route::post('/customer/logout', [App\Http\Controllers\CustomerController::class, 'logout'])->name('customer.logout');

// Customer Lease Form (accessible without authentication)
Route::get('/Lease', [App\Http\Controllers\CustomerController::class, 'showLeaseForm'])->name('customer.lease');

// Payment processing routes (accessible without authentication)
Route::post('/customer/create-paypal-order', [App\Http\Controllers\CustomerController::class, 'createPayPalOrder'])
    ->name('customer.create-paypal-order');
Route::get('/customer/verify-payment/callback', [App\Http\Controllers\CustomerController::class, 'handlePayPalCallback'])
    ->name('customer.verify-payment.callback');

// Rental success page (accessible without authentication for redirect after rental)
Route::get('/customer/rental-success', [App\Http\Controllers\CustomerController::class, 'rentalSuccess'])
    ->name('customer.rental-success');

// Customer authenticated routes
Route::middleware(['customer.auth'])->group(function () {
    Route::get('/powerbanks/available', [App\Http\Controllers\PowerbankController::class, 'available'])
        ->name('powerbanks.available');
    Route::get('/customer/available-powerbanks', [App\Http\Controllers\CustomerController::class, 'getAvailablePowerbanks'])
        ->name('customer.available-powerbanks');
    Route::post('/customer/rent-powerbank', [App\Http\Controllers\CustomerController::class, 'rentPowerbank'])
        ->name('customer.rent-powerbank');
    Route::get('/customer/logout', [App\Http\Controllers\CustomerController::class, 'logout'])
        ->name('customer.logout');
});

// Admin routes - protected by auth middleware
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard and admin panel routes
    Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])
        ->middleware('permission:view-dashboard')->name('dashboard');

    // Admin-only routes
    Route::middleware('auth')->group(function () {
        // Profile routes
        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

        // Resource routes for admin management with permissions
        Route::resource('device', App\Http\Controllers\DeviceController::class)
            ->middleware('permission:view-devices');
        Route::resource('admaterial', App\Http\Controllers\AdMaterialController::class)
            ->middleware('permission:view-ad-materials');
        Route::resource('adplan', App\Http\Controllers\AdController::class)
            ->middleware('permission:view-ad-plans');

        // Admin customer management routes with permissions
        Route::resource('customer', App\Http\Controllers\CustomerController::class)
            ->middleware('permission:view-customers')
            ->names([
                'index' => 'customer.index',
                'create' => 'customer.create',
                'store' => 'customer.store',
                'show' => 'customer.show',
                'edit' => 'customer.edit',
                'update' => 'customer.update',
                'destroy' => 'customer.destroy',
            ]);
        Route::get('customer/registrations', [App\Http\Controllers\CustomerController::class, 'registrations'])
            ->middleware('permission:view-customer-registrations')->name('customer.registrations');
        Route::get('customer/reports', [App\Http\Controllers\CustomerController::class, 'reports'])
            ->middleware('permission:view-customer-reports')->name('customer.reports');
        Route::post('/customer/send-reminder', [App\Http\Controllers\CustomerController::class, 'sendReminder'])
            ->middleware('permission:send-customer-reminders')->name('customer.send-reminder');

        Route::resource('user', App\Http\Controllers\UserController::class);
    });

    // Device Data Routes with permissions
    Route::middleware(['auth', 'permission:view-device-data'])->group(function () {
        Route::get('/device-data', [App\Http\Controllers\DeviceDataController::class, 'index'])->name('device-data.index');
        Route::get('/device-data/{id}', [App\Http\Controllers\DeviceDataController::class, 'show'])->name('device-data.show');
        Route::get('/device-data/device/{sn}', [App\Http\Controllers\DeviceDataController::class, 'getByDeviceSn'])->name('device-data.by-device');
    });

    Route::delete('/device-data/{id}', [App\Http\Controllers\DeviceDataController::class, 'destroy'])
        ->middleware(['auth', 'permission:delete-device-data'])->name('device-data.destroy');

    // Device Group Web Routes with permissions
    Route::middleware(['auth', 'permission:view-device-groups'])->group(function () {
        // Basic CRUD routes
        Route::resource('devicegroups', App\Http\Controllers\DeviceGroupController::class);
    });

    // Additional routes for device and plan assignment
    Route::get('/devicegroups/{devicegroup}/assign-devices', [App\Http\Controllers\DeviceGroupController::class, 'showAssignDevices'])
        ->middleware(['auth', 'permission:assign-devices-groups'])->name('devicegroup.assign.devices.show');
    Route::post('/devicegroups/{devicegroup}/assign-devices', [App\Http\Controllers\DeviceGroupController::class, 'assignDevices'])
        ->middleware(['auth', 'permission:assign-devices-groups'])->name('devicegroup.assign.devices');
    Route::get('/devicegroups/{devicegroup}/assign-plans', [App\Http\Controllers\DeviceGroupController::class, 'showAssignPlans'])
        ->middleware(['auth', 'permission:assign-plans-groups'])->name('devicegroup.assign.plans.show');
    Route::post('/devicegroups/{devicegroup}/assign-plans', [App\Http\Controllers\DeviceGroupController::class, 'assignPlans'])
        ->middleware(['auth', 'permission:assign-plans-groups'])->name('devicegroup.assign.plans');

    // Powerbank routes with permissions
    Route::resource('powerbanks', App\Http\Controllers\PowerbankController::class)
        ->middleware('permission:view-powerbanks');
    Route::get('/devices/{device}/powerbanks', [App\Http\Controllers\PowerbankController::class, 'byDevice'])
        ->middleware('permission:view-powerbanks')->name('powerbanks.by-device');
    Route::post('/powerbanks/{powerbank}/rent', [App\Http\Controllers\PowerbankController::class, 'rent'])
        ->middleware('permission:rent-powerbanks')->name('powerbanks.rent');
    Route::post('/powerbanks/{powerbank}/return', [App\Http\Controllers\PowerbankController::class, 'returnPowerbank'])
        ->middleware('permission:return-powerbanks')->name('powerbanks.return');
    Route::post('/powerbanks/{powerbank}/mark-charged', [App\Http\Controllers\PowerbankController::class, 'markCharged'])
        ->middleware('permission:mark-powerbanks-charged')->name('powerbanks.mark-charged');
    Route::post('/powerbanks/{id}/report-issue', [App\Http\Controllers\PowerbankController::class, 'reportIssue'])
        ->middleware('permission:report-powerbank-issues')->name('powerbanks.report-issue');
    Route::post('/powerbanks/issues/{id}/resolve', [App\Http\Controllers\PowerbankController::class, 'resolveIssue'])
        ->middleware('permission:resolve-powerbank-issues')->name('powerbanks.resolve-issue');
    Route::get('/powerbanks/{id}/issues', [App\Http\Controllers\PowerbankController::class, 'issues'])
        ->middleware('permission:view-powerbank-issues')->name('powerbanks.issues');
    Route::get('/powerbanks-issues', [App\Http\Controllers\PowerbankController::class, 'allIssues'])
        ->middleware('permission:view-powerbank-issues')->name('powerbanks.issues.index');
    Route::resource('rentals', App\Http\Controllers\RentalController::class)
        ->middleware('permission:view-rentals');

    // Device control routes with permissions
    Route::post('/devices/eject-all', [App\Http\Controllers\DeviceController::class, 'ejectAllPowerbanks'])
        ->middleware('permission:control-devices')->name('devices.eject-all');
    Route::post('/devices/reboot', [App\Http\Controllers\DeviceController::class, 'rebootDevice'])
        ->middleware('permission:reboot-devices')->name('devices.reboot');
    Route::post('/powerbanks/eject', [App\Http\Controllers\PowerbankController::class, 'ejectPowerbank'])
        ->middleware('permission:eject-powerbanks')->name('powerbanks.eject');

    // Device command routes with permissions
    Route::post('/device-commands', [App\Http\Controllers\DeviceCommandController::class, 'store'])
        ->middleware('permission:send-device-commands')->name('device-commands.store');
});

require __DIR__ . '/auth.php';

// Two-Factor Authentication Routes
Route::middleware('auth')->group(function () {
    Route::get('/two-factor/setup', [App\Http\Controllers\Auth\TwoFactorAuthController::class, 'setup'])
        ->name('two-factor.setup');
    Route::post('/two-factor/enable', [App\Http\Controllers\Auth\TwoFactorAuthController::class, 'enable'])
        ->name('two-factor.enable');
    Route::post('/two-factor/disable', [App\Http\Controllers\Auth\TwoFactorAuthController::class, 'disable'])
        ->name('two-factor.disable');
    Route::get('/two-factor/manage', [App\Http\Controllers\Auth\TwoFactorAuthController::class, 'manage'])
        ->name('two-factor.manage');
});

// These routes should be outside the auth middleware
Route::get('/two-factor/challenge', [App\Http\Controllers\Auth\TwoFactorAuthController::class, 'challenge'])
    ->name('two-factor.challenge');
Route::post('/two-factor/verify', [App\Http\Controllers\Auth\TwoFactorAuthController::class, 'verify'])
    ->name('two-factor.verify');

// Advertisement Plan routes
Route::get('/adplan/{adplan}/preview', [App\Http\Controllers\AdController::class, 'preview'])
    ->name('adplan.preview');

// Device powerbanks status route for AJAX refresh
Route::get('/devices/{id}/powerbanks-status', [App\Http\Controllers\DeviceController::class, 'getPowerbanksStatus'])
    ->name('devices.powerbanks-status');

// Customer lease routes
Route::get('/lease', [App\Http\Controllers\CustomerController::class, 'lease'])
    ->name('customer.lease');

// Customer rent powerbank route
Route::post('/rent-powerbank', [App\Http\Controllers\CustomerController::class, 'rentPowerbank'])
    ->name('customer.rent-powerbank');

// Advanced User Management Routes
Route::middleware(['auth'])->group(function () {
    // User CRUD with permissions
    Route::get('users', [App\Http\Controllers\UserController::class, 'index'])
        ->middleware('permission:view-users')->name('user.index');
    Route::get('users/create', [App\Http\Controllers\UserController::class, 'create'])
        ->middleware('permission:create-users')->name('user.create');
    Route::post('users', [App\Http\Controllers\UserController::class, 'store'])
        ->middleware('permission:create-users')->name('user.store');
    Route::get('users/{user}', [App\Http\Controllers\UserController::class, 'show'])
        ->middleware('permission:view-users')->name('user.show');
    Route::get('users/{user}/edit', [App\Http\Controllers\UserController::class, 'edit'])
        ->middleware('permission:edit-users')->name('user.edit');
    Route::put('users/{user}', [App\Http\Controllers\UserController::class, 'update'])
        ->middleware('permission:edit-users')->name('user.update');
    Route::delete('users/{user}', [App\Http\Controllers\UserController::class, 'destroy'])
        ->middleware('permission:delete-users')->name('user.destroy');

    // Advanced user management features
    Route::get('users/{user}/roles', [App\Http\Controllers\UserController::class, 'editRoles'])
        ->middleware('permission:assign-roles')->name('users.roles.edit');
    Route::put('users/{user}/roles', [App\Http\Controllers\UserController::class, 'updateRoles'])
        ->middleware('permission:assign-roles')->name('users.roles.update');
    Route::patch('users/{user}/toggle-status', [App\Http\Controllers\UserController::class, 'toggleStatus'])
        ->middleware('permission:manage-user-status')->name('users.toggle-status');
    Route::post('users/{user}/reset-password', [App\Http\Controllers\UserController::class, 'resetPassword'])
        ->middleware('permission:reset-user-password')->name('users.reset-password');

    Route::post('users/bulk-action', [App\Http\Controllers\UserController::class, 'bulkAction'])
        ->middleware('permission:edit-users')->name('users.bulk-action');
    Route::get('users/export', [App\Http\Controllers\UserController::class, 'export'])
        ->middleware('permission:export-data')->name('users.export');

    // Role and Permission Routes with permissions
    Route::resource('role', App\Http\Controllers\RoleController::class)->middleware('permission:view-roles');
    Route::resource('permission', App\Http\Controllers\PermissionController::class)->middleware('permission:view-permissions');
});



// Admin routes with permissions
Route::prefix('admin')->name('admin.')->middleware(['auth', 'verified', 'track.activity'])->group(function () {


    // Audit Trail routes
    Route::get('/audit-trail', [App\Http\Controllers\Admin\AuditTrailController::class, 'index'])
        ->middleware('permission:view-audit-trail')->name('audit-trail.index');
    Route::get('/audit-trail/{id}', [App\Http\Controllers\Admin\AuditTrailController::class, 'show'])
        ->middleware('permission:view-audit-trail')->name('audit-trail.show');

    // Database Backup routes
    Route::get('/backups', [App\Http\Controllers\Admin\DatabaseBackupController::class, 'index'])
        ->middleware('permission:manage-backups')->name('backups.index');
    Route::get('/backups/create', [App\Http\Controllers\Admin\DatabaseBackupController::class, 'create'])
        ->middleware('permission:manage-backups')->name('backups.create');
    Route::get('/backups/{id}/download', [App\Http\Controllers\Admin\DatabaseBackupController::class, 'download'])
        ->middleware('permission:download-backups')->name('backups.download');
    Route::delete('/backups/{id}', [App\Http\Controllers\Admin\DatabaseBackupController::class, 'destroy'])
        ->middleware('permission:delete-backups')->name('backups.destroy');
});
