<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('view-roles');

        $roles = Role::with('permissions')->get(); // Changed from paginate to get all records for DataTables
        return view('admin.roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create-roles');

        $permissions = Permission::all();
        $groupedPermissions = $this->groupPermissions($permissions);
        return view('admin.roles.create', compact('permissions', 'groupedPermissions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create-roles');

        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:roles'],
            'description' => ['nullable', 'string', 'max:255'],
            'permissions' => ['nullable', 'array'],
        ]);

        $role = Role::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
        ]);

        if ($request->has('permissions')) {
            $role->permissions()->sync($request->permissions);
        }

        return redirect()->route('role.index')
            ->with('success', 'Role created successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $this->authorize('view-roles');

        $role = Role::with('permissions')->findOrFail($id);
        $rolePermissions = $role->permissions;
        return view('admin.roles.show', compact('role', 'rolePermissions'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $this->authorize('edit-roles');

        $role = Role::findOrFail($id);
        $permissions = Permission::all();
        $groupedPermissions = $this->groupPermissions($permissions);
        $rolePermissions = $role->permissions->pluck('id')->toArray();

        return view('admin.roles.edit', compact('role', 'permissions', 'groupedPermissions', 'rolePermissions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $this->authorize('edit-roles');

        $role = Role::findOrFail($id);

        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:roles,name,' . $id],
            'description' => ['nullable', 'string', 'max:255'],
            'permissions' => ['nullable', 'array'],
        ]);

        $role->name = $request->name;
        $role->slug = Str::slug($request->name);
        $role->description = $request->description;
        $role->save();

        if ($request->has('permissions')) {
            $role->permissions()->sync($request->permissions);
        } else {
            $role->permissions()->detach();
        }

        return redirect()->route('role.index')
            ->with('success', 'Role updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->authorize('delete-roles');

        $role = Role::findOrFail($id);

        // Check if role is in use
        if ($role->users()->count() > 0) {
            return redirect()->route('role.index')
                ->with('error', 'Cannot delete role because it is assigned to users');
        }

        $role->permissions()->detach();
        $role->delete();

        return redirect()->route('role.index')
            ->with('success', 'Role deleted successfully');
    }

    /**
     * Group permissions by category for better organization
     */
    private function groupPermissions($permissions)
    {
        $groups = [
            'Dashboard' => [
                'icon' => 'fe-home',
                'description' => 'Dashboard and overview access',
                'permissions' => []
            ],
            'User Management' => [
                'icon' => 'fe-users',
                'description' => 'User accounts and profile management',
                'permissions' => []
            ],
            'Role & Permission Management' => [
                'icon' => 'fe-shield',
                'description' => 'Roles and permissions administration',
                'permissions' => []
            ],
            'Device Management' => [
                'icon' => 'fe-monitor',
                'description' => 'Device operations and monitoring',
                'permissions' => []
            ],
            'Device Group Management' => [
                'icon' => 'fe-layers',
                'description' => 'Device Group operations',
                'permissions' => []
            ],
            'Customer Management' => [
                'icon' => 'fe-user-check',
                'description' => 'Customer accounts and services',
                'permissions' => []
            ],
            'Powerbank Management' => [
                'icon' => 'fe-battery',
                'description' => 'Powerbank operations and maintenance',
                'permissions' => []
            ],
            'Rental Management' => [
                'icon' => 'fe-credit-card',
                'description' => 'Rental transactions and tracking',
                'permissions' => []
            ],
            'Ad Management' => [
                'icon' => 'fe-image',
                'description' => 'Advertisement materials and scheduling',
                'permissions' => []
            ],
            'Ad Schedule' => [
                'icon' => 'fe-calendar',
                'description' => 'Advertisement schedule and management',
                'permissions' => []
            ],
            'System Administration' => [
                'icon' => 'fe-settings',
                'description' => 'System monitoring and administration',
                'permissions' => []
            ],
            'Reports & Analytics' => [
                'icon' => 'fe-bar-chart-2',
                'description' => 'Reports and data analytics',
                'permissions' => []
            ],
            'Security & Profile' => [
                'icon' => 'fe-lock',
                'description' => 'Security settings and user profiles',
                'permissions' => []
            ],
        ];

        foreach ($permissions as $permission) {
            $slug = $permission->slug;
            $assigned = false;

            // Dashboard permissions
            if (str_contains($slug, 'dashboard')) {
                $groups['Dashboard']['permissions'][] = $permission;
                $assigned = true;
            }

            // User management permissions
            elseif (str_contains($slug, 'user') && !str_contains($slug, 'activity')) {
                $groups['User Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Role and permission management
            elseif (str_contains($slug, 'role') || str_contains($slug, 'permission')) {
                $groups['Role & Permission Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Device management
            elseif (str_contains($slug, 'device') && !str_contains($slug, 'device-group')) {
                $groups['Device Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Device Group Management
            elseif (str_contains($slug, 'device-group')) {
                $groups['Device Group Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Customer management
            elseif (str_contains($slug, 'customer')) {
                $groups['Customer Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Powerbank management
            elseif (str_contains($slug, 'powerbank')) {
                $groups['Powerbank Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Rental management
            elseif (str_contains($slug, 'rental') || str_contains($slug, 'rent')) {
                $groups['Rental Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Ad management
            elseif (str_contains($slug, 'ad-') || str_contains($slug, 'material') || str_contains($slug, 'plan')) {
                $groups['Ad Management']['permissions'][] = $permission;
                $assigned = true;
            }

            elseif (str_contains($slug, 'ad-schedule')) {
                $groups['Ad Schedule']['permissions'][] = $permission;
                $assigned = true;
            }

            // System administration
            elseif (str_contains($slug, 'activity') || str_contains($slug, 'audit') || str_contains($slug, 'backup') || str_contains($slug, 'system') || str_contains($slug, 'log')) {
                $groups['System Administration']['permissions'][] = $permission;
                $assigned = true;
            }

            // Reports and analytics
            elseif (str_contains($slug, 'report') || str_contains($slug, 'export') || str_contains($slug, 'analytics')) {
                $groups['Reports & Analytics']['permissions'][] = $permission;
                $assigned = true;
            }

            // Security and profile
            elseif (str_contains($slug, 'profile') || str_contains($slug, '2fa') || str_contains($slug, 'password')) {
                $groups['Security & Profile']['permissions'][] = $permission;
                $assigned = true;
            }

            // If not assigned to any group, put in System Administration as fallback
            if (!$assigned) {
                $groups['System Administration']['permissions'][] = $permission;
            }
        }

        // Remove empty groups
        return array_filter($groups, function ($group) {
            return !empty($group['permissions']);
        });
    }
}
