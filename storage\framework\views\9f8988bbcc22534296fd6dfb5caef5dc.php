<?php $__env->startSection('content'); ?>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Customer Login</h4>
                    </div>
                    <div class="card-body p-4">
                        <p class="text-muted mb-4">Enter your mobile number to receive a WhatsApp OTP</p>
                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        <form action="<?php echo e(route('customer.login.mobile.submit')); ?>" method="POST" class="needs-validation"
                            novalidate id="loginForm">
                            <?php echo csrf_field(); ?>
                            <!-- Contact Number with Country Code -->
                            <div class="col-12 col-md-6">
                                <label for="phone" class="form-label">Phone Number:</label>
                                <input id="phone" type="tel" name="contact_no"
                                    class="form-control form-control-lg phonenum" maxlength="80" placeholder="Phone Number"
                                    value="<?php echo e(old('contact_no')); ?>" required>
                                <div class="form-text">We'll send a verification code to this phone number</div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    Send OTP <i class="bi bi-whatsapp"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <p class="text-muted">
                        Don't have an account? <a href="<?php echo e(route('customer.register')); ?>"
                            class="text-decoration-none">Register now</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        // COUNTRY CODE
        const inputs = document.querySelectorAll(".phonenum");

        inputs.forEach(input => {
            window.intlTelInput(input, {
                initialCountry: "pk",
                separateDialCode: true,
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js", // For formatting
            });
        });
        // COUNTRY CODE
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.customer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\websocket-api\resources\views/customers/login-mobile.blade.php ENDPATH**/ ?>