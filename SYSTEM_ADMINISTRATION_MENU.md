# System Administration Menu

## Overview

The System Administration menu provides comprehensive tools for monitoring, auditing, and managing the WebSocket API application. This menu is accessible only to users with appropriate permissions and includes three main sections:

1. **User Activity** - Monitor and track user actions
2. **Audit Trail** - Track data changes and system modifications
3. **Database Backups** - Manage database backup files

## Menu Structure

### 📊 **System Administration**
```
System Administration
├── User Activity
├── Audit Trail
└── Database Backups
```

## Features

### 🔍 **User Activity**
**Route:** `/admin/user-activity`  
**Permission:** `view-user-activity`

#### **Features:**
- **Real-time Activity Monitoring** - Track all user actions in the system
- **Advanced Filtering** - Filter by user, action, module, date range
- **Search Functionality** - Search across descriptions, actions, and user details
- **Statistics Dashboard** - View total activities, today's activities, active users, weekly trends
- **Export to CSV** - Export filtered activity data
- **Auto-refresh** - Page refreshes every 30 seconds for real-time monitoring

#### **Statistics Cards:**
- **Total Activities** - All recorded user activities
- **Today's Activities** - Activities from today
- **Active Users** - Users active in the last 7 days
- **This Week** - Activities from the past 7 days

#### **Activity Types Tracked:**
- Login/Logout events
- Create, Update, Delete operations
- View actions
- System interactions
- Device operations
- Customer management actions

#### **Data Displayed:**
- User information with avatar
- Action type with color-coded badges
- Module/section affected
- Detailed description
- IP address and user agent
- Timestamp with relative time

### 📋 **Audit Trail**
**Route:** `/admin/audit-trail`  
**Permission:** `view-audit-trail`

#### **Features:**
- **Data Change Tracking** - Monitor all database modifications
- **Model-specific Filtering** - Filter by specific data models
- **Change Comparison** - View before/after values in modal dialogs
- **Advanced Search** - Search across all audit fields
- **Statistics Dashboard** - View audit metrics and trends
- **Export Functionality** - Export audit data for compliance
- **Auto-refresh** - Updates every 60 seconds

#### **Statistics Cards:**
- **Total Records** - All audit trail entries
- **Today's Records** - Audit entries from today
- **Data Changes** - Number of update/delete operations
- **Active Users** - Users who made changes in the last 7 days

#### **Audit Information:**
- User who made the change
- Action performed (Created/Updated/Deleted)
- Model type and record ID
- Old and new values comparison
- IP address tracking
- Timestamp with relative time

#### **Change Viewer:**
- Side-by-side comparison of old vs new values
- JSON formatted display
- Modal popup for detailed view
- Syntax highlighting for better readability

### 💾 **Database Backups**
**Route:** `/admin/backups`  
**Permission:** `manage-backups`, `download-backups`, `delete-backups`

#### **Features:**
- **Backup Creation** - Create database backups on-demand
- **Backup Management** - View, download, and delete backup files
- **File Status Monitoring** - Check if backup files exist on disk
- **Statistics Dashboard** - Monitor backup metrics
- **User Tracking** - Track who created each backup
- **Auto-refresh** - Updates every 30 seconds

#### **Statistics Cards:**
- **Total Backups** - Number of backup files
- **Total Size** - Combined size of all backups
- **Latest Backup** - When the last backup was created
- **Success Rate** - Backup success percentage

#### **Backup Operations:**
- **Create Backup** - Generate new database backup
- **Download Backup** - Download backup files
- **Delete Backup** - Remove backup files and records
- **File Verification** - Check if backup files exist

#### **Backup Information:**
- Filename and path
- File size (human-readable format)
- Creator information
- Creation timestamp
- File availability status

## Permissions Required

### **User Activity**
- `view-user-activity` - Access to user activity logs

### **Audit Trail**
- `view-audit-trail` - Access to audit trail records

### **Database Backups**
- `manage-backups` - Create and manage backups
- `download-backups` - Download backup files
- `delete-backups` - Delete backup files

## Navigation

### **Sidebar Menu**
The System Administration menu appears in the main sidebar navigation and is only visible to users with the required permissions:

```html
System Administration
├── User Activity
├── Audit Trail
└── Database Backups
```

### **Breadcrumb Navigation**
Each page includes breadcrumb navigation:
- Dashboard > System Administration > [Page Name]

## Technical Implementation

### **Controllers**
- `UserActivityController` - Handles user activity display and filtering
- `AuditTrailController` - Manages audit trail viewing and filtering
- `DatabaseBackupController` - Controls backup operations

### **Models**
- `UserActivity` - Stores user action logs
- `AuditTrail` - Stores data change records
- `DatabaseBackup` - Manages backup file records

### **Views**
- `admin.user-activity.index` - User activity listing
- `admin.audit-trail.index` - Audit trail listing
- `admin.backups.index` - Database backup management

### **Routes**
```php
// User Activity
Route::get('/admin/user-activity', [UserActivityController::class, 'index']);
Route::get('/admin/user-activity/{id}', [UserActivityController::class, 'show']);

// Audit Trail
Route::get('/admin/audit-trail', [AuditTrailController::class, 'index']);
Route::get('/admin/audit-trail/{id}', [AuditTrailController::class, 'show']);

// Database Backups
Route::get('/admin/backups', [DatabaseBackupController::class, 'index']);
Route::get('/admin/backups/create', [DatabaseBackupController::class, 'create']);
Route::get('/admin/backups/{id}/download', [DatabaseBackupController::class, 'download']);
Route::delete('/admin/backups/{id}', [DatabaseBackupController::class, 'destroy']);
```

## Security Features

### **Permission-Based Access**
- All routes protected with appropriate permissions
- Menu items only visible to authorized users
- Role-based access control

### **Activity Logging**
- All administrative actions are logged
- IP address and user agent tracking
- Audit trail for compliance

### **Data Protection**
- Secure backup file handling
- Protected download endpoints
- Safe file deletion with verification

## Usage Examples

### **Monitoring User Activity**
1. Navigate to System Administration > User Activity
2. Use filters to find specific activities
3. Export data for reporting
4. View detailed activity information

### **Reviewing Data Changes**
1. Go to System Administration > Audit Trail
2. Filter by model type or user
3. Click "View Changes" to see modifications
4. Export audit data for compliance

### **Managing Backups**
1. Access System Administration > Database Backups
2. Click "Create Backup" for new backup
3. Download existing backups as needed
4. Delete old backups to save space

## Benefits

### **For Administrators**
- Complete visibility into system usage
- Data change tracking for compliance
- Reliable backup management
- Security monitoring capabilities

### **For Compliance**
- Detailed audit trails
- User activity logs
- Data export capabilities
- Change tracking records

### **For Security**
- User behavior monitoring
- Suspicious activity detection
- Access pattern analysis
- System integrity verification

This comprehensive System Administration menu provides enterprise-level monitoring, auditing, and backup capabilities for the WebSocket API application.
