<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // System Administration permissions
        Permission::firstOrCreate(['slug' => 'view-user-activity'], [
            'name' => 'View User Activity',
            'description' => 'Can view user activity logs'
        ]);

        Permission::firstOrCreate(['slug' => 'manage-user-activity'], [
            'name' => 'Manage User Activity',
            'description' => 'Can manage user activity logs (clear, export)'
        ]);

        Permission::firstOrCreate(['slug' => 'view-audit-trail'], [
            'name' => 'View Audit Trail',
            'description' => 'Can view audit trail records'
        ]);

        Permission::firstOrCreate(['slug' => 'manage-backups'], [
            'name' => 'Manage Backups',
            'description' => 'Can create and manage database backups'
        ]);

        Permission::firstOrCreate(['slug' => 'download-backups'], [
            'name' => 'Download Backups',
            'description' => 'Can download database backup files'
        ]);

        Permission::firstOrCreate(['slug' => 'delete-backups'], [
            'name' => 'Delete Backups',
            'description' => 'Can delete database backup files'
        ]);
    }
}
