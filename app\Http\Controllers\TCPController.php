<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Console\Commands\TCPServer;

class TCPController extends Controller
{
    public function rent(Request $request)
    {
        $data = [
            "cmd" => "rent",
            "msg" => rand(1000, 9999),
            "sn" => $request->input('sn'),
            "data" => [
                "n" => $request->input('n'),
                "sn" => $request->input('power_bank_sn'),
            ]
        ];

        $response = $this->sendTcpRequest($data);
        return response()->json($response);
    }

    public function sendCommand(Request $request)
    {
        $sn = $request->input('sn');
        $command = [
            "cmd" => $request->input('cmd'),
            "msg" => rand(1000, 9999),
            "data" => $request->input('data', [])
        ];

        if (TCPServer::sendCommandToDevice($sn, $command)) {
            return response()->json(['status' => 'success', 'message' => "Command sent to device: $sn"]);
        }

        return response()->json(['status' => 'error', 'message' => "Device $sn not connected"], 400);
    }


    private function TCPsendTcpRequest($data)
    {
        $server = '127.0.0.1'; // Change to your TCP server IP
        $port = 8089; // TCP server port (make sure this is NOT an SSL port)

        $socket = @fsockopen("tcp://$server", $port, $errno, $errstr, 5);
        if (!$socket) {
            return ['error' => "TCP Connection failed: $errstr ($errno)"];
        }

        $message = "#*" . json_encode($data) . "*#";
        fwrite($socket, $message);
        $response = fread($socket, 1024);
        fclose($socket);

        return json_decode(trim($response, "#*"), true);
    }


    private function SSLsendTcpRequest($data)
    {
        $server = '127.0.0.1'; // Change to your SSL TCP server IP
        $port = 8089; // The port where TCP SSL is listening (e.g., NGINX stream SSL)

        $socket = @fsockopen("ssl://$server", $port, $errno, $errstr, 5);
        if (!$socket) {
            return ['error' => "TCP SSL Connection failed: $errstr ($errno)"];
        }

        $message = "#*" . json_encode($data) . "*#";
        fwrite($socket, $message);
        $response = fread($socket, 1024);
        fclose($socket);

        return json_decode(trim($response, "#*"), true);
    }
}

// /**
//  * Send a command to a device via TCP
//  *
//  * @param string $deviceSn The device serial number
//  * @param string $command The raw command to send
//  * @return array Response from the TCP server
//  */
// public function sendCommand($deviceSn, $command)
// {
//     try {
//         $server = config('tcp.server_ip', '127.0.0.1');
//         $port = config('tcp.server_port', 8089);

//         // Create socket with timeout
//         $socket = @fsockopen("tcp://$server", $port, $errno, $errstr, 3);

//         if (!$socket) {
//             return [
//                 'success' => false,
//                 'error' => "TCP Connection failed: $errstr ($errno)"
//             ];
//         }

//         // Set socket options
//         stream_set_timeout($socket, 5); // 5 second timeout

//         // Ensure command has proper format
//         if (strpos($command, '#*') !== 0) {
//             $command = "#*" . $command;
//         }
//         if (substr($command, -2) !== '*#') {
//             $command .= "*#";
//         }

//         // Write command to socket
//         $bytesWritten = fwrite($socket, $command);

//         if ($bytesWritten === false || $bytesWritten === 0) {
//             fclose($socket);
//             return [
//                 'success' => false,
//                 'error' => "Failed to write command to socket"
//             ];
//         }

//         // Wait for response with timeout
//         $response = '';
//         $startTime = time();

//         while (time() - $startTime < 5) { // 5 second timeout
//             $data = fread($socket, 4096);

//             if ($data === false) {
//                 break;
//             }

//             if (strlen($data) === 0) {
//                 // No data available yet, wait a bit
//                 usleep(100000); // 100ms
//                 continue;
//             }

//             $response .= $data;

//             // Check if we have a complete response
//             if (strpos($response, '#*') === 0 && substr($response, -2) === '*#') {
//                 break;
//             }
//         }

//         fclose($socket);

//         // Process response
//         if (empty($response)) {
//             return [
//                 'success' => true,
//                 'message' => "Command sent successfully, no response received"
//             ];
//         }

//         // Clean response
//         $cleanResponse = trim($response, "#*");
//         $jsonResponse = json_decode($cleanResponse, true);

//         if ($jsonResponse) {
//             return [
//                 'success' => true,
//                 'data' => $jsonResponse
//             ];
//         }

//         return [
//             'success' => true,
//             'raw_response' => $response
//         ];
//     } catch (\Exception $e) {
//         if (isset($socket) && is_resource($socket)) {
//             fclose($socket);
//         }

//         return [
//             'success' => false,
//             'error' => "Exception: " . $e->getMessage()
//         ];
//     }
// }
