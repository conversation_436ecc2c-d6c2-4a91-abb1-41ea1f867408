<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DatabaseBackup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:backup {--filename= : Custom filename for the backup}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a backup of the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get database configuration
        $dbHost = config('database.connections.mysql.host');
        $dbPort = config('database.connections.mysql.port');
        $dbName = config('database.connections.mysql.database');
        $dbUser = config('database.connections.mysql.username');
        $dbPassword = config('database.connections.mysql.password');

        // Create backup directory if it doesn't exist
        $backupPath = storage_path('app/backups');
        if (!File::exists($backupPath)) {
            File::makeDirectory($backupPath, 0755, true);
        }

        // Set filename
        $filename = $this->option('filename') ?: 'backup_' . Carbon::now()->format('Y-m-d_H-i-s') . '.sql';
        $filePath = $backupPath . '/' . $filename;

        // Build the mysqldump command
        $command = sprintf(
            'mysqldump -h %s -P %s -u %s -p%s %s > %s',
            $dbHost,
            $dbPort,
            $dbUser,
            $dbPassword,
            $dbName,
            $filePath
        );

        // Execute the command
        $returnVar = NULL;
        $output = NULL;
        exec($command, $output, $returnVar);

        if ($returnVar === 0) {
            // Compress the SQL file
            $compressedFilePath = $filePath . '.gz';
            $compressCommand = "gzip -f $filePath";
            exec($compressCommand);

            // Log the backup
            $this->info("Database backup created successfully: $filename.gz");
            Log::info("Database backup created successfully", ['filename' => "$filename.gz"]);

            // Store backup info in database
            \App\Models\DatabaseBackup::create([
                'filename' => "$filename.gz",
                'size' => File::size($compressedFilePath),
                'path' => $compressedFilePath,
                'created_by' => auth()->id() ?? 1,
            ]);

            // Clean up old backups (keep last 10)
            $this->cleanupOldBackups();

            return 0;
        } else {
            $this->error("Database backup failed");
            Log::error("Database backup failed", ['output' => $output]);
            return 1;
        }
    }

    /**
     * Clean up old backups, keeping only the last 10
     */
    protected function cleanupOldBackups()
    {
        $backups = \App\Models\DatabaseBackup::orderBy('created_at', 'desc')
            ->get();

        if ($backups->count() > 10) {
            foreach ($backups->slice(10) as $oldBackup) {
                // Delete the file
                if (File::exists($oldBackup->path)) {
                    File::delete($oldBackup->path);
                }

                // Delete the record
                $oldBackup->delete();
            }

            $this->info("Cleaned up old backups, keeping the 10 most recent");
        }
    }
}