<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeviceCommand extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'device_sn',
        'command',
        'message_id',
        'data',
        'raw_command',
        'status',
        'executed_at',
        'response',
        'failure_reason',
        'created_by',
        'aims'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'executed_at' => 'datetime',
    ];

    /**
     * Get the device that owns the command.
     */
    public function device()
    {
        return $this->belongsTo(Device::class, 'device_sn', 'sn');
    }

    /**
     * Get the user who created the command.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Process a command and send it to the device
     *
     * @return bool True if command was processed successfully, false otherwise
     */
    public function process()
    {
        // Skip if already processed
        if ($this->status !== 'pending') {
            return false;
        }

        // Find the device
        $device = Device::where('sn', $this->device_sn)->first();

        if (!$device) {
            $this->update([
                'status' => 'failed',
                'executed_at' => now(),
                'failure_reason' => 'Device not found'
            ]);
            return false;
        }

        // Check if device is online
        if ($device->status !== 1) {
            // Don't mark as failed immediately, give it a chance to connect
            if (now()->diffInSeconds($this->created_at) > 30) {
                $this->update([
                    'status' => 'failed',
                    'executed_at' => now(),
                    'failure_reason' => 'Device offline'
                ]);
                return false;
            }
            return false;
        }

        // Try to send the command via TCP
        try {
            $tcpController = new \App\Http\Controllers\TCPController();
            $response = $tcpController->sendCommand($this->device_sn, $this->raw_command);

            if ($response && isset($response['success']) && $response['success']) {
                $this->update([
                    'status' => 'sent',
                    'executed_at' => now(),
                    'response' => json_encode($response)
                ]);
                return true;
            } else {
                $this->update([
                    'status' => 'failed',
                    'executed_at' => now(),
                    'failure_reason' => 'TCP send failed: ' . json_encode($response)
                ]);
                return false;
            }
        } catch (\Exception $e) {
            $this->update([
                'status' => 'failed',
                'executed_at' => now(),
                'failure_reason' => 'Exception: ' . $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if there are too many pending commands for a device
     *
     * @param string $deviceSn Device serial number
     * @return bool True if rate limit is exceeded
     */
    public static function isRateLimitExceeded($deviceSn)
    {
        // Count pending commands for this device in the last minute
        $recentCommandCount = self::where('device_sn', $deviceSn)
            ->where('status', 'pending')
            ->where('created_at', '>=', now()->subMinutes(1))
            ->count();

        // Rate limit: maximum 3 pending commands per minute per device
        return $recentCommandCount >= 3;
    }

    /**
     * Create a new command with rate limiting
     *
     * @param array $data Command data
     * @return DeviceCommand|string New command or error message
     */
    public static function createWithRateLimit(array $data)
    {
        // Check if rate limit is exceeded
        if (self::isRateLimitExceeded($data['device_sn'])) {
            return "Rate limit exceeded for device {$data['device_sn']}";
        }

        // Create the command
        return self::create($data);
    }
}

