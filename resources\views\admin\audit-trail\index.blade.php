@extends('layouts.master')

@section('title', 'Audit Trail')

@section('content')
    <div class="container-fluid">
        <!-- Page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item active">Audit Trail</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Audit Trail</h4>
                </div>
            </div>
        </div>

        <!-- Statistics cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="text-muted font-weight-normal mt-0">Total Records</h5>
                                <h3 class="mt-2" id="totalRecords">{{ number_format($stats['total']) }}</h3>
                            </div>
                            <div class="avatar-sm">
                                <span class="avatar-title bg-soft-primary rounded">
                                    <i class="fe-file-text font-20 text-primary"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="text-muted font-weight-normal mt-0">Today's Records</h5>
                                <h3 class="mt-2" id="todayRecords">{{ number_format($stats['today']) }}</h3>
                            </div>
                            <div class="avatar-sm">
                                <span class="avatar-title bg-soft-success rounded">
                                    <i class="fe-calendar font-20 text-success"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="text-muted font-weight-normal mt-0">High Severity</h5>
                                <h3 class="mt-2" id="highSeverityRecords">{{ number_format($stats['high_severity']) }}
                                </h3>
                            </div>
                            <div class="avatar-sm">
                                <span class="avatar-title bg-soft-danger rounded">
                                    <i class="fe-alert-triangle font-20 text-danger"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="text-muted font-weight-normal mt-0">Unique Users</h5>
                                <h3 class="mt-2" id="uniqueUsers">{{ number_format($stats['unique_users']) }}</h3>
                            </div>
                            <div class="avatar-sm">
                                <span class="avatar-title bg-soft-info rounded">
                                    <i class="fe-users font-20 text-info"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters card -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">Filters</h4>
                        <form action="{{ route('admin.audit-trail.index') }}" method="GET" id="filterForm">
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="user_id">User</label>
                                        <select class="form-control select2" id="user_id" name="user_id">
                                            <option value="">All Users</option>
                                            @foreach ($users as $user)
                                                <option value="{{ $user->id }}"
                                                    {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                                    {{ $user->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="action">Action</label>
                                        <select class="form-control select2" id="action" name="action">
                                            <option value="">All Actions</option>
                                            @foreach ($actions as $action)
                                                <option value="{{ $action }}"
                                                    {{ request('action') == $action ? 'selected' : '' }}>
                                                    {{ ucfirst($action) }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="module">Module</label>
                                        <select class="form-control select2" id="module" name="module">
                                            <option value="">All Modules</option>
                                            @foreach ($modules as $module)
                                                <option value="{{ $module }}"
                                                    {{ request('module') == $module ? 'selected' : '' }}>
                                                    {{ ucfirst($module) }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="severity">Severity</label>
                                        <select class="form-control select2" id="severity" name="severity">
                                            <option value="">All Severities</option>
                                            <option value="low" {{ request('severity') == 'low' ? 'selected' : '' }}>
                                                Low
                                            </option>
                                            <option value="medium" {{ request('severity') == 'medium' ? 'selected' : '' }}>
                                                Medium</option>
                                            <option value="high" {{ request('severity') == 'high' ? 'selected' : '' }}>
                                                High</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="date_from">Date From</label>
                                        <input type="date" class="form-control" id="date_from" name="date_from"
                                            value="{{ request('date_from') }}">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="date_to">Date To</label>
                                        <input type="date" class="form-control" id="date_to" name="date_to"
                                            value="{{ request('date_to') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 text-right">
                                    <button type="submit" class="btn btn-primary" id="applyFilters">
                                        <i class="fe-filter mr-1"></i> Apply Filters
                                    </button>
                                    <a href="{{ route('admin.audit-trail.index') }}" class="btn btn-secondary"
                                        id="resetFilters">
                                        <i class="fe-refresh-cw mr-1"></i> Reset
                                    </a>
                                    <a href="{{ route('admin.audit-trail.export', request()->all()) }}"
                                        class="btn btn-success" id="exportBtn">
                                        <i class="fe-download mr-1"></i> Export
                                    </a>
                                    @can('clear-audit-trail')
                                        <button type="button" class="btn btn-danger" data-toggle="modal"
                                            data-target="#clearAuditTrailModal">
                                            <i class="fe-trash-2 mr-1"></i> Clear Audit Trail
                                        </button>
                                    @endcan
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">Audit Trail Records</h4>
                        <div class="table-responsive">
                            <table class="table table-centered table-striped table-hover" id="auditTrailTable"
                                style="border-collapse: collapse; border-spacing: 0px; width: 100%;">

                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Action</th>
                                        <th>Module</th>
                                        <th>Severity</th>
                                        <th>Notes</th>
                                        <th>Date & Time</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($auditTrails as $audit)
                                        <tr>
                                            <td><span class="text-monospace">#{{ $audit->id }}</span></td>
                                            <td>
                                                @if ($audit->user)
                                                    <div class="d-flex align-items-center">
                                                        <div class="mr-2">
                                                            <img class="rounded-circle" width="32" height="32"
                                                                src="{{ $audit->user->profile_photo_url ?? asset('assets/images/users/default-avatar.png') }}"
                                                                alt="{{ $audit->user->name }}"
                                                                onerror="this.src='{{ asset('assets/images/users/default-avatar.png') }}'">
                                                        </div>
                                                        <div>
                                                            <div class="font-weight-bold">{{ $audit->user->name }}</div>
                                                            <small class="text-muted">{{ $audit->user->email }}</small>
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="d-flex align-items-center">
                                                        <div class="mr-2">
                                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center"
                                                                style="width:32px;height:32px;">
                                                                <i class="fas fa-cog text-white"></i>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <div class="font-weight-bold">System</div>
                                                            <small class="text-muted">Automated Process</small>
                                                        </div>
                                                    </div>
                                                @endif
                                            </td>
                                            <td>
                                                @php
                                                    $actionClass = match ($audit->action) {
                                                        'created' => 'success',
                                                        'updated' => 'info',
                                                        'deleted' => 'danger',
                                                        'viewed' => 'secondary',
                                                        'login' => 'primary',
                                                        'logout' => 'warning',
                                                        'failed_login' => 'danger',
                                                        default => 'secondary',
                                                    };
                                                @endphp
                                                <span
                                                    class="badge badge-{{ $actionClass }}">{{ ucfirst($audit->action) }}</span>
                                            </td>
                                            <td>
                                                @if ($audit->module)
                                                    <span class="badge badge-info">{{ ucfirst($audit->module) }}</span>
                                                @else
                                                    <span class="badge badge-secondary">N/A</span>
                                                @endif
                                            </td>
                                            <td>
                                                @php
                                                    $severityClass = match ($audit->severity) {
                                                        'low' => 'success',
                                                        'medium' => 'warning',
                                                        'high' => 'danger',
                                                        default => 'secondary',
                                                    };
                                                @endphp
                                                <span
                                                    class="badge badge-{{ $severityClass }}">{{ ucfirst($audit->severity) }}</span>
                                            </td>
                                            <td>
                                                <span title="{{ $audit->notes }}">
                                                    {{ \Illuminate\Support\Str::limit($audit->notes ?? 'No notes', 50) }}
                                                </span>
                                            </td>
                                            <td>
                                                <span title="{{ $audit->created_at->format('Y-m-d H:i:s') }}">
                                                    {{ $audit->created_at->diffForHumans() }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ route('admin.audit-trail.show', $audit->id) }}"
                                                        class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if ($audit->user)
                                                        <a href="{{ route('admin.users.show', $audit->user->id) }}"
                                                            class="btn btn-sm btn-info">
                                                            <i class="fas fa-user"></i>
                                                        </a>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center">No audit trail records found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Clear Audit Trail Modal -->
    @can('clear-audit-trail')
        <div class="modal fade" id="clearAuditTrailModal" tabindex="-1" role="dialog"
            aria-labelledby="clearAuditTrailModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="clearAuditTrailModalLabel">Clear Audit Trail</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p class="text-danger">Warning: This action will permanently delete all audit trail records. This
                            cannot be undone.</p>
                        <form id="clearAuditTrailForm" action="{{ route('admin.audit-trail.clear') }}" method="POST">
                            @csrf
                            <div class="form-group">
                                <label for="confirmClear">Type "CLEAR" to confirm:</label>
                                <input type="text" class="form-control" id="confirmClear" name="confirm"
                                    placeholder="Type CLEAR here" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmClearBtn" disabled>Clear All
                            Records</button>
                    </div>
                </div>
            </div>
        </div>
    @endcan
@endsection

@section('scripts')

    <script>
        $(document).ready(function() {
            // Initialize DataTable with enhanced features
            $('#auditTrailTable').DataTable({
                dom: 'Bfrtip',
                buttons: [{
                        extend: 'copy',
                        exportOptions: {
                            columns: [0, 2, 3, 4, 5, 6]
                        }
                    },
                    {
                        extend: 'csv',
                        exportOptions: {
                            columns: [0, 2, 3, 4, 5, 6]
                        }
                    },
                    {
                        extend: 'excel',
                        exportOptions: {
                            columns: [0, 2, 3, 4, 5, 6]
                        }
                    },
                    {
                        extend: 'pdf',
                        exportOptions: {
                            columns: [0, 2, 3, 4, 5, 6]
                        }
                    },
                    {
                        extend: 'print',
                        exportOptions: {
                            columns: [0, 2, 3, 4, 5, 6]
                        }
                    }
                ],
                responsive: true,
                ordering: true,
                searching: true,
                paging: true, // Disable DataTables paging as we're using Laravel's pagination
                info: true, // Disable DataTables info as we're using Laravel's pagination
                language: {
                    emptyTable: "No audit trail records found",
                    zeroRecords: "No matching audit trail records found"
                },
                columnDefs: [{
                        orderable: false,
                        targets: [1, 7]
                    } // Disable sorting on user and actions columns
                ]
            });

            // Initialize select2 for better dropdown experience
            $('.select2').select2({
                placeholder: 'Select an option',
                allowClear: true,
                width: '100%'
            });

            // Handle clear audit trail confirmation
            $('#confirmClear').on('input', function() {
                $('#confirmClearBtn').prop('disabled', $(this).val() !== 'CLEAR');
            });

            $('#confirmClearBtn').click(function() {
                if ($('#confirmClear').val() === 'CLEAR') {
                    $('#clearAuditTrailForm').submit();
                }
            });

            // Date range validation
            $('#date_from, #date_to').on('change', function() {
                const dateFrom = $('#date_from').val();
                const dateTo = $('#date_to').val();

                if (dateFrom && dateTo && dateFrom > dateTo) {
                    alert('Date From cannot be later than Date To');
                    $('#date_to').val('');
                }
            });

            // Add tooltips to truncated text
            $('[title]').tooltip({
                placement: 'top',
                trigger: 'hover'
            });

            // Handle filter form submission
            $('#filterForm').on('submit', function() {
                // Remove empty fields from the form to keep the URL clean
                $(this).find('input, select').each(function() {
                    if ($(this).val() === '' || $(this).val() === null) {
                        $(this).prop('disabled', true);
                    }
                });
                return true;
            });

            // Handle export button
            $('#exportBtn').on('click', function(e) {
                e.preventDefault();

                // Get current filter values
                const filters = {};
                $('#filterForm').serializeArray().forEach(function(item) {
                    if (item.value) {
                        filters[item.name] = item.value;
                    }
                });

                // Build export URL with filters
                let exportUrl = $(this).attr('href').split('?')[0];
                const queryParams = new URLSearchParams(filters).toString();

                if (queryParams) {
                    exportUrl += '?' + queryParams;
                }

                // Navigate to export URL
                window.location.href = exportUrl;
            });

            // Add row highlighting on hover
            $('#auditTrailTable tbody tr').hover(
                function() {
                    $(this).addClass('bg-light');
                },
                function() {
                    $(this).removeClass('bg-light');
                }
            );

            // Add click event to view details
            $('#auditTrailTable tbody tr').click(function(e) {
                // Don't trigger if clicking on a button or link
                if (!$(e.target).closest('a, button').length) {
                    const id = $(this).find('td:first-child').text().replace('#', '');
                    window.location.href = "{{ url('admin/audit-trail') }}/" + id;
                }
            });

            // Make rows with clickable style have a pointer cursor
            $('#auditTrailTable tbody tr').css('cursor', 'pointer');

            // Highlight high severity items
            $('#auditTrailTable tbody tr').each(function() {
                const severityText = $(this).find('td:nth-child(5) .badge').text().toLowerCase();
                if (severityText === 'high') {
                    $(this).addClass('border-left border-danger');
                }
            });
        });
    </script>
@endsection
