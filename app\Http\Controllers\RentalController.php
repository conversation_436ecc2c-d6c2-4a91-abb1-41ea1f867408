<?php

namespace App\Http\Controllers;

use App\Models\PowerbankRental;
use Illuminate\Http\Request;

class RentalController extends Controller
{
    /**
     * Display a listing of the rentals.
     */
    public function index()
    {
        $rentals = PowerbankRental::with(['powerbank', 'customer'])
            ->latest('rented_at')
            ->paginate(15);

        return view('rentals.index', compact('rentals'));
    }

    /**
     * Show the form for creating a new rental.
     */
    public function create()
    {
        return view('rentals.create');
    }

    /**
     * Store a newly created rental in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'powerbank_id' => 'required|exists:powerbanks,id',
            'customer_id' => 'required|exists:customers,id',
            'initial_charge' => 'required|numeric|min:0|max:100',
            'aims' => 'nullable|integer|min:0|max:4',
            'slot_number' => 'nullable|integer|min:1',
            'device_id' => 'nullable|exists:devices,id',
        ]);

        // Get powerbank to retrieve its aims and device_id if not provided
        $powerbank = \App\Models\Powerbank::findOrFail($validated['powerbank_id']);

        $rental = PowerbankRental::create([
            'powerbank_id' => $validated['powerbank_id'],
            'customer_id' => $validated['customer_id'],
            'initial_charge' => $validated['initial_charge'],
            'rented_at' => now(),
            'aims' => $validated['aims'] ?? $powerbank->aims ?? 0,
            'slot_number' => $validated['slot_number'] ?? $powerbank->slot_number,
            'device_id' => $validated['device_id'] ?? $powerbank->device_id,
        ]);

        return redirect()->route('rentals.show', $rental->id)
            ->with('success', 'Rental created successfully.');
    }

    /**
     * Display the specified rental.
     */
    public function show(string $id)
    {
        $rental = PowerbankRental::with(['powerbank', 'customer', 'device'])->findOrFail($id);
        return view('rentals.show', compact('rental'));
    }

    /**
     * Show the form for editing the specified rental.
     */
    public function edit(string $id)
    {
        $rental = PowerbankRental::findOrFail($id);
        return view('rentals.edit', compact('rental'));
    }

    /**
     * Update the specified rental in storage.
     */
    public function update(Request $request, string $id)
    {
        $rental = PowerbankRental::findOrFail($id);

        $validated = $request->validate([
            'return_charge' => 'nullable|numeric|min:0|max:100',
        ]);

        $rental->update($validated);

        return redirect()->route('rentals.show', $rental->id)
            ->with('success', 'Rental updated successfully.');
    }

    /**
     * Remove the specified rental from storage.
     */
    public function destroy(string $id)
    {
        $rental = PowerbankRental::findOrFail($id);
        $rental->delete();

        return redirect()->route('rentals.index')
            ->with('success', 'Rental deleted successfully.');
    }
}
