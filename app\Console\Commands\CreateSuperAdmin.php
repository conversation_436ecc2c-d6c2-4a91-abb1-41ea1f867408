<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Role;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateSuperAdmin extends Command
{
    protected $signature = 'admin:create {email?} {name?}';
    protected $description = 'Create a super admin user with all permissions';

    public function handle()
    {
        $email = $this->argument('email') ?? $this->ask('What is the admin email?');
        $name = $this->argument('name') ?? $this->ask('What is the admin name?');
        $password = $this->secret('What is the admin password?');

        // Create user
        $user = User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
        ]);

        // Get admin role
        $adminRole = Role::where('slug', 'administrator')->first();
        
        if ($adminRole) {
            $user->roles()->attach($adminRole);
            $this->info("Super admin created successfully!");
        } else {
            $this->error("Administrator role not found!");
        }
    }
}