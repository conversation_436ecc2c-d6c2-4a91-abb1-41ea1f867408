<?php

namespace App\Http\Controllers;

use App\Models\Otp;
use App\Models\Customer;
use App\Models\PowerbankRentalNotification;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\DeviceCommand;

class CustomerController extends Controller
{
    // Step 1: Show registration form
    public function showRegistrationForm(Request $request)
    {
        // If user is already registered and in session, redirect to appropriate page
        if ($request->session()->has('verified_customer_id')) {
            // return redirect()->route('powerbanks.available');
            return redirect()->route('customer.registration-complete');
        }

        // If there's a pending registration, clear it to prevent issues
        if ($request->session()->has('customer_data')) {
            $request->session()->forget(['customer_data', 'otp_verified', 'card_details']);
        }

        return view('customers.register');
    }

    // Step 2: Process registration and send OTPs
    public function register(Request $request)
    {
        Log::debug('Registration started', ['data' => $request->all()]);

        // Check if customer already exists with this email or phone
        $existingCustomer = \App\Models\Customer::where('email', $request->email)
            ->orWhere('contact_no', $request->contact_no)
            ->first();

        if ($existingCustomer) {
            $errorMessage = 'An account already exists with this ';
            if ($existingCustomer->email === $request->email) {
                $errorMessage .= 'email address.';
            } else {
                $errorMessage .= 'phone number.';
            }

            return redirect()->back()
                ->withErrors(['error' => $errorMessage])
                ->withInput();
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:customers',
            'contact_no' => 'required|string|max:20|unique:customers',
            'dob' => 'required|date',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Get social data from session if exists
        $socialData = $request->session()->get('social_data', []);

        // Store customer data in session
        $request->session()->put('customer_data', [
            'name' => $request->name,
            'email' => $request->email,
            'contact_no' => $request->contact_no,
            'dob' => $request->dob,
            'social_id' => $socialData['social_id'] ?? null,
            'social_type' => $socialData['social_type'] ?? null,
        ]);

        // Clear social data from session
        $request->session()->forget('social_data');

        // Skip both OTP and payment verification if in testing mode
        if (env('SKIP_OTP_VERIFICATION', false) && env('SKIP_PAYMENT_VERIFICATION', false)) {
            Log::debug('Skipping both OTP and payment verification');
            // Mark OTP as verified in the session
            $request->session()->put('otp_verified', true);

            try {
                // Create the customer directly
                $customer = \App\Models\Customer::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'contact_no' => $request->contact_no,
                    'dob' => $request->dob,
                    'email_verified_at' => now(),
                    'contact_verified_at' => now(),
                    'card_verified' => true,
                    'password' => Hash::make('password'), // Generate a random password
                    'stripe_customer_id' => 'test_' . time(),
                ]);

                // Clear session data
                $request->session()->forget(['customer_data', 'otp_verified', 'card_details']);

                // Store customer ID in session for powerbank rental
                $request->session()->put('verified_customer_id', $customer->id);

                // Store customer in session for registration complete page
                $request->session()->put('registered_customer', $customer);

                // Check if we need to redirect back to a lease page
                if ($request->session()->has('lease_return_url')) {
                    $returnUrl = $request->session()->get('lease_return_url');
                    $request->session()->forget('lease_return_url');
                    return redirect($returnUrl);
                }

                return redirect()->route('customer.registration-complete');
            } catch (\Exception $e) {
                return redirect()->route('customer.register')
                    ->withErrors(['error' => 'Registration failed: ' . $e->getMessage()])
                    ->withInput();
            }
        }

        // Skip only OTP verification
        if (env('SKIP_OTP_VERIFICATION', false)) {
            // Mark OTP as verified in the session
            $request->session()->put('otp_verified', true);

            // Redirect to payment verification
            return redirect()->route('customer.payment-verification');
        }

        // Generate and send SMS OTP
        $phoneOtp = $this->generateOtp($request->contact_no, 'phone');

        // Format phone number for WhatsApp API
        $formattedPhoneNumber = $this->formatPhoneNumber($request->contact_no);

        // Log the phone number formatting
        Log::debug('Formatting phone number for WhatsApp', [
            'original' => $request->contact_no,
            'formatted' => $formattedPhoneNumber
        ]);

        // Prepare the payload for WhatsApp
        $payload = [
            'to' => $formattedPhoneNumber, // Formatted recipient's number
            'type' => 'template',
            'template_id' => 881, // Your template ID
            'header_variables' => [],
            'body_variables' => [$phoneOtp->token], // Insert the OTP
            'button_variables' => [''], // Optional
            'priority' => '1'
        ];

        // Send request as raw JSON body
        $response = Http::withHeaders([
            'hash' => env('OTP_WHATSAPP_API_KEY'),
            'Content-Type' => 'application/json',
        ])->withBody(json_encode($payload), 'application/json')
            ->post('http://wa-api.veevotech.com/wa/v1/send_message');

        // Check for success
        if ($response->successful()) {
            $data = $response->json(); // Convert JSON to array
            //dd($data); // Dump and die to see the output
        } else {
            // Handle error
            dd('Request failed', $response->status());
        }

        // For testing, store SMS OTP in session
        $request->session()->flash('sms_otp', $phoneOtp->token);

        return redirect()->route('customer.verify-otp');
    }

    // Step 3: Show OTP verification form
    public function showOtpVerificationForm(Request $request)
    {
        // Skip OTP verification if in testing mode

        return view('customers.verify-otp');
        exit;

        if (env('SKIP_OTP_VERIFICATION', false) && !$request->session()->has('otp_verified')) {
            $request->session()->put('otp_verified', true);
            return redirect()->route('customer.payment-verification');
        }

        if (!$request->session()->has('customer_data')) {
            return redirect()->route('customer.register');
        }

        return view('customers.verify-otp');
    }

    // Step 4: Verify OTPs
    public function verifyOtp(Request $request)
    {
        // Skip OTP verification if in testing mode
        if (env('SKIP_OTP_VERIFICATION', false)) {
            $request->session()->put('otp_verified', true);

            // If payment verification is also skipped, go directly to completion
            if (env('SKIP_PAYMENT_VERIFICATION', false)) {
                return $this->completePaymentVerification($request);
            }

            return redirect()->route('customer.payment-verification');
        }

        $validator = Validator::make($request->all(), [
            'phone_otp' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $customerData = $request->session()->get('customer_data');

        if (!$customerData) {
            return redirect()->route('customer.register')
                ->withErrors(['error' => 'Session expired. Please register again.']);
        }

        // Verify phone OTP
        $phoneOtp = Otp::where('identifier', $customerData['contact_no'])
            ->where('type', 'phone')
            ->where('token', $request->phone_otp)
            ->where('valid', true)
            ->first();

        if (!$phoneOtp || !$phoneOtp->isValid()) {
            return redirect()->back()
                ->withErrors(['otp' => 'Invalid or expired OTP.'])
                ->withInput();
        }

        // Store verification status in session
        $request->session()->put('otp_verified', true);

        // If payment verification is skipped, go directly to completion
        if (env('SKIP_PAYMENT_VERIFICATION', false)) {
            return $this->completePaymentVerification($request);
        }

        return redirect()->route('customer.payment-verification');
    }

    // Step 5: Show payment verification form
    public function showPaymentVerificationForm(Request $request)
    {
        if (!$request->session()->has('customer_data')) {
            return redirect()->route('customer.register');
        }

        // Skip OTP check if OTP verification is disabled
        if (!env('SKIP_OTP_VERIFICATION', false) && !$request->session()->has('otp_verified')) {
            return redirect()->route('customer.verify-otp');
        }

        // Skip payment verification if in testing mode
        if (env('SKIP_PAYMENT_VERIFICATION', false)) {
            // Create a mock payment record
            $request->session()->put('payment', [
                'order_id' => 'TEST_' . time(),
                'status' => 'COMPLETED',
                'amount' => '1.00',
            ]);

            return $this->completePaymentVerification($request);
        }

        return view('customers.verify-payment');
    }

    // Step 6: Process payment verification
    public function verifyPayment(Request $request)
    {
        if (!$request->session()->has('customer_data')) {
            return redirect()->route('customer.register');
        }

        // Skip OTP check if OTP verification is disabled
        if (!env('SKIP_OTP_VERIFICATION', false) && !$request->session()->has('otp_verified')) {
            return redirect()->route('customer.verify-otp');
        }

        // Validate card details
        $validator = Validator::make($request->all(), [
            'card_holder_name' => 'required|string|max:255',
            'card_number' => 'required|string|max:19',
            'expiry_date' => 'required|string|max:5',
            'cvv' => 'required|string|max:4',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // For testing, we can also skip actual payment processing
        if (
            env('SKIP_PAYMENT_VERIFICATION', false) ||
            (env('SKIP_OTP_VERIFICATION', false) && env('APP_ENV') !== 'production')
        ) {
            // Create a mock payment record
            $request->session()->put('payment', [
                'order_id' => 'TEST_' . time(),
                'status' => 'COMPLETED',
                'amount' => '1.00',
            ]);

            return $this->completePaymentVerification($request);
        }

        // Process manual card entry
        // In a real application, you would integrate with a payment processor here
        // For this example, we'll just create a mock payment record
        $request->session()->put('payment', [
            'order_id' => 'MANUAL_' . time(),
            'status' => 'COMPLETED',
            'amount' => '1.00',
        ]);

        // Store card details in session for later use
        $request->session()->put('card_details', [
            'card_holder_name' => $request->card_holder_name,
            'card_number' => $request->card_number,
            'card_expiry' => $request->expiry_date,
            'card_brand' => $this->detectCardBrand($request->card_number),
            'cvv' => $request->cvv,
        ]);

        // Force the session to be saved immediately
        $request->session()->save();

        // Log the payment information for debugging
        \Log::debug('Payment verification completed', [
            'payment_info' => $request->session()->get('payment'),
            'customer_data' => $request->session()->get('customer_data')
        ]);

        return $this->completePaymentVerification($request);
    }

    // Step 7: Show registration complete page
    public function showRegistrationComplete(Request $request)
    {
        \Log::debug('Show registration complete called', [
            'has_registered_customer' => $request->session()->has('registered_customer'),
            'has_verified_customer_id' => $request->session()->has('verified_customer_id')
        ]);

        // Try to get customer from session
        $customer = $request->session()->get('registered_customer');

        // If not in session, try to get from verified_customer_id
        if (!$customer && $request->session()->has('verified_customer_id')) {
            $customer = \App\Models\Customer::find($request->session()->get('verified_customer_id'));
        }

        // If still no customer, redirect to register
        if (!$customer) {
            return redirect()->route('customer.register')
                ->withErrors(['error' => 'Customer information not found. Please register again.']);
        }

        return view('customers.registration-complete', ['customer' => $customer]);
    }

    // Customer Login Methods
    public function showLoginForm(Request $request)
    {
        // If already logged in, redirect to registration complete
        if ($request->session()->has('verified_customer_id')) {
            return redirect()->route('customer.registration-complete');
        }
        return view('customers.login-mobile');
    }

    public function loginMobileSubmit(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'contact_no' => 'required|string',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        // Check if customer exists
        $customer = Customer::where('contact_no', $request->contact_no)->first();
        if (!$customer) {
            return redirect()->back()->withErrors(['contact_no' => 'No account found with this mobile number.'])->withInput();
        }
        // Generate and send WhatsApp OTP
        $phoneOtp = $this->generateOtp($request->contact_no, 'phone');
        $formattedPhoneNumber = $this->formatPhoneNumber($request->contact_no);
        $payload = [
            'to' => $formattedPhoneNumber,
            'type' => 'template',
            'template_id' => 881,
            'header_variables' => [],
            'body_variables' => [$phoneOtp->token],
            'button_variables' => [''],
            'priority' => '1'
        ];
        $response = \Http::withHeaders([
            'hash' => env('OTP_WHATSAPP_API_KEY'),
            'Content-Type' => 'application/json',
        ])->withBody(json_encode($payload), 'application/json')
            ->post('http://wa-api.veevotech.com/wa/v1/send_message');
        // For testing, store SMS OTP in session
        $request->session()->put('login_mobile', $request->contact_no);
        $request->session()->flash('sms_otp', $phoneOtp->token);
        return redirect()->route('customer.login.verify-otp');
    }

    public function showLoginOtpForm(Request $request)
    {
        if (!$request->session()->has('login_mobile')) {
            return redirect()->route('customer.login');
        }
        return view('customers.login-verify-otp');
    }

    public function loginVerifyOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone_otp' => 'required|string|size:6',
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        $contact_no = $request->session()->get('login_mobile');
        if (!$contact_no) {
            return redirect()->route('customer.login');
        }

        $phoneOtp = Otp::where('identifier', $contact_no)
            ->where('type', 'phone')
            ->where('token', $request->phone_otp)
            ->where('valid', true)
            ->first();

        if (!$phoneOtp || !$phoneOtp->isValid()) {
            return redirect()->back()->withErrors(['otp' => 'Invalid or expired OTP.'])->withInput();
        }
        
        // OTP valid, log in customer
        $customer = Customer::where('contact_no', $contact_no)->first();
        if (!$customer) {
            return redirect()->route('customer.login')->withErrors(['contact_no' => 'No account found.']);
        }

        $request->session()->put('verified_customer_id', $customer->id);
        $request->session()->forget('login_mobile');
        
        // Redirect to registration complete (or lease form if lease_return_url exists)
        if ($request->session()->has('lease_return_url')) {
            $returnUrl = $request->session()->get('lease_return_url');
            $request->session()->forget('lease_return_url');
            return redirect($returnUrl);
        }
        return redirect()->route('customer.registration-complete');
    }

    public function logout(Request $request)
    {
        $request->session()->forget('verified_customer_id');
        return redirect()->route('customer.login');
    }

    // Helper method to generate OTP
    /**
     * Generate a new OTP for the given identifier
     *
     * @param string $identifier Email or phone number
     * @param string $type Type of OTP (email or phone)
     * @return \App\Models\Otp
     */
    private function generateOtp($identifier, $type)
    {
        // Invalidate any existing OTPs for this identifier and type
        \App\Models\Otp::where('identifier', $identifier)
            ->where('type', $type)
            ->update(['valid' => false]);

        // Generate a new 6-digit OTP
        $token = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        // Create and return the new OTP
        return \App\Models\Otp::create([
            'identifier' => $identifier,
            'token' => $token,
            'type' => $type,
            'valid' => true,
            'expires_at' => now()->addMinutes(10), // OTP expires after 10 minutes
        ]);
    }

    // Admin methods for customer management
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('view-customers');

        $customers = Customer::latest()->get();
        return view('customer.index', compact('customers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create-customers');

        return view('customer.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create-customers');

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:customers,email',
            'contact_no' => 'required|string|unique:customers,contact_no',
            'dob' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        $customer = Customer::create([
            'name' => $request->name,
            'email' => $request->email,
            'contact_no' => $request->contact_no,
            'dob' => $request->dob,
            'notes' => $request->notes,
            'password' => Hash::make(Str::random(12)), // Generate a random password
        ]);

        return redirect()->route('customer.index')
            ->with('success', 'Customer created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Customer $customer)
    {
        $this->authorize('view-customers');

        return view('customer.show', compact('customer'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Customer $customer)
    {
        $this->authorize('edit-customers');

        return view('customer.edit', compact('customer'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Customer $customer)
    {
        $this->authorize('edit-customers');

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:customers,email,' . $customer->id,
            'contact_no' => 'required|string|unique:customers,contact_no,' . $customer->id,
            'dob' => 'required|date',
            'notes' => 'nullable|string',
            'status' => 'boolean',
        ]);

        $customer->update([
            'name' => $request->name,
            'email' => $request->email,
            'contact_no' => $request->contact_no,
            'dob' => $request->dob,
            'notes' => $request->notes,
            'status' => $request->status ?? $customer->status,
        ]);

        return redirect()->route('customer.index')
            ->with('success', 'Customer updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Customer $customer)
    {
        $this->authorize('delete-customers');

        $customer->delete();

        return redirect()->route('customer.index')
            ->with('success', 'Customer deleted successfully.');
    }

    public function addNote(Request $request, $id)
    {
        $customer = Customer::findOrFail($id);
        $customer->notes = $request->note_content;
        $customer->save();

        return redirect()->route('customer.show', $id)
            ->with('success', 'Note added successfully.');
    }

    public function registrations()
    {
        $customers = Customer::where('card_verified', true)
            ->latest()
            ->paginate(15);

        return view('customer.registrations', compact('customers'));
    }

    public function reports()
    {
        $totalCustomers = Customer::count();
        $verifiedCustomers = Customer::where('card_verified', true)->count();
        $recentCustomers = Customer::latest()->take(10)->get();

        return view('customer.reports', compact('totalCustomers', 'verifiedCustomers', 'recentCustomers'));
    }

    /**
     * Send a reminder to customer about returning powerbank
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function sendReminder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'rental_id' => 'required|exists:powerbank_rentals,id',
            'message' => 'required|string',
            'type' => 'sometimes|in:sms,email,push',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $rental = PowerbankRental::with(['powerbank', 'device', 'customer'])->findOrFail($request->rental_id);

        // Create notification record
        $notification = PowerbankRentalNotification::create([
            'powerbank_rental_id' => $rental->id,
            'message' => $request->message,
            'type' => $request->type ?? 'sms',
            'status' => 'pending',
            'sent_by' => auth()->id(),
        ]);

        // Here you would implement the actual message sending logic
        try {
            // Example: Send SMS via a service
            // $response = SmsService::send($rental->customer->contact_no, $request->message);

            // For now, we'll just simulate success
            $notification->update([
                'status' => 'sent',
                'sent_at' => now(),
                'response' => json_encode(['success' => true])
            ]);

            return redirect()->back()->with('success', 'Reminder sent successfully to customer.');
        } catch (\Exception $e) {
            $notification->update([
                'status' => 'failed',
                'response' => $e->getMessage()
            ]);

            return redirect()->back()
                ->withErrors(['message' => 'Failed to send reminder: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show the lease form for customers to rent powerbanks
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function showLeaseForm(Request $request)
    {
        // Extract device SN from the URL parameters
        $deviceSn = $request->query('t');

        if (!$deviceSn) {
            return redirect()->route('error')
                ->with('error', 'Invalid machine ID. Please rescan the QR code.');
        }

        // Save device SN in session
        $request->session()->put('device_sn', $deviceSn);

        // Find the device
        $device = \App\Models\Device::where('sn', $deviceSn)->first();

        if (!$device) {
            return redirect()->route('error')
                ->with('error', 'Invalid machine ID. Please rescan the QR code.');
        }



        // Get all devices with available powerbanks
        $devices = \App\Models\Device::withCount(['powerbanks' => function($query) {
            $query->where('status', 'available');
        }])
        ->get()
        ->map(function($device) {
            $device->is_available = $device->powerbanks_count > 0;
            $device->available_powerbanks_count = $device->powerbanks_count;
            return $device;
        });

        // Sort devices by location
        $devices = $devices->sortBy('location');

        // Check if user is logged in
        if (session()->has('verified_customer_id')) {
            $customer = \App\Models\Customer::find(session('verified_customer_id'));
            
            // Check if customer already has an active rental
            $activeRental = \App\Models\PowerbankRental::where('customer_id', $customer->id)
                ->whereNull('returned_at')
                ->first();

            // return view('customers.devices.index', compact('devices', 'customer', 'activeRental'));
            if ($activeRental) {
                return redirect()->route('error')
                    ->with('error', 'You already have an active powerbank rental. Please return it before renting another one.');
            }

            // Get available powerbanks in this device
            $powerbanks = \App\Models\Powerbank::where('device_id', $device->id)
                ->where('status', 'available')
                ->get();

            return view('customers.registration-complete', compact('customer', 'powerbanks', 'device'));

        } else {
            // Store return URL in session
            session(['lease_return_url' => request()->fullUrl()]);
            return view('customers.devices.index', compact('devices'));
        }
        
        // // Check if user is logged in as customer
        // if (session()->has('verified_customer_id')) {
        //     // Customer is logged in, get customer details
        //     $customer = \App\Models\Customer::find(session('verified_customer_id'));
            
        //     // Check if customer already has an active rental
        //     $activeRental = \App\Models\PowerbankRental::where('customer_id', $customer->id)
        //         ->whereNull('returned_at')
        //         ->first();

        //     if ($activeRental) {
        //         return redirect()->route('error')
        //             ->with('error', 'You already have an active powerbank rental. Please return it before renting another one.');
        //     }

        //     // Get available powerbanks in this device
        //     $powerbanks = \App\Models\Powerbank::where('device_id', $device->id)
        //         ->where('status', 'available')
        //         ->get();

        //     return view('customers.registration-complete', compact('customer', 'powerbanks', 'device'));
        // } else {
        //     // Customer not logged in, store device SN in session and redirect to login
        //     session(['selected_device_sn' => $deviceSn]);
        //     session(['lease_return_url' => $request->fullUrl()]);
        //     return redirect()->route('customer.login')
        //         ->with('info', 'Please login to rent a powerbank.');
        // }
    }

    /**
     * Process powerbank rental for customer
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function rentPowerbank(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'device_sn' => 'required|string',
            'customer_id' => 'required|exists:customers,id',
            'slot_number' => 'required|integer|min:1|max:12',
            'aims' => 'required|integer|min:0',
            'powerbank_id' => 'required|exists:powerbanks,id'
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Verify customer ID matches the authenticated user
        if ($request->customer_id != session('verified_customer_id')) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            return redirect()->back()->with('error', 'Unauthorized access.');
        }

        // Check if customer already has an active rental
        $activeRental = \App\Models\PowerbankRental::where('customer_id', $request->customer_id)
            ->whereNull('returned_at')
            ->first();

        if ($activeRental) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You already have an active powerbank rental. Please return it before renting another one.'
                ], 400);
            }

            return redirect()->back()->with('error', 'You already have an active powerbank rental. Please return it before renting another one.');
        }

        // Find the device
        $device = \App\Models\Device::where('sn', $request->device_sn)->first();

        if (!$device) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Device not found.'
                ], 404);
            }

            return redirect()->back()->with('error', 'Device not found.');
        }

        // Find the powerbank
        $powerbank = \App\Models\Powerbank::find($request->powerbank_id);

        if (!$powerbank || $powerbank->status !== 'available') {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Powerbank not available for rent.'
                ], 400);
            }

            return redirect()->back()->with('error', 'Powerbank not available for rent.');
        }

        // Generate a message ID for the command
        $messageId = mt_rand(10000, 99999);

        // Calculate actual slot number based on AIMS
        $aims = (int)$request->aims;
        $slotNumber = (int)$request->slot_number;
        $actualSlotNumber = $aims > 0 ? $slotNumber % 12 || 12 : $slotNumber;

        // Create the rent command
        $rentCommand = "#*{\"cmd\":\"rent\",\"sn\":\"{$request->device_sn}\",\"aims\":{$aims},\"data\":{\"msg\":{$messageId},\"n\":{$actualSlotNumber}}}*#";

        // Save the command to the database
        $command = \App\Models\DeviceCommand::create([
            'device_sn' => $request->device_sn,
            'command' => 'rent',
            'message_id' => $messageId,
            'data' => json_encode([
                'n' => (int)$actualSlotNumber,
                'aims' => $aims,
                'customer_id' => $request->customer_id
            ]),
            'raw_command' => $rentCommand,
            'status' => 'pending',
        ]);

        // Update powerbank status
        $powerbank->update([
            'status' => 'rented',
            'slot_number' => null
        ]);

        // Create rental record
        $rental = \App\Models\PowerbankRental::create([
            'powerbank_id' => $powerbank->id,
            'device_id' => $device->id,
            'customer_id' => $request->customer_id,
            'rented_at' => now(),
            'initial_charge' => $powerbank->current_charge,
            'slot_number' => $actualSlotNumber,
            'aims' => $aims
        ]);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Powerbank rental initiated successfully.',
                'command_id' => $command->id,
                'rental_id' => $rental->id,
                'message_id' => $messageId
            ]);
        }

        return redirect()->route('customer.rental-success')->with('success', 'Powerbank rental initiated. Please take your powerbank from the device.');
    }

    /**
     * Show rental success page
     *
     * @return \Illuminate\Http\Response
     */
    public function rentalSuccess()
    {
        return view('customers.rental-success');
    }

    public function completePaymentVerification(Request $request)
    {
        \Log::debug('Complete payment verification called', [
            'method' => $request->method(),
            'has_customer_data' => $request->session()->has('customer_data')
        ]);

        // If this is accessed directly via GET, we need to check for a valid session
        if ($request->isMethod('get') && !$request->session()->has('payment')) {
            // Check if we're in testing mode
            if (env('SKIP_PAYMENT_VERIFICATION', false) && $request->session()->has('customer_data')) {
                // Create a mock payment record
                $request->session()->put('payment', [
                    'order_id' => 'TEST_' . time(),
                    'status' => 'COMPLETED',
                    'amount' => '1.00',
                ]);
            } else {
                return redirect()->route('customer.payment-verification')
                    ->withErrors(['payment' => 'Invalid request. Please complete payment verification.']);
            }
        }

        $payment = $request->session()->get('payment');
        if (!$payment) {
            \Log::error('Payment verification failed: No payment information found');
            return redirect()->route('customer.payment-verification')
                ->withErrors(['payment' => 'Payment verification failed: No payment information found']);
        }

        $customerData = $request->session()->get('customer_data');
        if (!$customerData) {
            \Log::error('Customer data not found in session');
            return redirect()->route('customer.register')
                ->withErrors(['error' => 'Customer data not found. Please register again.']);
        }

        try {
            \Log::debug('Creating customer', $customerData);

            // Get card details from session
            $cardDetails = $request->session()->get('card_details', []);

            // Use a database transaction to ensure data consistency
            $customer = \DB::transaction(function () use ($customerData, $payment, $cardDetails) {
                // Create the customer in our database
                return Customer::create([
                    'name' => $customerData['name'],
                    'email' => $customerData['email'],
                    'contact_no' => $customerData['contact_no'],
                    'dob' => $customerData['dob'] ?? null,
                    'email_verified_at' => now(),
                    'contact_verified_at' => now(),
                    'card_verified' => true,
                    'password' => Hash::make(Str::random(12)), // Generate a random password
                    'payment_method' => 'card',
                    'payment_id' => $payment['order_id'],
                    'card_holder_name' => $cardDetails['card_holder_name'] ?? null,
                    'card_number' => $cardDetails['card_number'] ?? null,
                    'card_expiry' => $cardDetails['card_expiry'] ?? null,
                    'card_brand' => $cardDetails['card_brand'] ?? null,
                    'cvv' => $cardDetails['cvv'] ?? null,
                    'payment_verified' => true,
                    'payment_verified_at' => now(),
                    'card_verified' => true,
                    'card_verified_at' => now(),
                    'social_id' => $customerData['social_id'] ?? null,
                    'social_type' => $customerData['social_type'] ?? null,
                    'status' => '1',
                    'notes' => 'Payment verified',
                    'created_by' => 'system',
                    'updated_by' => 'system'
                ]);
            });

            \Log::debug('Customer created successfully', ['id' => $customer->id]);

            // Clear session data
            $request->session()->forget(['customer_data', 'otp_verified', 'payment', 'card_details']);

            // Store customer ID in session for powerbank rental
            $request->session()->put('verified_customer_id', $customer->id);

            // Store customer in session for registration complete page
            $request->session()->put('registered_customer', $customer);

            // Check if we need to redirect back to a lease page
            if ($request->session()->has('lease_return_url')) {
                $returnUrl = $request->session()->get('lease_return_url');
                $request->session()->forget('lease_return_url');
                \Log::debug('Redirecting to lease page', ['url' => $returnUrl]);
                return redirect($returnUrl);
            }

            return redirect()->route('customer.registration-complete');
        } catch (\Exception $e) {
            \Log::error('Customer registration failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('customer.payment-verification')
                ->withErrors(['payment' => 'Customer registration failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Detect card brand from card number
     */
    private function detectCardBrand($cardNumber)
    {
        // Remove spaces and non-numeric characters
        $cardNumber = preg_replace('/\D/', '', $cardNumber);

        // Card patterns
        $patterns = [
            'visa' => '/^4/',
            'mastercard' => '/^5[1-5]/',
            'amex' => '/^3[47]/',
            'discover' => '/^6(?:011|5)/',
            'diners' => '/^3(?:0[0-5]|[68])/',
            'jcb' => '/^(?:2131|1800|35\d{3})/'
        ];

        // Check patterns
        foreach ($patterns as $brand => $pattern) {
            if (preg_match($pattern, $cardNumber)) {
                return $brand;
            }
        }

        return 'unknown';
    }

    /**
     * Direct registration method for testing
     */
    public function directRegister(Request $request)
    {
        // Only allow in non-production environments
        if (app()->environment('production') && !env('ALLOW_DIRECT_REGISTER', false)) {
            abort(404);
        }

        try {
            // Create the customer directly
            $customer = \App\Models\Customer::create([
                'name' => $name,
                'email' => $email,
                'contact_no' => $phone,
                'dob' => '1990-01-01',
                'email_verified_at' => now(),
                'contact_verified_at' => now(),
                'card_verified' => true,
                'password' => Hash::make('password123'), // Simple password for testing
            ]);

            // Store customer ID in session for powerbank rental
            $request->session()->put('verified_customer_id', $customer->id);

            // Store customer in session for registration complete page
            $request->session()->put('registered_customer', $customer);

            return redirect()->route('customer.registration-complete')
                ->with('success', 'Test account created successfully: ' . $email);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Registration failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Get available powerbanks for the customer
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailablePowerbanks($deviceSn = null)
    {
        if (!$deviceSn) {
            $deviceSn = session('device_sn');
        }

        // Check if user is logged in
        if (!session()->has('verified_customer_id')) {
            return response()->json([
                'success' => false,
                'message' => 'Please login first',
                'redirect' => route('customer.login')
            ], 401);
        }

        // If deviceSn is not provided, try to get it from session
        if (!$deviceSn) {
            $deviceSn = session('device_sn');
        }

        if (!$deviceSn) {
            return response()->json([
                'success' => false,
                'message' => 'Device SN not found'
            ], 400);
        }

        // Find the device
        $device = \App\Models\Device::where('sn', $deviceSn)->first();

        if (!$device) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid machine ID. Please rescan the QR code.',
                'error' => true,
                'redirect' => route('error')
            ], 400);
        }

        // Get the first available powerbank
        $powerbank = \App\Models\Powerbank::where('status', 'available')
            ->where('device_id', $device->id)
            ->whereNotNull('slot_number')
            ->first();
         
        //->whereNotNull('device_id')

        if (!$powerbank) {
            return response()->json([
                'success' => false,
                'message' => 'No power banks available in this device'
            ], 404);
        }

        // Generate random slot number between 1 to 8
        $slotNumber = rand(1, 8);

        // Generate a message ID for the command
        $messageId = mt_rand(10000, 99999);

        // Create the rent command with random slot number
        $rentCommand = "#*{\"cmd\":\"rent\",\"sn\":\"{$device->sn}\",\"aims\":{$powerbank->aims},\"data\":{\"msg\":{$messageId},\"n\":{$slotNumber}}}*#";

        // Save the command to the database
        $command = DeviceCommand::create([
            'device_sn' => $device->sn,
            'command' => 'rent',
            'message_id' => $messageId,
            'data' => json_encode([
                'n' => $slotNumber,
                'aims' => $powerbank->aims,
                'powerbank_id' => $powerbank->id,
                'customer_id' => session('verified_customer_id')
            ]),
            'raw_command' => $rentCommand,
            'status' => 'pending'
        ]);

        // Update powerbank status
        $powerbank->update([
            'status' => 'rented',
            'slot_number' => null
        ]);

        // Create rental record
        $rental = \App\Models\PowerbankRental::create([
            'powerbank_id' => $powerbank->id,
            'device_id' => $device->id,
            'customer_id' => session('verified_customer_id'),
            'rented_at' => now(),
            'initial_charge' => $powerbank->current_charge,
            'slot_number' => $slotNumber,
            'aims' => $powerbank->aims
        ]);
        
        return response()->json([
            'success' => true,
            'powerbank' => $powerbank,
            'command' => $command,
            'rental' => $rental
        ]);
    }

    /**
     * Handle powerbank lease request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function lease(Request $request)
    {
        // Get device SN from request
        $deviceSn = $request->t;

        $request->session()->put('device_sn', $deviceSn);

        if (!$deviceSn) {
            return redirect()->route('powerbanks.available')->with('error', 'No device specified.');
        }

        // Find the device
        $device = \App\Models\Device::where('sn', $deviceSn)->first();

        if (!$device) {
            return redirect()->route('powerbanks.available')
                ->with('error', 'Device not found.');
        }

        // Check if user is logged in as customer
        if (session()->has('verified_customer_id')) {
            // Customer is logged in
            $customer = \App\Models\Customer::find(session('verified_customer_id'));

            // Check if customer already has an active rental
            $activeRental = \App\Models\PowerbankRental::where('customer_id', $customer->id)
                ->whereNull('returned_at')
                ->first();

            if ($activeRental) {
                return redirect()->route('powerbanks.available')
                    ->with('error', 'You already have an active powerbank rental. Please return it before renting another one.');
            }

            // Get available powerbanks in this device
            $powerbanks = \App\Models\Powerbank::where('device_id', $device->id)
                ->where('status', 'available')
                ->get();

            //$deviceSn = $request->session()->get('device_sn');

            return view('customers.registration-complete', compact('customer', 'powerbanks', 'device'));
            // return view('customers.rent-powerbank', compact('device', 'customer', 'powerbanks'));
        } else {
            // Customer not logged in, store device SN in session and redirect to login
            session(['selected_device_sn' => $deviceSn]);
            session(['lease_return_url' => $request->fullUrl()]);
            return redirect()->route('customer.login')
                ->with('info', 'Please login to rent a powerbank.');
        }
    }

    /**
     * Resend OTP to customer email and phone
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function resendOtp(Request $request)
    {
        if (!$request->session()->has('customer_data')) {
            return redirect()->route('customer.register');
        }

        $customerData = $request->session()->get('customer_data');

        // Generate and send SMS OTP
        $phoneOtp = $this->generateOtp($customerData['contact_no'], 'phone');

        // Format phone number for WhatsApp API
        $formattedPhoneNumber = $this->formatPhoneNumber($customerData['contact_no']);

        // Log the phone number formatting
        Log::debug('Formatting phone number for WhatsApp (resend)', [
            'original' => $customerData['contact_no'],
            'formatted' => $formattedPhoneNumber
        ]);

        // Prepare the payload for WhatsApp
        $payload = [
            'to' => $formattedPhoneNumber, // Formatted recipient's number
            'type' => 'template',
            'template_id' => 881, // Your template ID
            'header_variables' => [],
            'body_variables' => [$phoneOtp->token], // Insert the OTP
            'button_variables' => [''], // Optional
            'priority' => '1'
        ];

        // Send request as raw JSON body
        $response = Http::withHeaders([
            'hash' => env('OTP_WHATSAPP_API_KEY'),
            'Content-Type' => 'application/json',
        ])->withBody(json_encode($payload), 'application/json')
            ->post('http://wa-api.veevotech.com/wa/v1/send_message');

        // For testing, store SMS OTP in session
        $request->session()->flash('sms_otp', $phoneOtp->token);

        return redirect()->route('customer.verify-otp')
            ->with('success', 'OTP codes have been resent to your WhatsApp.');
    }

    /**
     * Format phone number for WhatsApp API
     * - If number starts with 0, replace with 92
     * - If number already starts with 92, leave as is
     *
     * @param string $phoneNumber
     * @return string
     */
    private function formatPhoneNumber($phoneNumber)
    {
        // Remove any spaces or special characters
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        // If number starts with 0, replace with 92
        if (substr($phoneNumber, 0, 1) === '0') {
            return '92' . substr($phoneNumber, 1);
        }

        // If number doesn't start with 92, add it
        if (substr($phoneNumber, 0, 2) !== '92') {
            return '92' . $phoneNumber;
        }

        // Otherwise, return as is
        return $phoneNumber;
    }

    /**
     * Show devices page with map and table
     *
     * @return \Illuminate\Http\Response
     */
    public function showDevices()
    {
        // Get all devices with available powerbanks
        $devices = \App\Models\Device::withCount(['powerbanks' => function($query) {
                $query->where('status', 'available');
            }])
            ->get()
            ->map(function($device) {
                $device->is_available = $device->powerbanks_count > 0;
                $device->available_powerbanks_count = $device->powerbanks_count;
                return $device;
            });

        // Sort devices by location
        $devices = $devices->sortBy('location');

        // Check if user is logged in
        if (session()->has('verified_customer_id')) {
            $customer = \App\Models\Customer::find(session('verified_customer_id'));
            
            // Check if customer already has an active rental
            $activeRental = \App\Models\PowerbankRental::where('customer_id', $customer->id)
                ->whereNull('returned_at')
                ->first();

            return view('customers.devices.index', compact('devices', 'customer', 'activeRental'));
        } else {
            // Store return URL in session
            session(['lease_return_url' => request()->fullUrl()]);
            return view('customers.devices.index', compact('devices'));
        }
    }

    // Check if customer exists (AJAX endpoint)
    public function checkCustomerExists(Request $request)
    {
        \Log::info('checkCustomerExists called', $request->all());
        
        $email = $request->input('email');
        $contactNo = $request->input('contact_no');
        
        \Log::info('Checking for customer', [
            'email' => $email,
            'contact_no' => $contactNo
        ]);
        
        $existingCustomer = Customer::where(function($query) use ($email, $contactNo) {
            if ($email) {
                $query->where('email', $email);
            }
            if ($contactNo) {
                $query->orWhere('contact_no', $contactNo);
            }
        })->first();

        \Log::info('Existing customer found', [
            'exists' => $existingCustomer ? true : false,
            'customer' => $existingCustomer ? $existingCustomer->toArray() : null
        ]);

        if ($existingCustomer) {
            $message = 'A customer with this ';
            if ($existingCustomer->email === $email) {
                $message .= 'email address is already registered.';
            } else {
                $message .= 'phone number is already registered.';
            }
            
            $response = [
                'exists' => true,
                'message' => $message,
                'field' => $existingCustomer->email === $email ? 'email' : 'contact_no'
            ];
            
            \Log::info('Returning exists response', $response);
            return response()->json($response);
        }

        $response = [
            'exists' => false,
            'message' => 'Contact information is available for registration.'
        ];
        
        \Log::info('Returning not exists response', $response);
        return response()->json($response);
    }
}
