@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="d-flex align-items-center">
                        <h4 class="card-title mb-0">Add New Powerbank</h4>
                        <a href="{{ route('powerbanks.index') }}" class="btn btn-light btn-sm ml-auto">
                            <i class="fas fa-arrow-left mr-1"></i> Back to Powerbanks
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('powerbanks.store') }}" method="POST">
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card-box bg-light p-3 mb-3">
                                    <h5 class="text-uppercase text-muted font-weight-bold mb-3">
                                        <i class="fas fa-info-circle mr-1"></i> Basic Information
                                    </h5>

                                    <div class="form-group">
                                        <label for="serial_number">Serial Number <span class="text-danger">*</span></label>
                                        <input type="text"
                                            class="form-control @error('serial_number') is-invalid @enderror"
                                            id="serial_number" name="serial_number" value="{{ old('serial_number') }}"
                                            required>
                                        @error('serial_number')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Unique identifier for this powerbank</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="device_id">Associated Device</label>
                                        <select class="form-control select2 @error('device_id') is-invalid @enderror"
                                            id="device_id" name="device_id">
                                            <option value="">Select Device (Optional)</option>
                                            @foreach ($devices as $device)
                                                <option value="{{ $device->id }}"
                                                    {{ old('device_id') == $device->id || $selectedDeviceId == $device->id ? 'selected' : '' }}>
                                                    {{ $device->sn }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('device_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Device where this powerbank will be
                                            installed</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="slot_number">Slot Number</label>
                                        <input type="number"
                                            class="form-control @error('slot_number') is-invalid @enderror" id="slot_number"
                                            name="slot_number" value="{{ old('slot_number') }}">
                                        @error('slot_number')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Position in the device (if applicable)</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card-box bg-light p-3 mb-3">
                                    <h5 class="text-uppercase text-muted font-weight-bold mb-3">
                                        <i class="fas fa-battery-full mr-1"></i> Power Specifications
                                    </h5>

                                    <div class="form-group">
                                        <label for="capacity">Capacity (mAh) <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                            id="capacity" name="capacity" value="{{ old('capacity', 5000) }}" required>
                                        @error('capacity')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Total power capacity in
                                            milliampere-hours</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="current_charge">Current Charge (%) <span
                                                class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number"
                                                class="form-control @error('current_charge') is-invalid @enderror"
                                                id="current_charge" name="current_charge"
                                                value="{{ old('current_charge', 100) }}" required>
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                        @error('current_charge')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group">
                                        <label for="charge_cycles">Charge Cycles</label>
                                        <input type="number"
                                            class="form-control @error('charge_cycles') is-invalid @enderror"
                                            id="charge_cycles" name="charge_cycles" value="{{ old('charge_cycles', 0) }}">
                                        @error('charge_cycles')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Number of completed charge cycles</small>
                                    </div>
                                </div>

                                <div class="card-box bg-light p-3">
                                    <h5 class="text-uppercase text-muted font-weight-bold mb-3">
                                        <i class="fas fa-cog mr-1"></i> Status
                                    </h5>

                                    <div class="form-group">
                                        <label for="status">Current Status <span class="text-danger">*</span></label>
                                        <select class="form-control @error('status') is-invalid @enderror" id="status"
                                            name="status" required>
                                            <option value="available" {{ old('status') == 'available' ? 'selected' : '' }}>
                                                Available</option>
                                            <option value="charging" {{ old('status') == 'charging' ? 'selected' : '' }}>
                                                Charging</option>
                                            <option value="maintenance"
                                                {{ old('status') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group">
                                        <label for="aims">AIMS Value</label>
                                        <input type="number" class="form-control @error('aims') is-invalid @enderror"
                                            id="aims" name="aims" value="{{ old('aims', 0) }}" min="0"
                                            max="4">
                                        @error('aims')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">AIMS value must be between 0 and 4</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-right mt-4">
                            <a href="{{ url()->previous() }}" class="btn btn-light mr-2">
                                <i class="fas fa-times mr-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-1"></i> Save Powerbank
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            // Initialize select2 for better dropdown experience
            $('.select2').select2({
                width: '100%',
                placeholder: 'Select an option'
            });
        });
    </script>
@endpush
