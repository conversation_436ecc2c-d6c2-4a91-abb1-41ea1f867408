<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('powerbanks', function (Blueprint $table) {
            $table->id();
            $table->string('serial_number')->unique();
            $table->foreignId('device_id')->nullable()->constrained()->onDelete('set null');
            $table->integer('capacity')->comment('in mAh');
            $table->integer('current_charge')->comment('percentage');
            $table->integer('slot_number')->nullable();
            $table->enum('status', ['available', 'rented', 'charging', 'maintenance'])->default('available');
            $table->integer('charge_cycles')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('powerbanks');
    }
};
