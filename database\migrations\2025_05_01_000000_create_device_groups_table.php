<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('device_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Create pivot table for devices and groups
        Schema::create('device_device_group', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained()->onDelete('cascade');
            $table->foreignId('device_group_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            
            // Prevent duplicate assignments
            $table->unique(['device_id', 'device_group_id']);
        });

        // Create pivot table for groups and ad plans
        Schema::create('ad_plan_device_group', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ad_plan_id')->constrained()->onDelete('cascade');
            $table->foreignId('device_group_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            
            // Prevent duplicate assignments
            $table->unique(['ad_plan_id', 'device_group_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ad_plan_device_group');
        Schema::dropIfExists('device_device_group');
        Schema::dropIfExists('device_groups');
    }
};