@extends('layouts.customer')

@section('content')
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-12 col-md-10 col-lg-8 col-xl-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Customer Registration</h4>
                    </div>
                    <div class="card-body p-4">
                        <p class="text-muted mb-4">Please fill in your details to create an account</p>

                        <!-- Error Message Area -->
                        <div id="customer-exists-error" class="alert alert-danger d-none" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Customer Already Exists!</strong> A customer with this email or phone number is already
                            registered.
                            Please <a href="{{ route('customer.login') }}" class="alert-link">login here</a> or use
                            different contact details.
                        </div>

                        <!-- Success Message Area -->
                        <div id="customer-check-success" class="alert alert-success d-none" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Great!</strong> This contact information is available for registration.
                        </div>

                        <form action="{{ route('customer.register.post') }}" method="POST" class="needs-validation"
                            novalidate>
                            @csrf

                            <div class="row g-3">
                                <!-- Full Name - Takes more space on larger screens -->
                                <div class="col-12 col-lg-8">
                                    <label for="name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                        id="name" name="name" value="{{ old('name', session('social_data.name')) }}"
                                        placeholder="Enter your name" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Date of Birth - Takes less space on larger screens -->
                                <div class="col-12 col-lg-4">
                                    <label for="dob" class="form-label">Date of Birth</label>
                                    <input type="date" class="form-control @error('dob') is-invalid @enderror"
                                        id="dob" name="dob" value="{{ old('dob') }}" placeholder="DD/MM/YYYY"
                                        required>
                                    @error('dob')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Must be 18+</div>
                                </div>

                                <!-- Email Address -->
                                <div class="col-12 col-md-6">
                                    <label for="email" class="form-label">Email Address</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                        <input type="email" class="form-control @error('email') is-invalid @enderror"
                                            id="email" name="email"
                                            value="{{ old('email', session('social_data.email')) }}"
                                            placeholder="Enter your email" required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-text">We'll send a verification code to this email</div>
                                </div>

                                <!-- Contact Number with Country Code -->
                                <div class="col-12 col-md-6">
                                    <label for="phone" class="form-label">Phone Number:</label>
                                    <input id="phone" type="tel" class="form-control phonenum" maxlength="17"
                                        placeholder="Phone Number" value="{{ old('phone') }}" required>
                                    <div class="form-text">We'll send a verification code to this phone number</div>
                                </div>

                                <!-- Terms and Conditions -->
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="terms" name="terms"
                                            required>
                                        <label class="form-check-label" for="terms">
                                            I agree to the <a href="{{ route('privacy.policy') }}">privacy policy</a>, <a
                                                href="{{ route('terms.of.use') }}">terms of use</a> and <a
                                                href="{{ route('cookie.policy') }}">cookie policy</a>.
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="submit" class="btn btn-primary btn-lg px-4">
                                            Continue <i class="fas fa-arrow-right ms-2"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <p class="text-muted">
                        Already have an account? <a href="{{ route('customer.login') }}" class="text-decoration-none">Sign
                            in</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
    <!--
        <div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-scrollable">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="termsModalLabel">Terms and Conditions</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Please read these terms and conditions carefully before using our service.</p>
                        <p>By registering, you agree to our <a href="{{ route('privacy.policy') }}">privacy policy</a>  and <a href="{{ route('terms.of.use') }}">terms of use</a>.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div> -->
@endsection

@section('styles')
    <style>
        .card {
            border-radius: 0.5rem;
            border: none;
        }

        .card-header {
            border-radius: 0.5rem 0.5rem 0 0 !important;
        }

        .btn-primary {
            padding: 0.5rem 1.5rem;
        }

        /* Add styles for social login buttons */
        .btn-outline-danger {
            border-color: #ea4335;
            color: #ea4335;
        }

        .btn-outline-danger:hover {
            background-color: #ea4335;
            color: white;
        }

        .btn-outline-primary {
            border-color: #1877f2;
            color: #1877f2;
        }

        .btn-outline-primary:hover {
            background-color: #1877f2;
            color: white;
        }

        /* Improved form styling */
        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .input-group-text {
            background-color: #f8f9fa;
            border-color: #ced4da;
        }

        /* Country code dropdown improvements */
        .dropdown-toggle::after {
            margin-left: 0.5rem;
        }

        .dropdown-menu {
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            z-index: 1050;
        }

        .dropdown-item {
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #f8f9fa;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #0d6efd;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        /* Ensure dropdown is visible */
        .dropdown.show .dropdown-menu {
            display: block !important;
        }

        /* Input group styling for country code */
        .input-group .dropdown {
            position: relative;
        }

        .input-group .dropdown-toggle {
            border-right: 0;
        }

        .input-group .form-control {
            border-left: 0;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .card-body {
                padding: 1.5rem !important;
            }

            .btn-lg {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
            }
        }

        @media (max-width: 576px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .card-body {
                padding: 1rem !important;
            }
        }
    </style>
@endsection

@section('scripts')
    <script>
        // COUNTRY CODE
        const inputs = document.querySelectorAll(".phonenum");

        inputs.forEach(input => {
            window.intlTelInput(input, {
                initialCountry: "us",
                separateDialCode: true,
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js", // For formatting
            });
        });
        // COUNTRY CODE
        console.log('Registration page script loaded');

        // Form validation and customer existence check functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up customer check functionality');

            const form = document.querySelector('form[action*="customer.register.post"]');
            const errorDiv = document.getElementById('customer-exists-error');
            const successDiv = document.getElementById('customer-check-success');
            const submitBtn = form.querySelector('button[type="submit"]');

            console.log('Elements found:', {
                form: !!form,
                errorDiv: !!errorDiv,
                successDiv: !!successDiv,
                submitBtn: !!submitBtn
            });

            // If submit button not found, try alternative selectors
            if (!submitBtn) {
                console.log('Submit button not found, trying alternative selectors...');
                const alternativeSubmitBtn = form.querySelector('.btn-primary[type="submit"]') ||
                    form.querySelector('button[type="submit"]') ||
                    form.querySelector('.btn-primary');
                console.log('Alternative submit button found:', !!alternativeSubmitBtn);
            }

            // Function to show/hide messages
            function showMessage(type, message) {
                console.log('Showing message:', type, message);
                // Hide all messages first
                errorDiv.classList.add('d-none');
                successDiv.classList.add('d-none');

                if (type === 'error') {
                    errorDiv.classList.remove('d-none');
                    errorDiv.querySelector('strong').textContent = 'Customer Already Exists!';
                } else if (type === 'success') {
                    successDiv.classList.remove('d-none');
                }
            }

            // Function to check customer existence
            async function checkCustomerExists(formData) {
                try {
                    console.log('Checking customer existence...');

                    // Get CSRF token from meta tag
                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    console.log('CSRF token found:', csrfToken ? 'Yes' : 'No');

                    const requestData = {
                        email: formData.get('email'),
                        contact_no: formData.get('contact_no')
                    };

                    console.log('Request data:', requestData);

                    const response = await fetch('{{ route('customer.check-exists') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    });

                    console.log('Response status:', response.status);
                    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    console.log('Customer check response:', data);
                    return data;
                } catch (error) {
                    console.error('Error checking customer existence:', error);
                    return {
                        exists: false,
                        message: 'Unable to check customer existence: ' + error.message
                    };
                }
            }

            // Handle form submission with validation and customer check
            form.addEventListener('submit', async function(event) {
                event.preventDefault();
                event.stopPropagation();

                console.log('Form submission intercepted');

                // Get the submit button again in case it wasn't found initially
                const currentSubmitBtn = form.querySelector('button[type="submit"]') ||
                    form.querySelector('.btn-primary[type="submit"]') ||
                    form.querySelector('.btn-primary');

                if (!currentSubmitBtn) {
                    console.error('Submit button not found, proceeding without UI feedback');
                }

                // Show loading state
                const originalBtnText = currentSubmitBtn ? currentSubmitBtn.innerHTML : '';
                if (currentSubmitBtn) {
                    currentSubmitBtn.innerHTML =
                        '<i class="fas fa-spinner fa-spin me-2"></i>Checking...';
                    currentSubmitBtn.disabled = true;
                }

                // Get form data
                const formData = new FormData(form);

                console.log('Form data:', {
                    email: formData.get('email'),
                    contact_no: formData.get('contact_no')
                });

                // Check if customer exists first (regardless of validation)
                const result = await checkCustomerExists(formData);

                console.log('Check result:', result);

                if (result.exists) {
                    // Show error message
                    showMessage('error', result.message);

                    // Highlight the problematic field
                    const fieldName = result.field;
                    const field = form.querySelector(`[name="${fieldName}"]`);
                    if (field) {
                        field.classList.add('is-invalid');
                        field.focus();
                    }

                    // Reset button
                    if (currentSubmitBtn) {
                        currentSubmitBtn.innerHTML = originalBtnText;
                        currentSubmitBtn.disabled = false;
                    }
                    return; // Stop here if customer exists
                }

                // If customer doesn't exist, now check form validation
                if (!form.checkValidity()) {
                    console.log('Form validation failed');
                    form.classList.add('was-validated');

                    // Reset button
                    if (currentSubmitBtn) {
                        currentSubmitBtn.innerHTML = originalBtnText;
                        currentSubmitBtn.disabled = false;
                    }
                    return;
                }

                // Show success message briefly
                showMessage('success', result.message);

                // Submit the form after a short delay
                setTimeout(() => {
                    console.log('Submitting form...');
                    form.submit();
                }, 1000);
            });

            // Clear messages when user starts typing
            const emailInput = form.querySelector('input[name="email"]');
            const contactInput = form.querySelector('input[name="contact_no"]');

            [emailInput, contactInput].forEach(input => {
                if (input) {
                    input.addEventListener('input', function() {
                        errorDiv.classList.add('d-none');
                        successDiv.classList.add('d-none');
                        this.classList.remove('is-invalid');
                    });
                }
            });

        });
    </script>
@endsection
