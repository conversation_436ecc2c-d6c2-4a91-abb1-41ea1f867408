<?php

namespace App\Http\Controllers;

use App\Models\AdPlan;
use App\Models\AdMaterial;
use App\Models\Device;
use Illuminate\Http\Request;

class AdController extends Controller
{
    public function index()
    {
        $adPlans = AdPlan::all();
        return view('adplan.adplanlst', compact('adPlans'));
    }

    public function create()
    {
        $adMaterials = AdMaterial::all();
        return view('adplan.adplanadd', compact('adMaterials'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_time' => 'required',
            'end_time' => 'required',
            'is_active' => 'boolean',
            'priority' => 'integer',
            'ad_materials' => 'array',
        ]);

        $adPlan = AdPlan::create([
            'name' => $request->name,
            'description' => $request->description,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'is_active' => $request->is_active ?? false,
            'priority' => $request->priority ?? 0,
        ]);

        if ($request->has('ad_materials')) {
            foreach ($request->ad_materials as $materialId => $details) {
                $adPlan->adMaterials()->attach($materialId, [
                    'display_order' => $details['order'] ?? 0,
                    'display_time' => $details['time'] ?? 8,
                ]);
            }
        }

        return redirect()->route('adplan.index')->with('success', 'Advertisement plan created successfully.');
    }

    public function show(AdPlan $adplan)
    {
        return view('adplan.adplanshow', compact('adplan'));
    }

    public function edit(AdPlan $adplan)
    {
        $adMaterials = AdMaterial::all();
        return view('adplan.adplanedt', compact('adplan', 'adMaterials'));
    }

    public function update(Request $request, AdPlan $adplan)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_time' => 'required',
            'end_time' => 'required',
            'is_active' => 'boolean',
            'priority' => 'integer',
            'ad_materials' => 'array',
        ]);

        $adplan->update([
            'name' => $request->name,
            'description' => $request->description,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'is_active' => $request->is_active ?? false,
            'priority' => $request->priority ?? 0,
        ]);

        // Sync ad materials
        $adplan->adMaterials()->detach();
        if ($request->has('ad_materials')) {
            foreach ($request->ad_materials as $materialId => $details) {
                $adplan->adMaterials()->attach($materialId, [
                    'display_order' => $details['order'] ?? 0,
                    'display_time' => $details['time'] ?? 8,
                ]);
            }
        }

        return redirect()->route('adplan.index')->with('success', 'Advertisement plan updated successfully.');
    }

    public function destroy(AdPlan $adplan)
    {
        $adplan->delete();
        return redirect()->route('adplan.index')->with('success', 'Advertisement plan deleted successfully.');
    }

    // API methods
    public function getAds(Request $request)
    {
        // Validate request
        $request->validate([
            'sn' => 'required|string',
        ]);

        $deviceSn = $request->sn;
        $currentTime = now()->format('H:i');

        // Find the device by serial number
        $device = Device::where('sn', $deviceSn)->first();

        if (!$device) {
            return response()->json([
                "status" => 0,
                "error" => "Device not found"
            ], 404);
        }

        // Get active groups for this device
        $activeGroupIds = $device->groups()
            ->where('is_active', true)
            ->pluck('device_groups.id');

        if ($activeGroupIds->isEmpty()) {
            return $this->getDefaultAds();
        }

        // Get active ad plans for the device through its groups
        $activePlan = AdPlan::whereHas('groups', function ($query) use ($activeGroupIds) {
            $query->whereIn('device_groups.id', $activeGroupIds);
        })
            ->where('is_active', true)
            ->where('start_time', '<=', $currentTime)
            ->where('end_time', '>=', $currentTime)
            ->orderBy('priority', 'desc')
            ->with(['adMaterials' => function ($query) {
                $query->orderBy('pivot_display_order', 'asc');
            }])
            ->first();

        if (!$activePlan) {
            return $this->getDefaultAds();
        }

        // Format active plan for response
        $adsList = [];
        foreach ($activePlan->adMaterials as $material) {
            $adsList[] = [
                "type" => $material->type,
                "time" => $material->pivot->display_time,
                "file" => getenv('APP_URL') . $material->file_url
            ];
        }

        $ads = [
            "status" => 1,
            "list" => [
                [
                    "list" => $adsList,
                    "start" => $activePlan->start_time,
                    "end" => $activePlan->end_time,
                ]
            ],
            "bottom" => [
                "height" => 190,
                "img" => "https://pixelflow.com.pk/storage/images/footer.png",
            ]
        ];

        return response()->json($ads);
    }

    /**
     * Get default ads when no active plans are found
     */
    private function getDefaultAds()
    {
        return response()->json([
            "status" => 1,
            "list" => [
                [
                    "list" => [
                        ["type" => "image", "time" => 8, "file" => "https://www.hbl.com/assets/images/HBL_TikTok_807x567.png"],
                        ["type" => "image", "time" => 8, "file" => "https://www.hbl.com/assets/images/HBL-Digital-Account-Banner-970x460-06-01-2024_Day.png"],
                        ["type" => "video", "time" => "", "file" => "https://pixelflow.com.pk/storage/videos/video1.mp4"],
                        ["type" => "video", "time" => "", "file" => "https://pixelflow.com.pk/storage/videos/video2.mp4"],
                    ],
                    "start" => "00:00",
                    "end" => "23:59",
                ]
            ],
            "bottom" => [
                "height" => 190,
                "img" => "https://pixelflow.com.pk/storage/images/footer.png",
            ]
        ]);
    }

    public function rent(Request $request)
    {
        // echo "rent called \n";
        return response()->json([
            "status" => 1,
            "msg" => "renting",
            "success_text" => "rental successfully",
            "fail_text" => "rental failure",
            "success_content" => "Please take out the shared power bank",
            "fail_content" => "Please scan the code again to rent",
            "success_cross" => "https://pixelflow.com.pk/storage/images/success.png",
            "fail_cross" => "https://pixelflow.com.pk/storage/images/fail.png",
        ]);
    }

    public function getVoicePrompts()
    {
        //echo "get audio called. \n";
        return response()->json([
            "boot_strap" => "https://pixelflow.com.pk/storage/audio/boot_strap.mp3",
            "networking_success" => "https://pixelflow.com.pk/storage/audio/networking_success.mp3",
            "networking_fail" => "https://pixelflow.com.pk/storage/audio/networking_fail.mp3",
            "rent_success" => "https://pixelflow.com.pk/storage/audio/rent_success.mp3",
            "rent_fail" => "https://pixelflow.com.pk/storage/audio/rent_fail.mp3",
            "return_success" => "https://pixelflow.com.pk/storage/audio/return_success.mp3",
            "return_fail" => "https://pixelflow.com.pk/storage/audio/return_fail.mp3",
        ]);
    }

    /**
     * Preview the advertisement plan
     *
     * @param  \App\Models\AdPlan  $adplan
     * @return \Illuminate\Http\Response
     */
    public function preview(AdPlan $adplan)
    {
        // Get all materials sorted by display order
        $materials = $adplan->adMaterials()
            ->orderBy('pivot_display_order')
            ->get();

        if ($materials->isEmpty()) {
            return redirect()->route('adplan.show', $adplan->id)
                ->with('error', 'This plan has no materials to preview.');
        }

        // Prepare materials for the preview
        $previewMaterials = [];

        foreach ($materials as $material) {
            $previewMaterials[] = [
                'id' => $material->id,
                'name' => $material->name,
                'type' => $material->type,
                'file_url' => $material->file_url,
                'display_time' => $material->pivot->display_time,
                'display_order' => $material->pivot->display_order
            ];
        }

        return view('adplan.preview', [
            'adplan' => $adplan,
            'materials' => $previewMaterials
        ]);
    }
}
