@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title m-0">Device Data Details</h4>
                        <div>
                            <a href="{{ route('device-data.index') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-list mr-1"></i> Back to List
                            </a>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-borderless mb-0">
                                        <tbody>
                                            <tr>
                                                <th width="40%">ID</th>
                                                <td>{{ $deviceData->id }}</td>
                                            </tr>
                                            <tr>
                                                <th>Serial Number</th>
                                                <td>{{ $deviceData->sn }}</td>
                                            </tr>
                                            <tr>
                                                <th>Command</th>
                                                <td>{{ $deviceData->cmd }}</td>
                                            </tr>
                                            <tr>
                                                <th>Message</th>
                                                <td>{{ $deviceData->msg }}</td>
                                            </tr>
                                            <tr>
                                                <th>AIMS</th>
                                                <td>{{ $deviceData->aims }}</td>
                                            </tr>
                                            <tr>
                                                <th>Status</th>
                                                <td>{{ $deviceData->st }}</td>
                                            </tr>
                                            <tr>
                                                <th>Created At</th>
                                                <td>{{ $deviceData->created_at->format('Y-m-d H:i:s') }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0">Data Values</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-borderless mb-0">
                                        <tbody>
                                            <tr>
                                                <th width="40%">N Value</th>
                                                <td>{{ $deviceData->n }}</td>
                                            </tr>
                                            <tr>
                                                <th>R Value</th>
                                                <td>{{ $deviceData->r }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card border">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0">JSON Data</h5>
                                </div>
                                <div class="card-body">
                                    <pre class="json-data">{{ json_encode($deviceData->data, JSON_PRETTY_PRINT) }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Actions</h4>
                    <div class="d-flex flex-column">
                        <a href="{{ route('device-data.by-device', $deviceData->sn) }}" class="btn btn-outline-primary mb-2">
                            <i class="fas fa-list mr-1"></i> View All Data for This Device
                        </a>
                        <form action="{{ route('device-data.destroy', $deviceData->id) }}" method="POST">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger mb-2 w-100" onclick="return confirm('Are you sure you want to delete this data?')">
                                <i class="fas fa-trash mr-1"></i> Delete This Record
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<style>
    .json-data {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        max-height: 400px;
        overflow-y: auto;
    }
</style>
@endsection