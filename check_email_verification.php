<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

$user = App\Models\User::where('email', '<EMAIL>')->first();

if ($user) {
    echo "User: {$user->name}\n";
    echo "Email: {$user->email}\n";
    echo "Email verified: " . ($user->email_verified_at ? "Yes ({$user->email_verified_at})" : "No") . "\n";
    echo "Status: {$user->status}\n";
} else {
    echo "User not found\n";
}
