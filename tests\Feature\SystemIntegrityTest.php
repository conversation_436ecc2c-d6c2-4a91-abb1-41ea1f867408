<?php

namespace Tests\Feature;

use App\Models\User;
use App\Traits\Auditable;
use Tests\TestCase;

class SystemIntegrityTest extends TestCase
{
    /** @test */
    public function middleware_is_properly_registered()
    {
        $kernel = app(\Illuminate\Contracts\Http\Kernel::class);

        // Use reflection to access protected property
        $reflection = new \ReflectionClass($kernel);
        $property = $reflection->getProperty('middlewareAliases');
        $property->setAccessible(true);
        $middlewareAliases = $property->getValue($kernel);

        $this->assertArrayHasKey('track.activity', $middlewareAliases);
        $this->assertEquals(\App\Http\Middleware\TrackUserActivity::class, $middlewareAliases['track.activity']);
    }

    /** @test */
    public function user_model_has_auditable_trait()
    {
        // Check that User model uses Auditable trait
        $traits = class_uses(User::class);
        $this->assertContains(Auditable::class, $traits);
    }

    /** @test */
    public function two_factor_authentication_fields_exist_on_user_model()
    {
        $user = new User();

        // Check that 2FA fields are fillable
        $fillable = $user->getFillable();
        $this->assertContains('two_factor_enabled', $fillable);
        $this->assertContains('two_factor_secret', $fillable);
        $this->assertContains('two_factor_recovery_codes', $fillable);

        // Check that 2FA fields are hidden
        $hidden = $user->getHidden();
        $this->assertContains('two_factor_secret', $hidden);
        $this->assertContains('two_factor_recovery_codes', $hidden);
    }

    /** @test */
    public function required_classes_exist()
    {
        // Check that all required classes exist
        $this->assertTrue(class_exists(\App\Models\AuditTrail::class));
        $this->assertTrue(class_exists(\App\Models\UserActivity::class));
        $this->assertTrue(class_exists(\App\Models\DatabaseBackup::class));
        $this->assertTrue(class_exists(\App\Services\ActivityLogService::class));
        $this->assertTrue(class_exists(\App\Http\Middleware\TrackUserActivity::class));
        $this->assertTrue(class_exists(\App\Http\Controllers\Auth\TwoFactorAuthController::class));
    }

    /** @test */
    public function user_model_has_two_factor_methods()
    {
        $user = new User();

        // Check that 2FA methods exist
        $this->assertTrue(method_exists($user, 'generateRecoveryCodes'));
        $this->assertTrue(method_exists($user, 'getTwoFactorSecretAttribute'));
        $this->assertTrue(method_exists($user, 'setTwoFactorSecretAttribute'));
        $this->assertTrue(method_exists($user, 'getTwoFactorRecoveryCodesAttribute'));
        $this->assertTrue(method_exists($user, 'setTwoFactorRecoveryCodesAttribute'));
    }
}
