<?php

namespace Tests\Feature;

use App\Models\User;
use App\Traits\Auditable;
use Tests\TestCase;

class SystemIntegrityTest extends TestCase
{
    /** @test */
    public function middleware_is_properly_registered()
    {
        $kernel = app(\Illuminate\Contracts\Http\Kernel::class);

        // Use reflection to access protected property
        $reflection = new \ReflectionClass($kernel);
        $property = $reflection->getProperty('middlewareAliases');
        $property->setAccessible(true);
        $middlewareAliases = $property->getValue($kernel);

        $this->assertArrayHasKey('track.activity', $middlewareAliases);
        $this->assertEquals(\App\Http\Middleware\TrackUserActivity::class, $middlewareAliases['track.activity']);
    }

    /** @test */
    public function audit_trail_is_created_when_user_is_updated()
    {
        // Ensure no audit trails exist initially
        $this->assertEquals(0, AuditTrail::count());

        // Act as the test user
        $this->actingAs($this->user);

        // Update the user
        $this->user->update(['name' => 'Updated Name']);

        // Assert audit trail was created
        $this->assertEquals(1, AuditTrail::count());

        $auditTrail = AuditTrail::first();
        $this->assertEquals($this->user->id, $auditTrail->user_id);
        $this->assertEquals('updated', $auditTrail->action);
        $this->assertEquals(User::class, $auditTrail->auditable_type);
        $this->assertEquals($this->user->id, $auditTrail->auditable_id);
    }

    /** @test */
    public function user_activity_can_be_logged()
    {
        // Ensure no activities exist initially
        $this->assertEquals(0, UserActivity::count());

        // Act as the test user
        $this->actingAs($this->user);

        // Log an activity
        ActivityLogService::log('test_action', 'test_module', 'Test description');

        // Assert activity was logged
        $this->assertEquals(1, UserActivity::count());

        $activity = UserActivity::first();
        $this->assertEquals($this->user->id, $activity->user_id);
        $this->assertEquals('test_action', $activity->action);
        $this->assertEquals('test_module', $activity->module);
        $this->assertEquals('Test description', $activity->description);
    }

    /** @test */
    public function two_factor_authentication_fields_exist_on_user_model()
    {
        // Check that 2FA fields are fillable
        $fillable = $this->user->getFillable();
        $this->assertContains('two_factor_enabled', $fillable);
        $this->assertContains('two_factor_secret', $fillable);
        $this->assertContains('two_factor_recovery_codes', $fillable);

        // Check that 2FA fields are hidden
        $hidden = $this->user->getHidden();
        $this->assertContains('two_factor_secret', $hidden);
        $this->assertContains('two_factor_recovery_codes', $hidden);
    }

    /** @test */
    public function user_can_generate_recovery_codes()
    {
        $recoveryCodes = $this->user->generateRecoveryCodes();

        $this->assertIsArray($recoveryCodes);
        $this->assertCount(8, $recoveryCodes);

        // Refresh the user model to get updated data
        $this->user->refresh();
        $this->assertNotNull($this->user->two_factor_recovery_codes);
    }

    /** @test */
    public function database_backup_model_has_required_relationships()
    {
        $backup = DatabaseBackup::create([
            'filename' => 'test_backup.sql.gz',
            'path' => '/path/to/backup.sql.gz',
            'size' => 1024,
            'created_by' => $this->user->id,
        ]);

        $this->assertInstanceOf(User::class, $backup->creator);
        $this->assertEquals($this->user->id, $backup->creator->id);
    }

    /** @test */
    public function auditable_trait_is_properly_configured()
    {
        // Check that User model uses Auditable trait
        $traits = class_uses(User::class);
        $this->assertContains(Auditable::class, $traits);
    }

    /** @test */
    public function activity_log_service_can_log_login_and_logout()
    {
        $this->actingAs($this->user);

        // Test login logging
        ActivityLogService::logLogin('User logged in successfully');

        $this->assertEquals(1, UserActivity::count());
        $loginActivity = UserActivity::first();
        $this->assertEquals('login', $loginActivity->action);
        $this->assertEquals('authentication', $loginActivity->module);

        // Test logout logging
        ActivityLogService::logLogout('User logged out');

        $this->assertEquals(2, UserActivity::count());
        $logoutActivity = UserActivity::latest()->first();
        $this->assertEquals('logout', $logoutActivity->action);
        $this->assertEquals('authentication', $logoutActivity->module);
    }
}
