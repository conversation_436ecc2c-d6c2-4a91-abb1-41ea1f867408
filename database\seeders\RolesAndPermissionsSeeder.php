<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // User management
            ['name' => 'View Users', 'slug' => 'view-users', 'description' => 'Can view user list and details'],
            ['name' => 'Create Users', 'slug' => 'create-users', 'description' => 'Can create new users'],
            ['name' => 'Edit Users', 'slug' => 'edit-users', 'description' => 'Can edit existing users'],
            ['name' => 'Delete Users', 'slug' => 'delete-users', 'description' => 'Can delete users'],
            ['name' => 'Assign Roles', 'slug' => 'assign-roles', 'description' => 'Can assign roles to users'],

            // Role management
            ['name' => 'View Roles', 'slug' => 'view-roles', 'description' => 'Can view role list and details'],
            ['name' => 'Create Roles', 'slug' => 'create-roles', 'description' => 'Can create new roles'],
            ['name' => 'Edit Roles', 'slug' => 'edit-roles', 'description' => 'Can edit existing roles'],
            ['name' => 'Delete Roles', 'slug' => 'delete-roles', 'description' => 'Can delete roles'],

            // Permission management
            ['name' => 'View Permissions', 'slug' => 'view-permissions', 'description' => 'Can view permission list and details'],
            ['name' => 'Create Permissions', 'slug' => 'create-permissions', 'description' => 'Can create new permissions'],
            ['name' => 'Edit Permissions', 'slug' => 'edit-permissions', 'description' => 'Can edit existing permissions'],
            ['name' => 'Delete Permissions', 'slug' => 'delete-permissions', 'description' => 'Can delete permissions'],

            // Device management
            ['name' => 'View Devices', 'slug' => 'view-devices', 'description' => 'Can view device list and details'],
            ['name' => 'Create Devices', 'slug' => 'create-devices', 'description' => 'Can create new devices'],
            ['name' => 'Edit Devices', 'slug' => 'edit-devices', 'description' => 'Can edit existing devices'],
            ['name' => 'Delete Devices', 'slug' => 'delete-devices', 'description' => 'Can delete devices'],
            ['name' => 'Control Devices', 'slug' => 'control-devices', 'description' => 'Can control devices (reboot, eject)'],

            // Ad Material management
            ['name' => 'View Ad Materials', 'slug' => 'view-ad-materials', 'description' => 'Can view ad material list and details'],
            ['name' => 'Create Ad Materials', 'slug' => 'create-ad-materials', 'description' => 'Can create new ad materials'],
            ['name' => 'Edit Ad Materials', 'slug' => 'edit-ad-materials', 'description' => 'Can edit existing ad materials'],
            ['name' => 'Delete Ad Materials', 'slug' => 'delete-ad-materials', 'description' => 'Can delete ad materials'],

            // Ad Schedule management
            ['name' => 'View Ad Plans', 'slug' => 'view-ad-plans', 'description' => 'Can view ad plan list and details'],
            ['name' => 'Create Ad Plans', 'slug' => 'create-ad-plans', 'description' => 'Can create new ad plans'],
            ['name' => 'Edit Ad Plans', 'slug' => 'edit-ad-plans', 'description' => 'Can edit existing ad plans'],
            ['name' => 'Delete Ad Plans', 'slug' => 'delete-ad-plans', 'description' => 'Can delete ad plans'],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }

        // Create roles
        $adminRole = Role::create([
            'name' => 'Administrator',
            'slug' => 'administrator',
            'description' => 'Full access to all system features',
        ]);

        $managerRole = Role::create([
            'name' => 'Manager',
            'slug' => 'manager',
            'description' => 'Access to most system features',
        ]);

        $userRole = Role::create([
            'name' => 'User',
            'slug' => 'user',
            'description' => 'Basic access to the system',
        ]);

        // Assign all permissions to admin role
        $adminRole->permissions()->attach(Permission::all());

        // Assign specific permissions to manager role
        $managerRole->permissions()->attach(
            Permission::whereIn('slug', [
                'view-users',
                'create-users',
                'edit-users',
                'view-roles',
                'view-permissions',
            ])->get()
        );

        // Assign basic permissions to user role
        $userRole->permissions()->attach(
            Permission::whereIn('slug', [
                'view-users',
            ])->get()
        );

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        // Assign admin role to admin user
        $admin->roles()->attach($adminRole);
    }
}
