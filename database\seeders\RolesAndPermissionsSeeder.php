<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Dashboard
            ['name' => 'View Dashboard', 'slug' => 'view-dashboard', 'description' => 'Can access main dashboard'],

            // User management
            ['name' => 'View Users', 'slug' => 'view-users', 'description' => 'Can view user list and details'],
            ['name' => 'Create Users', 'slug' => 'create-users', 'description' => 'Can create new users'],
            ['name' => 'Edit Users', 'slug' => 'edit-users', 'description' => 'Can edit existing users'],
            ['name' => 'Delete Users', 'slug' => 'delete-users', 'description' => 'Can delete users'],
            ['name' => 'Assign Roles', 'slug' => 'assign-roles', 'description' => 'Can assign roles to users'],
            ['name' => 'Manage User Status', 'slug' => 'manage-user-status', 'description' => 'Can activate/deactivate users'],
            ['name' => 'Reset User Password', 'slug' => 'reset-user-password', 'description' => 'Can reset user passwords'],
            ['name' => 'View User Profile', 'slug' => 'view-user-profile', 'description' => 'Can view user profiles'],

            // Role management
            ['name' => 'View Roles', 'slug' => 'view-roles', 'description' => 'Can view role list and details'],
            ['name' => 'Create Roles', 'slug' => 'create-roles', 'description' => 'Can create new roles'],
            ['name' => 'Edit Roles', 'slug' => 'edit-roles', 'description' => 'Can edit existing roles'],
            ['name' => 'Delete Roles', 'slug' => 'delete-roles', 'description' => 'Can delete roles'],

            // Permission management
            ['name' => 'View Permissions', 'slug' => 'view-permissions', 'description' => 'Can view permission list and details'],
            ['name' => 'Create Permissions', 'slug' => 'create-permissions', 'description' => 'Can create new permissions'],
            ['name' => 'Edit Permissions', 'slug' => 'edit-permissions', 'description' => 'Can edit existing permissions'],
            ['name' => 'Delete Permissions', 'slug' => 'delete-permissions', 'description' => 'Can delete permissions'],

            // Role management
            ['name' => 'View Roles', 'slug' => 'view-roles', 'description' => 'Can view role list and details'],
            ['name' => 'Create Roles', 'slug' => 'create-roles', 'description' => 'Can create new roles'],
            ['name' => 'Edit Roles', 'slug' => 'edit-roles', 'description' => 'Can edit existing roles'],
            ['name' => 'Delete Roles', 'slug' => 'delete-roles', 'description' => 'Can delete roles'],

            // Permission management
            ['name' => 'View Permissions', 'slug' => 'view-permissions', 'description' => 'Can view permission list and details'],
            ['name' => 'Create Permissions', 'slug' => 'create-permissions', 'description' => 'Can create new permissions'],
            ['name' => 'Edit Permissions', 'slug' => 'edit-permissions', 'description' => 'Can edit existing permissions'],
            ['name' => 'Delete Permissions', 'slug' => 'delete-permissions', 'description' => 'Can delete permissions'],

            // Device management
            ['name' => 'View Devices', 'slug' => 'view-devices', 'description' => 'Can view device list and details'],
            ['name' => 'Create Devices', 'slug' => 'create-devices', 'description' => 'Can create new devices'],
            ['name' => 'Edit Devices', 'slug' => 'edit-devices', 'description' => 'Can edit existing devices'],
            ['name' => 'Delete Devices', 'slug' => 'delete-devices', 'description' => 'Can delete devices'],
            ['name' => 'Control Devices', 'slug' => 'control-devices', 'description' => 'Can control devices (reboot, eject)'],

            // Ad Material management
            ['name' => 'View Ad Materials', 'slug' => 'view-ad-materials', 'description' => 'Can view ad material list and details'],
            ['name' => 'Create Ad Materials', 'slug' => 'create-ad-materials', 'description' => 'Can create new ad materials'],
            ['name' => 'Edit Ad Materials', 'slug' => 'edit-ad-materials', 'description' => 'Can edit existing ad materials'],
            ['name' => 'Delete Ad Materials', 'slug' => 'delete-ad-materials', 'description' => 'Can delete ad materials'],

            // Device management
            ['name' => 'View Devices', 'slug' => 'view-devices', 'description' => 'Can view device list and details'],
            ['name' => 'Create Devices', 'slug' => 'create-devices', 'description' => 'Can create new devices'],
            ['name' => 'Edit Devices', 'slug' => 'edit-devices', 'description' => 'Can edit existing devices'],
            ['name' => 'Delete Devices', 'slug' => 'delete-devices', 'description' => 'Can delete devices'],
            ['name' => 'Control Devices', 'slug' => 'control-devices', 'description' => 'Can control devices (reboot, eject)'],
            ['name' => 'View Device Data', 'slug' => 'view-device-data', 'description' => 'Can view device data and logs'],
            ['name' => 'Delete Device Data', 'slug' => 'delete-device-data', 'description' => 'Can delete device data'],

            // Device Group management
            ['name' => 'View Device Groups', 'slug' => 'view-device-groups', 'description' => 'Can view device group list and details'],
            ['name' => 'Create Device Groups', 'slug' => 'create-device-groups', 'description' => 'Can create new device groups'],
            ['name' => 'Edit Device Groups', 'slug' => 'edit-device-groups', 'description' => 'Can edit existing device groups'],
            ['name' => 'Delete Device Groups', 'slug' => 'delete-device-groups', 'description' => 'Can delete device groups'],
            ['name' => 'Assign Devices to Groups', 'slug' => 'assign-devices-groups', 'description' => 'Can assign devices to groups'],
            ['name' => 'Assign Plans to Groups', 'slug' => 'assign-plans-groups', 'description' => 'Can assign ad plans to groups'],

            // Powerbank management
            ['name' => 'View Powerbanks', 'slug' => 'view-powerbanks', 'description' => 'Can view powerbank list and details'],
            ['name' => 'Create Powerbanks', 'slug' => 'create-powerbanks', 'description' => 'Can create new powerbanks'],
            ['name' => 'Edit Powerbanks', 'slug' => 'edit-powerbanks', 'description' => 'Can edit existing powerbanks'],
            ['name' => 'Delete Powerbanks', 'slug' => 'delete-powerbanks', 'description' => 'Can delete powerbanks'],
            ['name' => 'Rent Powerbanks', 'slug' => 'rent-powerbanks', 'description' => 'Can rent powerbanks to customers'],
            ['name' => 'Return Powerbanks', 'slug' => 'return-powerbanks', 'description' => 'Can process powerbank returns'],
            ['name' => 'Mark Powerbanks Charged', 'slug' => 'mark-powerbanks-charged', 'description' => 'Can mark powerbanks as charged'],
            ['name' => 'Report Powerbank Issues', 'slug' => 'report-powerbank-issues', 'description' => 'Can report powerbank issues'],
            ['name' => 'Resolve Powerbank Issues', 'slug' => 'resolve-powerbank-issues', 'description' => 'Can resolve powerbank issues'],
            ['name' => 'View Powerbank Issues', 'slug' => 'view-powerbank-issues', 'description' => 'Can view powerbank issues'],

            // Customer management
            ['name' => 'View Customers', 'slug' => 'view-customers', 'description' => 'Can view customer list and details'],
            ['name' => 'Create Customers', 'slug' => 'create-customers', 'description' => 'Can create new customers'],
            ['name' => 'Edit Customers', 'slug' => 'edit-customers', 'description' => 'Can edit existing customers'],
            ['name' => 'Delete Customers', 'slug' => 'delete-customers', 'description' => 'Can delete customers'],
            ['name' => 'View Customer Registrations', 'slug' => 'view-customer-registrations', 'description' => 'Can view customer registration reports'],
            ['name' => 'View Customer Reports', 'slug' => 'view-customer-reports', 'description' => 'Can view customer reports'],
            ['name' => 'Send Customer Reminders', 'slug' => 'send-customer-reminders', 'description' => 'Can send reminders to customers'],

            // Rental management
            ['name' => 'View Rentals', 'slug' => 'view-rentals', 'description' => 'Can view rental list and details'],
            ['name' => 'Create Rentals', 'slug' => 'create-rentals', 'description' => 'Can create new rentals'],
            ['name' => 'Edit Rentals', 'slug' => 'edit-rentals', 'description' => 'Can edit existing rentals'],
            ['name' => 'Delete Rentals', 'slug' => 'delete-rentals', 'description' => 'Can delete rentals'],

            // Ad Material management
            ['name' => 'View Ad Materials', 'slug' => 'view-ad-materials', 'description' => 'Can view ad material list and details'],
            ['name' => 'Create Ad Materials', 'slug' => 'create-ad-materials', 'description' => 'Can create new ad materials'],
            ['name' => 'Edit Ad Materials', 'slug' => 'edit-ad-materials', 'description' => 'Can edit existing ad materials'],
            ['name' => 'Delete Ad Materials', 'slug' => 'delete-ad-materials', 'description' => 'Can delete ad materials'],

            // Ad Schedule management
            ['name' => 'View Ad Plans', 'slug' => 'view-ad-plans', 'description' => 'Can view ad plan list and details'],
            ['name' => 'Create Ad Plans', 'slug' => 'create-ad-plans', 'description' => 'Can create new ad plans'],
            ['name' => 'Edit Ad Plans', 'slug' => 'edit-ad-plans', 'description' => 'Can edit existing ad plans'],
            ['name' => 'Delete Ad Plans', 'slug' => 'delete-ad-plans', 'description' => 'Can delete ad plans'],
            ['name' => 'Preview Ad Plans', 'slug' => 'preview-ad-plans', 'description' => 'Can preview ad plans'],

            // Profile management
            ['name' => 'View Profile', 'slug' => 'view-profile', 'description' => 'Can view own profile'],
            ['name' => 'Edit Profile', 'slug' => 'edit-profile', 'description' => 'Can edit own profile'],
            ['name' => 'Delete Profile', 'slug' => 'delete-profile', 'description' => 'Can delete own profile'],

            // Two-Factor Authentication
            ['name' => 'Setup 2FA', 'slug' => 'setup-2fa', 'description' => 'Can setup two-factor authentication'],
            ['name' => 'Manage 2FA', 'slug' => 'manage-2fa', 'description' => 'Can manage two-factor authentication'],

            // System Administration
            ['name' => 'View User Activity', 'slug' => 'view-user-activity', 'description' => 'Can view user activity logs'],
            ['name' => 'View Audit Trail', 'slug' => 'view-audit-trail', 'description' => 'Can view audit trail logs'],
            ['name' => 'Manage Backups', 'slug' => 'manage-backups', 'description' => 'Can create and manage database backups'],
            ['name' => 'Download Backups', 'slug' => 'download-backups', 'description' => 'Can download database backups'],
            ['name' => 'Delete Backups', 'slug' => 'delete-backups', 'description' => 'Can delete database backups'],

            // Device Commands
            ['name' => 'Send Device Commands', 'slug' => 'send-device-commands', 'description' => 'Can send commands to devices'],
            ['name' => 'Eject Powerbanks', 'slug' => 'eject-powerbanks', 'description' => 'Can eject powerbanks from devices'],
            ['name' => 'Reboot Devices', 'slug' => 'reboot-devices', 'description' => 'Can reboot devices'],

            // Reports and Analytics
            ['name' => 'View Reports', 'slug' => 'view-reports', 'description' => 'Can view system reports'],
            ['name' => 'Export Data', 'slug' => 'export-data', 'description' => 'Can export system data'],

            // System Settings
            ['name' => 'Manage System Settings', 'slug' => 'manage-system-settings', 'description' => 'Can manage system settings'],
            ['name' => 'View System Logs', 'slug' => 'view-system-logs', 'description' => 'Can view system logs'],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }

        // Create roles
        $superAdminRole = Role::create([
            'name' => 'Super Administrator',
            'slug' => 'super-administrator',
            'description' => 'Full system access with all permissions',
        ]);

        $adminRole = Role::create([
            'name' => 'Administrator',
            'slug' => 'administrator',
            'description' => 'Full access to most system features',
        ]);

        $managerRole = Role::create([
            'name' => 'Manager',
            'slug' => 'manager',
            'description' => 'Access to operational features',
        ]);

        $operatorRole = Role::create([
            'name' => 'Operator',
            'slug' => 'operator',
            'description' => 'Access to daily operations',
        ]);

        $viewerRole = Role::create([
            'name' => 'Viewer',
            'slug' => 'viewer',
            'description' => 'Read-only access to system',
        ]);

        // Assign all permissions to super admin role
        $superAdminRole->permissions()->attach(Permission::all());

        // Assign most permissions to admin role (excluding super admin only features)
        $adminRole->permissions()->attach(
            Permission::whereNotIn('slug', [
                'delete-users',
                'delete-roles',
                'delete-permissions',
                'manage-system-settings',
            ])->get()
        );

        // Assign operational permissions to manager role
        $managerRole->permissions()->attach(
            Permission::whereIn('slug', [
                'view-dashboard',
                'view-users',
                'create-users',
                'edit-users',
                'view-roles',
                'view-permissions',
                'view-devices',
                'create-devices',
                'edit-devices',
                'view-device-groups',
                'create-device-groups',
                'edit-device-groups',
                'view-powerbanks',
                'create-powerbanks',
                'edit-powerbanks',
                'rent-powerbanks',
                'return-powerbanks',
                'mark-powerbanks-charged',
                'view-customers',
                'create-customers',
                'edit-customers',
                'view-rentals',
                'create-rentals',
                'edit-rentals',
                'view-ad-materials',
                'create-ad-materials',
                'edit-ad-materials',
                'view-ad-plans',
                'create-ad-plans',
                'edit-ad-plans',
                'view-profile',
                'edit-profile',
                'setup-2fa',
                'manage-2fa',
            ])->get()
        );

        // Assign daily operation permissions to operator role
        $operatorRole->permissions()->attach(
            Permission::whereIn('slug', [
                'view-dashboard',
                'view-devices',
                'view-powerbanks',
                'rent-powerbanks',
                'return-powerbanks',
                'mark-powerbanks-charged',
                'report-powerbank-issues',
                'view-powerbank-issues',
                'view-customers',
                'view-rentals',
                'view-profile',
                'edit-profile',
                'setup-2fa',
                'manage-2fa',
            ])->get()
        );

        // Assign read-only permissions to viewer role
        $viewerRole->permissions()->attach(
            Permission::whereIn('slug', [
                'view-dashboard',
                'view-users',
                'view-devices',
                'view-device-groups',
                'view-powerbanks',
                'view-customers',
                'view-rentals',
                'view-ad-materials',
                'view-ad-plans',
                'view-profile',
                'setup-2fa',
                'manage-2fa',
            ])->get()
        );

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        // Assign admin role to admin user
        $admin->roles()->attach($adminRole);
    }
}
