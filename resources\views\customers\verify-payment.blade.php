@extends('layouts.customer')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Payment Verification</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">We'll verify your payment method with a small authorization (no actual charge).</p>

                        @error('payment')
                            <div class="alert alert-danger">{{ $message }}</div>
                        @enderror

                        <form id="credit-card-form" action="{{ route('customer.verify-payment') }}" method="POST">
                            @csrf
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="card_holder_name"
                                            name="card_holder_name" placeholder="Card Holder Name" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-credit-card"></i></span>
                                        <input type="text" class="form-control" id="card_number" name="card_number"
                                            placeholder="1234 1234 1234 1234" required>
                                        <div id="card-brand" class="input-group-text">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        <input type="text" class="form-control" id="expiry_date" name="expiry_date"
                                            placeholder="MM/YY" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="text" class="form-control" id="cvv" name="cvv"
                                            placeholder="CVV" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <button id="manual-submit" type="submit" class="btn btn-primary mt-3">
                                Verify Card
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            // Credit card input formatting
            document.addEventListener('DOMContentLoaded', function() {
                const cardBrandElement = document.getElementById('card-brand');

                document.getElementById('card_number').addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 16) value = value.slice(0, 16);

                    // Add spaces every 4 digits
                    let formattedValue = '';
                    for (let i = 0; i < value.length; i++) {
                        if (i > 0 && i % 4 === 0) formattedValue += ' ';
                        formattedValue += value[i];
                    }

                    e.target.value = formattedValue;

                    // Update card brand icon based on first digits
                    updateCardBrandIcon(value);
                });

                document.getElementById('expiry_date').addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 4) value = value.slice(0, 4);

                    // Format as MM/YY
                    if (value.length > 2) {
                        value = value.slice(0, 2) + '/' + value.slice(2);
                    }

                    e.target.value = value;
                });

                document.getElementById('cvv').addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 4) value = value.slice(0, 4);
                    e.target.value = value;
                });

                // Update card brand icon
                function updateCardBrandIcon(cardNumber) {
                    const brand = detectCardType(cardNumber);
                    let iconClass = 'fa-credit-card'; // default

                    // Set icon based on card brand
                    switch (brand) {
                        case 'visa':
                            iconClass = 'fa-cc-visa';
                            cardBrandElement.style.color = '#1A1F71';
                            break;
                        case 'mastercard':
                            iconClass = 'fa-cc-mastercard';
                            cardBrandElement.style.color = '#EB001B';
                            break;
                        case 'amex':
                            iconClass = 'fa-cc-amex';
                            cardBrandElement.style.color = '#006FCF';
                            break;
                        case 'discover':
                            iconClass = 'fa-cc-discover';
                            cardBrandElement.style.color = '#FF6600';
                            break;
                        default:
                            cardBrandElement.style.color = '';
                    }

                    cardBrandElement.innerHTML = `<i class="fab ${iconClass}"></i>`;
                }

                // Credit card type detection
                function detectCardType(number) {
                    // Card patterns
                    const patterns = {
                        visa: /^4/,
                        mastercard: /^5[1-5]/,
                        amex: /^3[47]/,
                        discover: /^6(?:011|5)/,
                        diners: /^3(?:0[0-5]|[68])/,
                        jcb: /^(?:2131|1800|35\d{3})/
                    };

                    // Check patterns
                    for (const [brand, pattern] of Object.entries(patterns)) {
                        if (pattern.test(number)) {
                            return brand;
                        }
                    }

                    return 'unknown';
                }
            });
        </script>
    @endpush

    @push('styles')
        <style>
            .input-group-text {
                background-color: #f8f9fa;
            }

            .input-group-text i {
                font-size: 18px;
            }

            #card-brand {
                min-width: 46px;
                justify-content: center;
            }

            #card-brand i {
                font-size: 20px;
            }

            .form-control:focus {
                border-color: #80bdff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }

            .is-valid {
                border-color: #28a745 !important;
            }

            .is-invalid {
                border-color: #dc3545 !important;
            }
        </style>
    @endpush
@endsection
