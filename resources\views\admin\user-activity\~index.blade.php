@extends('layouts.master')

@section('title', 'User Activity Log')

@section('styles')
    <style>
        .dataTables_wrapper .dataTables_filter {
            float: right;
            text-align: right;
            margin-bottom: 10px;
        }

        .dataTables_wrapper .dataTables_length {
            float: left;
            margin-bottom: 10px;
        }

        .dataTables_wrapper .dt-buttons {
            float: left;
            margin-left: 10px;
            margin-bottom: 10px;
        }

        .table td {
            vertical-align: middle;
        }

        .badge {
            font-size: 0.75em;
        }
    </style>
@endsection

@section('content')
    <div class="container-fluid">
        <!-- Page Heading -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <h1 class="h3 mb-0 text-gray-800">User Activity Log</h1>
            <div>
                @can('manage-user-activity')
                    <button type="button" class="btn btn-sm btn-success shadow-sm" id="exportBtn">
                        <i class="fas fa-download fa-sm text-white-50"></i> Export to CSV
                    </button>
                    <button type="button" class="btn btn-sm btn-danger shadow-sm" data-toggle="modal"
                        data-target="#clearLogsModal">
                        <i class="fas fa-trash fa-sm text-white-50"></i> Clear Old Logs
                    </button>
                @endcan
            </div>
        </div>

        <!-- Filters Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Filter Activities</h6>
            </div>
            <div class="card-body">
                <form id="filterForm">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="user_id">User</label>
                            <select class="form-control" id="user_id" name="user_id">
                                <option value="">All Users</option>
                                @foreach ($users as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="action">Action</label>
                            <select class="form-control" id="action" name="action">
                                <option value="">All Actions</option>
                                @foreach ($actions as $action)
                                    <option value="{{ $action }}">{{ ucfirst($action) }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="module">Module</label>
                            <select class="form-control" id="module" name="module">
                                <option value="">All Modules</option>
                                @foreach ($modules as $module)
                                    <option value="{{ $module }}">{{ ucfirst($module) }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="date_from">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="date_to">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to">
                        </div>
                        <div class="col-md-3 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-primary mr-2" id="applyFilters">
                                <i class="fas fa-filter"></i> Apply Filters
                            </button>
                            <button type="button" class="btn btn-secondary" id="resetFilters">
                                <i class="fas fa-sync"></i> Reset
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Activities Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Activity Log</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="activitiesTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Action</th>
                                <th>Module</th>
                                <th>Description</th>
                                <th>IP Address</th>
                                <th>Date & Time</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Clear Logs Modal -->
    @can('manage-user-activity')
        <div class="modal fade" id="clearLogsModal" tabindex="-1" role="dialog" aria-labelledby="clearLogsModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <form action="{{ route('admin.user-activity.clear') }}" method="POST">
                        @csrf
                        <div class="modal-header">
                            <h5 class="modal-title" id="clearLogsModalLabel">Clear Old Activity Logs</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="days">Delete logs older than</label>
                                <select class="form-control" id="days" name="days" required>
                                    <option value="30">30 days</option>
                                    <option value="60">60 days</option>
                                    <option value="90">90 days</option>
                                    <option value="180">180 days</option>
                                    <option value="365">365 days</option>
                                </select>
                            </div>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> Warning: This action cannot be undone. All activity
                                logs older than the selected period will be permanently deleted.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-danger">Clear Logs</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endcan
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            console.log('Document ready...');
            console.log('jQuery version:', $.fn.jquery);
            console.log('DataTables available:', typeof $.fn.DataTable);
            console.log('Initializing DataTable...');
            console.log('AJAX URL:', '{{ route('admin.user-activity.data') }}');

            // Check if table element exists
            if ($('#activitiesTable').length === 0) {
                console.error('Table element #activitiesTable not found!');
                return;
            }

            // Test with static data first
            try {
                console.log('Testing with static data...');
                var staticTable = $('#activitiesTable').DataTable({
                    data: [
                        ['John Doe', 'login', 'auth', 'User logged in', '127.0.0.1',
                            '2025-06-10 16:00:00', 'View'
                        ],
                        ['Jane Smith', 'logout', 'auth', 'User logged out', '192.168.1.1',
                            '2025-06-10 15:30:00', 'View'
                        ],
                        ['System', 'create', 'users', 'User created', '********', '2025-06-10 14:00:00',
                            'View'
                        ]
                    ],
                    columns: [{
                            title: 'User'
                        },
                        {
                            title: 'Action'
                        },
                        {
                            title: 'Module'
                        },
                        {
                            title: 'Description'
                        },
                        {
                            title: 'IP Address'
                        },
                        {
                            title: 'Date & Time'
                        },
                        {
                            title: 'Actions'
                        }
                    ]
                });

                console.log('Static DataTable initialized:', staticTable);

                // If static works, try test AJAX first
                setTimeout(function() {
                    console.log('Destroying static table and trying test AJAX...');
                    staticTable.destroy();

                    var testTable = $('#activitiesTable').DataTable({
                        processing: true,
                        serverSide: true,
                        ajax: {
                            url: '{{ route('admin.user-activity.test-data') }}',
                            type: 'GET',
                            error: function(xhr, error, thrown) {
                                console.error('Test AJAX error:', error, thrown);
                                alert('Test AJAX Error: ' + error);
                            },
                            success: function(data) {
                                console.log('Test AJAX success:', data);
                            }
                        },
                        columns: [{
                                title: 'User'
                            },
                            {
                                title: 'Action'
                            },
                            {
                                title: 'Module'
                            },
                            {
                                title: 'Description'
                            },
                            {
                                title: 'IP Address'
                            },
                            {
                                title: 'Date & Time'
                            },
                            {
                                title: 'Actions'
                            }
                        ]
                    });

                    console.log('Test AJAX DataTable initialized:', testTable);

                    // If test AJAX works, try real data
                    setTimeout(function() {
                        console.log('Destroying test table and trying real data...');
                        testTable.destroy();

                        var realTable = $('#activitiesTable').DataTable({
                            processing: true,
                            serverSide: true,
                            ajax: {
                                url: '{{ route('admin.user-activity.data') }}',
                                type: 'GET',
                                error: function(xhr, error, thrown) {
                                    console.error('Real data AJAX error:', error,
                                        thrown);
                                    console.error('Response:', xhr.responseText);
                                    alert('Real data AJAX Error: ' + error);
                                },
                                success: function(data) {
                                    console.log('Real data AJAX success:', data);
                                }
                            },
                            columns: [{
                                    title: 'User'
                                },
                                {
                                    title: 'Action'
                                },
                                {
                                    title: 'Module'
                                },
                                {
                                    title: 'Description'
                                },
                                {
                                    title: 'IP Address'
                                },
                                {
                                    title: 'Date & Time'
                                },
                                {
                                    title: 'Actions'
                                }
                            ]
                        });

                        console.log('Real data DataTable initialized:', realTable);
                    }, 3000);
                }, 3000);

            } catch (error) {
                console.error('Error initializing DataTable:', error);
                alert('Error initializing DataTable: ' + error.message);
            }

            // Apply filters
            $('#applyFilters').click(function() {
                table.ajax.reload();
            });

            // Reset filters
            $('#resetFilters').click(function() {
                $('#filterForm')[0].reset();
                table.ajax.reload();
            });

            // Export functionality
            $('#exportBtn').click(function() {
                var params = new URLSearchParams();
                params.append('user_id', $('#user_id').val());
                params.append('action', $('#action').val());
                params.append('module', $('#module').val());
                params.append('date_from', $('#date_from').val());
                params.append('date_to', $('#date_to').val());

                window.location.href = '{{ route('admin.user-activity.export') }}?' + params.toString();
            });

            // Auto-reload table when filters change
            $('#user_id, #action, #module, #date_from, #date_to').change(function() {
                table.ajax.reload();
            });

            // Show success/error messages
            @if (session('success'))
                toastr.success('{{ session('success') }}');
            @endif

            @if (session('error'))
                toastr.error('{{ session('error') }}');
            @endif
        });
    </script>
@endsection
