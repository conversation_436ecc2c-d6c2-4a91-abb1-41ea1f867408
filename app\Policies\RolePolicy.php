<?php

namespace App\Policies;

use App\Models\Role;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class RolePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user)
    {
        return $user->hasPermission('view-roles');
    }

    public function view(User $user, Role $role)
    {
        return $user->hasPermission('view-roles');
    }

    public function create(User $user)
    {
        return $user->hasPermission('create-roles');
    }

    public function update(User $user, Role $role)
    {
        return $user->hasPermission('edit-roles');
    }

    public function delete(User $user, Role $role)
    {
        // Prevent deletion of super-admin role
        if ($role->slug === 'super-admin') {
            return false;
        }
        
        return $user->hasPermission('delete-roles');
    }
}