@extends('layouts.master')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">Create New Permission</h4>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('permission.store') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="name" class="form-label">Permission Name</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                    id="name" name="name" value="{{ old('name') }}" required>
                                <small class="form-text text-muted">The name will be converted to a slug
                                    automatically.</small>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description"
                                    rows="3">{{ old('description') }}</textarea>
                                <small class="form-text text-muted">Provide a clear description of what this permission
                                    allows.</small>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ route('permission.index') }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Create Permission</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Function to convert string to slug
            function stringToSlug(str) {
                str = str.toLowerCase().trim();
                str = str.replace(/[^a-z0-9\s-]/g, '');
                str = str.replace(/[\s-]+/g, '-');
                return str;
            }

            // Update slug when name changes
            $('#name').on('input', function() {
                const nameValue = $(this).val();
                const slugValue = stringToSlug(nameValue);
                $('#slug').val(slugValue);
            });

            // Initialize slug on page load if name has a value
            if ($('#name').val()) {
                $('#name').trigger('input');
            }
        });
    </script>
@endsection
