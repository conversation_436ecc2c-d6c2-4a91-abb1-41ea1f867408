<?php

return [
    /*
    |--------------------------------------------------------------------------
    | TCP Server Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for the TCP server that communicates
    | with devices.
    |
    */

    // Server IP address (0.0.0.0 to listen on all interfaces)
    'server_ip' => env('TCP_SERVER_IP', '0.0.0.0'),

    // Server port
    'server_port' => env('TCP_SERVER_PORT', 8089),

    // Command processing settings
    'command_processing' => [
        // Minimum delay between commands to the same device (seconds)
        'min_delay_between_commands' => env('TCP_MIN_DELAY_BETWEEN_COMMANDS', 2.0),

        // Maximum commands to process per cycle
        'max_commands_per_cycle' => env('TCP_MAX_COMMANDS_PER_CYCLE', 5),

        // Command processing interval (seconds)
        'processing_interval' => env('TCP_COMMAND_PROCESSING_INTERVAL', 5),

        // Command expiration time (seconds)
        'expiration_time' => env('TCP_COMMAND_EXPIRATION_TIME', 45),
    ],

    // Connection settings
    'connection' => [
        // Inactive connection timeout (seconds)
        'inactive_timeout' => env('TCP_INACTIVE_TIMEOUT', 120),

        // Heartbeat interval (seconds)
        'heartbeat_interval' => env('TCP_HEARTBEAT_INTERVAL', 30),
    ],
];
