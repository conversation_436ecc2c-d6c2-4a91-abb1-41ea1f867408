@extends('layouts.customer')

@section('content')
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Powerbank Rental</h5>
                            <a href="{{ route('customer.logout') }}" class="btn btn-sm btn-light">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <h4 class="mb-3">Welcome, {{ $customer->name }}!</h4>

                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if (isset($activeRental))
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> You have an active rental</h5>
                                <p>You already have a powerbank rented. Please return it before renting another one.</p>
                                <p><strong>Rented at:</strong> {{ $activeRental->rented_at->format('M d, Y h:i A') }}</p>
                                <p><strong>Current charge:</strong>
                                    {{ $activeRental->powerbank->current_charge ?? 'Unknown' }}%</p>
                            </div>

                            <div class="text-center mt-4">
                                <p class="lead">To rent another powerbank, please return your current one first.</p>
                                <p>You can return your powerbank to any available device.</p>
                            </div>
                        @else
                            @if (isset($selectedDevice))
                                <div class="alert alert-info mb-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-info-circle me-3 fa-2x"></i>
                                        <div>
                                            <h5 class="mb-1">Selected Device</h5>
                                            <p class="mb-0"><strong>Device ID:</strong> {{ $selectedDevice->sn }}</p>
                                            @if ($selectedDevice->location)
                                                <p class="mb-0"><strong>Location:</strong> {{ $selectedDevice->location }}
                                                </p>
                                            @endif
                                            <p class="mb-0"><strong>Available Powerbanks:</strong>
                                                {{ $selectedDevice->powerbanks->where('status', 'available')->count() }}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mb-4">
                                    <a href="{{ route('customer.lease', ['t' => $selectedDevice->sn]) }}"
                                        class="btn btn-primary btn-lg">
                                        <i class="fas fa-bolt"></i> Rent Powerbank from This Device
                                    </a>
                                </div>

                                <div class="text-center mb-4">
                                    <p>Or choose another device from the list below</p>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <div class="mb-4">
                                        <i class="fas fa-qrcode text-primary" style="font-size: 5rem;"></i>
                                    </div>
                                    <h3 class="mb-3">Scan a Device QR Code</h3>
                                    <p class="lead">Use the QR code scanner on your device to scan the QR code of the
                                        powerbank you want to rent.</p>
                                    <p>Or choose a device from the list below</p>
                                </div>
                            @endif

                            <!-- Tabs for Map and Table views -->
                            <ul class="nav nav-tabs mb-3" id="deviceViewTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="map-tab" data-bs-toggle="tab"
                                        data-bs-target="#map-view" type="button" role="tab" aria-controls="map-view"
                                        aria-selected="true">
                                        <i class="fas fa-map-marker-alt"></i> Map View
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="table-tab" data-bs-toggle="tab"
                                        data-bs-target="#table-view" type="button" role="tab"
                                        aria-controls="table-view" aria-selected="false">
                                        <i class="fas fa-table"></i> Table View
                                    </button>
                                </li>
                            </ul>

                            <div class="tab-content" id="deviceViewTabContent">
                                <!-- Map View -->
                                <div class="tab-pane fade show active" id="map-view" role="tabpanel"
                                    aria-labelledby="map-tab">
                                    <div id="map" style="height: 400px; width: 100%; border-radius: 8px;"></div>
                                    <div class="text-center mt-2">
                                        <small class="text-muted">Click on a marker to see device details</small>
                                    </div>
                                </div>

                                <!-- Table View -->
                                <div class="tab-pane fade" id="table-view" role="tabpanel" aria-labelledby="table-tab">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Device ID</th>
                                                    <th>Location</th>
                                                    <th>Available Powerbanks</th>
                                                    {{-- <th>Action</th> --}}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($devices as $device)
                                                    <tr>
                                                        <td>{{ $device->sn }}</td>
                                                        <td>{{ $device->location ?? 'Not specified' }}</td>
                                                        <td>{{ $device->powerbanks->where('status', 'available')->count() }}
                                                        </td>
                                                        {{-- <td>
                                                            <a href="{{ route('customer.lease', ['t' => $device->sn]) }}"
                                                                class="btn btn-primary btn-sm">
                                                                <i class="fas fa-bolt"></i> Rent
                                                            </a>
                                                        </td> --}}
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="mt-4">
                            <h5>How to Rent a Powerbank:</h5>
                            <ol class="list-group list-group-numbered mt-2">
                                <li class="list-group-item">Find a device near you</li>
                                <li class="list-group-item">Scan the QR code or select a device from the list above</li>
                                <li class="list-group-item">Choose an available powerbank</li>
                                <li class="list-group-item">Take your powerbank when it's ejected</li>
                                <li class="list-group-item">Return to any device when you're done</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .card-header.bg-primary {
            background-color: #4e73df !important;
        }

        .border-primary {
            border-color: #4e73df !important;
        }

        .progress-bar {
            transition: width 1s;
        }

        #map {
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .device-info-window {
            padding: 5px;
            max-width: 250px;
        }

        .device-info-window h5 {
            margin-bottom: 5px;
            color: #4e73df;
        }

        .device-info-window p {
            margin-bottom: 5px;
        }

        .device-info-window .btn {
            margin-top: 5px;
        }
    </style>
@endpush

@push('scripts')
    <!-- Google Maps JavaScript API -->
    <script src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY', '') }}&callback=initMap" async
        defer></script>

    <script>
        // Map initialization function
        function initMap() {
            // Default center (can be set to user's location or a default location)
            const defaultCenter = {
                lat: -34.397,
                lng: 150.644
            };

            // Create map
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 12,
                center: defaultCenter,
                mapTypeControl: false,
                streetViewControl: false,
                fullscreenControl: true,
                zoomControl: true,
            });

            // Try to get user's current location
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const userLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };

                        // Center map on user's location
                        map.setCenter(userLocation);

                        // Add marker for user's location
                        new google.maps.Marker({
                            position: userLocation,
                            map: map,
                            icon: {
                                path: google.maps.SymbolPath.CIRCLE,
                                scale: 10,
                                fillColor: "#4285F4",
                                fillOpacity: 1,
                                strokeColor: "#FFFFFF",
                                strokeWeight: 2,
                            },
                            title: "Your Location"
                        });
                    },
                    () => {
                        // Handle location error
                        console.log("Error: The Geolocation service failed.");
                    }
                );
            }

            // Add markers for devices
            const devices = @json($devices);
            const bounds = new google.maps.LatLngBounds();
            const infoWindow = new google.maps.InfoWindow();

            devices.forEach(device => {
                // Skip devices without location coordinates
                if (!device.latitude || !device.longitude) return;

                const position = {
                    lat: parseFloat(device.latitude),
                    lng: parseFloat(device.longitude)
                };

                // Add marker
                const marker = new google.maps.Marker({
                    position: position,
                    map: map,
                    title: `Device: ${device.sn}`,
                    icon: {
                        url: "https://maps.google.com/mapfiles/ms/icons/red-dot.png"
                    }
                });

                // Extend bounds
                bounds.extend(position);

                // Count available powerbanks
                const availablePowerbanks = device.powerbanks.filter(pb => pb.status === 'available').length;

                // Create info window content
                const content = `
                    <div class="device-info-window">
                        <h5>Device: ${device.sn}</h5>
                        <p><strong>Location:</strong> ${device.location || 'Not specified'}</p>
                        <p><strong>Available Powerbanks:</strong> ${availablePowerbanks}</p>
                        <a href="{{ url('customer/lease') }}?t=${device.sn}" class="btn btn-primary btn-sm">
                            <i class="fas fa-bolt"></i> Rent Powerbank
                        </a>
                    </div>
                `;

                // Add click listener to marker
                marker.addListener('click', () => {
                    infoWindow.setContent(content);
                    infoWindow.open(map, marker);
                });
            });

            // Fit map to bounds if we have device markers
            if (!bounds.isEmpty()) {
                map.fitBounds(bounds);

                // Don't zoom in too far
                const listener = google.maps.event.addListener(map, 'idle', () => {
                    if (map.getZoom() > 15) {
                        map.setZoom(15);
                    }
                    google.maps.event.removeListener(listener);
                });
            }
        }
    </script>
@endpush
