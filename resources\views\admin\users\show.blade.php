@extends('layouts.master')

@section('title', 'User Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">User Details</h4>
                    <div class="card-tools">
                        <a href="{{ route('user.index') }}" class="btn btn-secondary">
                            <i class="fe-arrow-left mr-1"></i> Back to Users
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3">User Information</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 150px;">Name</th>
                                    <td>{{ $user->name }}</td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>{{ $user->email }}</td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ $user->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At</th>
                                    <td>{{ $user->updated_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>2FA Enabled</th>
                                    <td>
                                        @if($user->two_factor_enabled)
                                            <span class="badge badge-success">Enabled</span>
                                        @else
                                            <span class="badge badge-secondary">Disabled</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-3">Roles</h5>
                            @if($user->roles->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Role</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->roles as $role)
                                                <tr>
                                                    <td>{{ $role->name }}</td>
                                                    <td>{{ $role->description }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    This user has no assigned roles.
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="text-right">
                                @can('edit-users')
                                <a href="{{ route('user.edit', $user->id) }}" class="btn btn-primary">
                                    <i class="fe-edit mr-1"></i> Edit User
                                </a>
                                @endcan
                                
                                @can('assign-roles')
                                <a href="{{ route('users.roles.edit', $user) }}" class="btn btn-warning">
                                    <i class="fe-users mr-1"></i> Manage Roles
                                </a>
                                @endcan
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection