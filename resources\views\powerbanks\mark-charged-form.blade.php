<div class="card">
    <div class="card-header">
        <h5 class="card-title">Mark as Charged</h5>
    </div>
    <div class="card-body">
        <form action="{{ route('powerbanks.mark-charged', $powerbank->id) }}" method="POST">
            @csrf
            <div class="form-group">
                <label for="current_charge">Current Charge (%)</label>
                <input type="number" class="form-control @error('current_charge') is-invalid @enderror" 
                    id="current_charge" name="current_charge" value="100" required>
                @error('current_charge')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <button type="submit" class="btn btn-primary">Mark as Charged</button>
        </form>
    </div>
</div>