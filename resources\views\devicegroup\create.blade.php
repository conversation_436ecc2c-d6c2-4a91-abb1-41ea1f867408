@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Create Device Group</h4>
                    <p class="sub-header">Create a new group to organize devices.</p>

                    <form method="POST" action="{{ route('devicegroups.store') }}">
                        @csrf

                        <div class="form-group">
                            <label for="name">Group Name<span class="text-danger">*</span></label>
                            <input type="text" name="name" class="form-control" id="name"
                                value="{{ old('name') }}" required>
                            @error('name')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea name="description" class="form-control" id="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active"
                                    value="1" checked>
                                <label class="custom-control-label" for="is_active">Active</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Select Devices</label>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th width="5%">Select</th>
                                            <th>Serial Number</th>
                                            <th>Model</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($devices as $device)
                                            <tr>
                                                <td class="text-center">
                                                    <input type="checkbox" name="devices[]" value="{{ $device->id }}"
                                                        {{ in_array($device->id, old('devices', [])) ? 'checked' : '' }}>
                                                </td>
                                                <td>{{ $device->sn }}</td>
                                                <td>{{ $device->model }}</td>
                                                <td>{{ $device->status }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="form-group text-right">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-1"></i> Create Group
                            </button>
                            <a href="{{ route('devicegroups.index') }}" class="btn btn-light ml-1">
                                <i class="fas fa-times mr-1"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
