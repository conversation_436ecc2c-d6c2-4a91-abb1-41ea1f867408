<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Tests\TestCase;

class AdvancedUserManagementTest extends TestCase
{
    /** @test */
    public function permissions_are_created_correctly()
    {
        // Check that all major permission categories exist
        $this->assertTrue(Permission::where('slug', 'view-dashboard')->exists());
        $this->assertTrue(Permission::where('slug', 'view-users')->exists());
        $this->assertTrue(Permission::where('slug', 'manage-user-status')->exists());
        $this->assertTrue(Permission::where('slug', 'view-devices')->exists());
        $this->assertTrue(Permission::where('slug', 'view-powerbanks')->exists());
        $this->assertTrue(Permission::where('slug', 'view-customers')->exists());
        $this->assertTrue(Permission::where('slug', 'manage-backups')->exists());
        
        // Check total count
        $this->assertGreaterThanOrEqual(70, Permission::count());
    }

    /** @test */
    public function roles_are_created_with_correct_hierarchy()
    {
        $superAdmin = Role::where('slug', 'super-administrator')->first();
        $admin = Role::where('slug', 'administrator')->first();
        $manager = Role::where('slug', 'manager')->first();
        $operator = Role::where('slug', 'operator')->first();
        $viewer = Role::where('slug', 'viewer')->first();

        $this->assertNotNull($superAdmin);
        $this->assertNotNull($admin);
        $this->assertNotNull($manager);
        $this->assertNotNull($operator);
        $this->assertNotNull($viewer);

        // Super admin should have all permissions
        $this->assertEquals(Permission::count(), $superAdmin->permissions()->count());
        
        // Admin should have most permissions but not super admin only ones
        $this->assertLessThan(Permission::count(), $admin->permissions()->count());
        $this->assertGreaterThan(50, $admin->permissions()->count());
    }

    /** @test */
    public function super_admin_user_is_created()
    {
        $superAdmin = User::where('email', '<EMAIL>')->first();
        
        $this->assertNotNull($superAdmin);
        $this->assertEquals('Super Administrator', $superAdmin->name);
        $this->assertEquals('active', $superAdmin->status);
        $this->assertTrue($superAdmin->hasRole('super-administrator'));
    }

    /** @test */
    public function user_permission_checking_works()
    {
        $superAdmin = User::where('email', '<EMAIL>')->first();
        
        // Super admin should have all permissions
        $this->assertTrue($superAdmin->hasPermission('view-dashboard'));
        $this->assertTrue($superAdmin->hasPermission('manage-user-status'));
        $this->assertTrue($superAdmin->hasPermission('delete-users'));
        $this->assertTrue($superAdmin->hasPermission('manage-system-settings'));
        
        // Test with array of permissions
        $this->assertTrue($superAdmin->hasPermission(['view-users', 'edit-users']));
    }

    /** @test */
    public function user_model_has_new_fields()
    {
        $user = new User();
        
        $fillable = $user->getFillable();
        $this->assertContains('status', $fillable);
        $this->assertContains('last_login_at', $fillable);
        $this->assertContains('password_changed_at', $fillable);
        
        $casts = $user->getCasts();
        $this->assertArrayHasKey('last_login_at', $casts);
        $this->assertArrayHasKey('password_changed_at', $casts);
    }

    /** @test */
    public function permission_middleware_is_registered()
    {
        $kernel = app(\Illuminate\Contracts\Http\Kernel::class);
        
        // Use reflection to access protected property
        $reflection = new \ReflectionClass($kernel);
        $property = $reflection->getProperty('middlewareAliases');
        $property->setAccessible(true);
        $middlewareAliases = $property->getValue($kernel);
        
        $this->assertArrayHasKey('permission', $middlewareAliases);
        $this->assertEquals(\App\Http\Middleware\CheckPermission::class, $middlewareAliases['permission']);
    }

    /** @test */
    public function comprehensive_permissions_cover_all_menu_options()
    {
        // Dashboard
        $this->assertTrue(Permission::where('slug', 'view-dashboard')->exists());
        
        // User Management
        $this->assertTrue(Permission::where('slug', 'view-users')->exists());
        $this->assertTrue(Permission::where('slug', 'create-users')->exists());
        $this->assertTrue(Permission::where('slug', 'edit-users')->exists());
        $this->assertTrue(Permission::where('slug', 'delete-users')->exists());
        
        // Device Management
        $this->assertTrue(Permission::where('slug', 'view-devices')->exists());
        $this->assertTrue(Permission::where('slug', 'control-devices')->exists());
        $this->assertTrue(Permission::where('slug', 'view-device-data')->exists());
        
        // Powerbank Management
        $this->assertTrue(Permission::where('slug', 'view-powerbanks')->exists());
        $this->assertTrue(Permission::where('slug', 'rent-powerbanks')->exists());
        $this->assertTrue(Permission::where('slug', 'return-powerbanks')->exists());
        
        // Customer Management
        $this->assertTrue(Permission::where('slug', 'view-customers')->exists());
        $this->assertTrue(Permission::where('slug', 'view-customer-reports')->exists());
        
        // System Administration
        $this->assertTrue(Permission::where('slug', 'view-user-activity')->exists());
        $this->assertTrue(Permission::where('slug', 'view-audit-trail')->exists());
        $this->assertTrue(Permission::where('slug', 'manage-backups')->exists());
        
        // Ad Management
        $this->assertTrue(Permission::where('slug', 'view-ad-materials')->exists());
        $this->assertTrue(Permission::where('slug', 'view-ad-plans')->exists());
    }
}
