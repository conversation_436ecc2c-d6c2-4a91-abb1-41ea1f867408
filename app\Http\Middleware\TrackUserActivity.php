<?php

namespace App\Http\Middleware;

use App\Services\ActivityLogService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TrackUserActivity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Process the request
        $response = $next($request);

        // Track activity for specific routes
        if ($this->shouldTrackRoute($request)) {
            $this->logActivity($request);
        }

        return $response;
    }

    /**
     * Determine if the route should be tracked
     */
    protected function shouldTrackRoute(Request $request): bool
    {
        // Define routes to track
        $trackableRoutes = [
            'admin.users.*',
            'admin.roles.*',
            'admin.permissions.*',
            // Add other important routes to track
        ];

        foreach ($trackableRoutes as $route) {
            if ($request->routeIs($route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Log the activity based on the request
     */
    protected function logActivity(Request $request): void
    {
        $routeName = $request->route()->getName();
        $action = $this->determineAction($request);
        $module = $this->determineModule($routeName);
        $description = $this->generateDescription($action, $module, $request);

        ActivityLogService::log($action, $module, $description);
    }

    /**
     * Determine the action based on the request method and route
     */
    protected function determineAction(Request $request): string
    {
        $method = $request->method();
        $routeName = $request->route()->getName();

        if ($method === 'GET' && str_ends_with($routeName, '.index')) {
            return 'view-list';
        }

        if ($method === 'GET' && str_ends_with($routeName, '.show')) {
            return 'view';
        }

        if ($method === 'GET' && (str_ends_with($routeName, '.create') || str_ends_with($routeName, '.edit'))) {
            return 'form-access';
        }

        if ($method === 'POST' && str_ends_with($routeName, '.store')) {
            return 'create';
        }

        if (($method === 'PUT' || $method === 'PATCH') && str_ends_with($routeName, '.update')) {
            return 'update';
        }

        if ($method === 'DELETE' || str_ends_with($routeName, '.destroy')) {
            return 'delete';
        }

        return 'access';
    }

    /**
     * Determine the module from the route name
     */
    protected function determineModule(string $routeName): string
    {
        $parts = explode('.', $routeName);
        return count($parts) >= 2 ? $parts[1] : 'unknown';
    }

    /**
     * Generate a description for the activity
     */
    protected function generateDescription(string $action, string $module, Request $request): string
    {
        $singularModule = str_singular($module);
        $id = $request->route($singularModule) ?? $request->route('id');

        switch ($action) {
            case 'view-list':
                return "Viewed {$module} list";
            case 'view':
                return "Viewed {$singularModule} #{$id}";
            case 'form-access':
                return str_ends_with($request->route()->getName(), '.create') 
                    ? "Accessed {$singularModule} creation form" 
                    : "Accessed {$singularModule} #{$id} edit form";
            case 'create':
                return "Created a new {$singularModule}";
            case 'update':
                return "Updated {$singularModule} #{$id}";
            case 'delete':
                return "Deleted {$singularModule} #{$id}";
            default:
                return "Accessed {$module}";
        }
    }
}