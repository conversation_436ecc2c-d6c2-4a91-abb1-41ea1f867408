<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('powerbank_rentals', function (Blueprint $table) {
            if (!Schema::hasColumn('powerbank_rentals', 'return_device_id')) {
                $table->unsignedBigInteger('return_device_id')->nullable()->after('device_id');
            }
            if (!Schema::hasColumn('powerbank_rentals', 'return_slot_number')) {
                $table->integer('return_slot_number')->nullable()->after('slot_number');
            }
            if (!Schema::hasColumn('powerbank_rentals', 'return_aims')) {
                $table->integer('return_aims')->nullable()->after('aims');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('powerbank_rentals', function (Blueprint $table) {
            if (Schema::hasColumn('powerbank_rentals', 'return_device_id')) {
                $table->dropColumn('return_device_id');
            }
            if (Schema::hasColumn('powerbank_rentals', 'return_slot_number')) {
                $table->dropColumn('return_slot_number');
            }
            if (Schema::hasColumn('powerbank_rentals', 'return_aims')) {
                $table->dropColumn('return_aims');
            }
        });
    }
};
