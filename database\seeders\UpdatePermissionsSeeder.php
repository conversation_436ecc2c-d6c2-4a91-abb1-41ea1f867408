<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class UpdatePermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define new permissions
        $newPermissions = [
            // Device management
            ['name' => 'View Devices', 'slug' => 'view-devices', 'description' => 'Can view device list and details'],
            ['name' => 'Create Devices', 'slug' => 'create-devices', 'description' => 'Can create new devices'],
            ['name' => 'Edit Devices', 'slug' => 'edit-devices', 'description' => 'Can edit existing devices'],
            ['name' => 'Delete Devices', 'slug' => 'delete-devices', 'description' => 'Can delete devices'],
            ['name' => 'Control Devices', 'slug' => 'control-devices', 'description' => 'Can control devices (reboot, eject)'],

            // Device Group management
            ['name' => 'View Device Groups', 'slug' => 'view-device-groups', 'description' => 'Can view device group list and details'],
            ['name' => 'Create Device Groups', 'slug' => 'create-device-groups', 'description' => 'Can create new device groups'],
            ['name' => 'Edit Device Groups', 'slug' => 'edit-device-groups', 'description' => 'Can edit existing device groups'],
            ['name' => 'Delete Device Groups', 'slug' => 'delete-device-groups', 'description' => 'Can delete device groups'],
            ['name' => 'Assign Devices to Groups', 'slug' => 'assign-devices-to-groups', 'description' => 'Can assign devices to groups'],
            ['name' => 'Assign Plans to Groups', 'slug' => 'assign-plans-to-groups', 'description' => 'Can assign ad plans to device groups'],

            // Customer management
            ['name' => 'View Customers', 'slug' => 'view-customers', 'description' => 'Can view customer list and details'],
            ['name' => 'Create Customers', 'slug' => 'create-customers', 'description' => 'Can create new customers'],
            ['name' => 'Edit Customers', 'slug' => 'edit-customers', 'description' => 'Can edit existing customers'],
            ['name' => 'Delete Customers', 'slug' => 'delete-customers', 'description' => 'Can delete customers'],

            // Ad Material management
            ['name' => 'View Ad Materials', 'slug' => 'view-ad-materials', 'description' => 'Can view ad material list and details'],
            ['name' => 'Create Ad Materials', 'slug' => 'create-ad-materials', 'description' => 'Can create new ad materials'],
            ['name' => 'Edit Ad Materials', 'slug' => 'edit-ad-materials', 'description' => 'Can edit existing ad materials'],
            ['name' => 'Delete Ad Materials', 'slug' => 'delete-ad-materials', 'description' => 'Can delete ad materials'],

            // Ad Schedule management
            ['name' => 'View Ad Plans', 'slug' => 'view-ad-plans', 'description' => 'Can view ad plan list and details'],
            ['name' => 'Create Ad Plans', 'slug' => 'create-ad-plans', 'description' => 'Can create new ad plans'],
            ['name' => 'Edit Ad Plans', 'slug' => 'edit-ad-plans', 'description' => 'Can edit existing ad plans'],
            ['name' => 'Delete Ad Plans', 'slug' => 'delete-ad-plans', 'description' => 'Can delete ad plans'],
        ];

        // Create permissions if they don't exist
        foreach ($newPermissions as $permissionData) {
            Permission::firstOrCreate(
                ['slug' => $permissionData['slug']],
                $permissionData
            );
        }

        // Get the administrator role
        $adminRole = Role::where('slug', 'administrator')->first();

        // If admin role exists, assign all permissions to it
        if ($adminRole) {
            $adminRole->permissions()->sync(Permission::all()->pluck('id')->toArray());
        }
    }
}