<?php

namespace App\Http\Controllers;

use App\Models\Device;
use App\Models\DeviceGroup;
use App\Models\AdPlan;
use Illuminate\Http\Request;

class DeviceGroupController extends Controller
{
    /**
     * Display a listing of the device groups.
     */
    public function index()
    {
        $this->authorize('view-device-groups');

        $deviceGroups = DeviceGroup::withCount('devices')->get();
        return view('devicegroup.index', compact('deviceGroups'));
    }

    /**
     * Show the form for creating a new device group.
     */
    public function create()
    {
        $this->authorize('create-device-groups');

        $devices = Device::all();
        return view('devicegroup.create', compact('devices'));
    }

    /**
     * Store a newly created device group in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create-device-groups');

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'devices' => 'array',
            'devices.*' => 'exists:devices,id',
        ]);

        $group = DeviceGroup::create([
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => $request->is_active ?? true,
        ]);

        if ($request->has('devices')) {
            $group->devices()->attach($request->devices);
        }

        return redirect()->route('devicegroups.show', $group->id)
            ->with('success', 'Device group created successfully.');
    }

    /**
     * Display the specified device group.
     */
    public function show(DeviceGroup $devicegroup)
    {
        $this->authorize('view-device-groups');

        $devicegroup->load('devices', 'adPlans');
        return view('devicegroup.show', compact('devicegroup'));
    }

    /**
     * Show the form for editing the specified device group.
     */
    public function edit(DeviceGroup $devicegroup)
    {
        $this->authorize('edit-device-groups');

        $devices = Device::all();
        $selectedDevices = $devicegroup->devices->pluck('id')->toArray();
        return view('devicegroup.edit', compact('devicegroup', 'devices', 'selectedDevices'));
    }

    /**
     * Update the specified device group in storage.
     */
    public function update(Request $request, DeviceGroup $devicegroup)
    {
        $this->authorize('edit-device-groups');

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $devicegroup->update([
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => $request->is_active ?? $devicegroup->is_active,
        ]);

        return redirect()->route('devicegroups.show', $devicegroup->id)
            ->with('success', 'Device group updated successfully.');
    }

    /**
     * Remove the specified device group from storage.
     */
    public function destroy(DeviceGroup $devicegroup)
    {
        $this->authorize('delete-device-groups');

        // Detach all relationships before deleting
        $devicegroup->devices()->detach();
        $devicegroup->adPlans()->detach();

        $devicegroup->delete();

        return redirect()->route('devicegroups.index')
            ->with('success', 'Device group deleted successfully.');
    }

    /**
     * Show form to assign devices to a device group.
     */
    public function showAssignDevices(DeviceGroup $devicegroup)
    {
        $this->authorize('assign-devices-to-groups');

        $devices = Device::all();
        $assignedDevices = $devicegroup->devices->pluck('id')->toArray();

        return view('devicegroup.assign_devices', compact('devicegroup', 'devices', 'assignedDevices'));
    }

    /**
     * Assign devices to a device group.
     */
    public function assignDevices(Request $request, DeviceGroup $devicegroup)
    {
        $this->authorize('assign-devices-to-groups');

        $request->validate([
            'devices' => 'array',
            'devices.*' => 'exists:devices,id',
        ]);

        // Sync the devices with the device group
        $devicegroup->devices()->sync($request->devices ?? []);

        return redirect()->route('devicegroups.show', $devicegroup->id)
            ->with('success', 'Devices assigned successfully.');
    }

    /**
     * Show form to assign ad plans to a device group.
     */
    public function showAssignPlans(DeviceGroup $devicegroup)
    {
        $this->authorize('assign-plans-to-groups');

        $adPlans = AdPlan::all();
        $assignedPlans = $devicegroup->adPlans->pluck('id')->toArray();

        return view('devicegroup.assign_plans', compact('devicegroup', 'adPlans', 'assignedPlans'));
    }

    /**
     * Assign ad plans to a device group.
     */
    public function assignPlans(Request $request, DeviceGroup $devicegroup)
    {
        $this->authorize('assign-plans-to-groups');

        $request->validate([
            'plans' => 'array',
            'plans.*' => 'exists:ad_plans,id',
        ]);

        // Sync the plans with the device group
        $devicegroup->adPlans()->sync($request->plans ?? []);

        return redirect()->route('devicegroups.show', $devicegroup->id)
            ->with('success', 'Ad plans assigned successfully.');
    }
}
