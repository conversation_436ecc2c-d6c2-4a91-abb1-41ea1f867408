<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('powerbanks', function (Blueprint $table) {
            $table->integer('aims')->default(0)->after('slot_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('powerbanks', function (Blueprint $table) {
            $table->dropColumn('aims');
        });
    }
};
