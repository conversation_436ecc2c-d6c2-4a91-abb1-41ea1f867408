<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\User;
use App\Models\UserActivity;
use App\Models\AuditTrail;
use App\Services\ActivityLogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('view-users');

        $query = User::with(['roles', 'auditTrails' => function ($q) {
            $q->latest()->limit(3);
        }]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Role filter
        if ($request->filled('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('slug', $request->role);
            });
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('status', 'active');
            } elseif ($request->status === 'inactive') {
                $query->where('status', 'inactive');
            } elseif ($request->status === '2fa_enabled') {
                $query->where('two_factor_enabled', true);
            }
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $users = $query->paginate(15)->appends($request->query());
        $roles = Role::all();

        // Statistics
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('status', 'active')->count(),
            'users_with_2fa' => User::where('two_factor_enabled', true)->count(),
            'recent_logins' => User::where('last_login_at', '>=', Carbon::now()->subDays(7))->count(),
        ];

        ActivityLogService::log('view', 'users', 'Viewed user list with filters: ' . json_encode($request->only(['search', 'role', 'status'])));

        return view('admin.users.index', compact('users', 'roles', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create-users');

        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create-users');

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'roles' => ['nullable', 'array'],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        if ($request->has('roles')) {
            $user->roles()->sync($request->roles);
        }

        return redirect()->route('user.index')
            ->with('success', 'User created successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $this->authorize('view-users');

        $user = User::with('roles')->findOrFail($id);
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $this->authorize('edit-users');

        $user = User::findOrFail($id);
        $roles = Role::all();
        $userRoles = $user->roles->pluck('id')->toArray();

        return view('admin.users.edit', compact('user', 'roles', 'userRoles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $this->authorize('edit-users');

        $user = User::findOrFail($id);

        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $id],
            'roles' => ['nullable', 'array'],
        ];

        // Only validate password if it's provided
        if ($request->filled('password')) {
            $rules['password'] = ['confirmed', Rules\Password::defaults()];
        }

        $request->validate($rules);

        $user->name = $request->name;
        $user->email = $request->email;

        if ($request->filled('password')) {
            $user->password = Hash::make($request->password);
        }

        $user->save();

        if ($request->has('roles')) {
            $user->roles()->sync($request->roles);
        } else {
            $user->roles()->detach();
        }

        return redirect()->route('user.index')
            ->with('success', 'User updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->authorize('delete-users');

        $user = User::findOrFail($id);

        // Prevent deleting yourself
        if ($user->id === auth()->id()) {
            return redirect()->route('user.index')
                ->with('error', 'You cannot delete your own account');
        }

        $user->roles()->detach();
        $user->delete();

        return redirect()->route('user.index')
            ->with('success', 'User deleted successfully');
    }

    /**
     * Show the form for editing user roles.
     */
    public function editRoles(User $user)
    {
        $this->authorize('assign-roles');

        $roles = Role::all();
        $userRoles = $user->roles->pluck('id')->toArray();

        return view('admin.users.roles', compact('user', 'roles', 'userRoles'));
    }

    /**
     * Update the user's roles.
     */
    public function updateRoles(Request $request, User $user)
    {
        $this->authorize('assign-roles');

        $request->validate([
            'roles' => 'array',
        ]);

        $user->roles()->sync($request->roles ?? []);

        ActivityLogService::log('update', 'users', "Updated roles for user: {$user->name}");

        return redirect()->route('user.index')
            ->with('success', 'User roles updated successfully');
    }

    /**
     * Toggle user status (activate/deactivate)
     */
    public function toggleStatus(User $user)
    {
        $this->authorize('manage-user-status');

        $newStatus = $user->status === 'active' ? 'inactive' : 'active';
        $user->update(['status' => $newStatus]);

        ActivityLogService::log('update', 'users', "Changed user status to {$newStatus} for: {$user->name}");

        return redirect()->back()
            ->with('success', "User {$newStatus} successfully");
    }

    /**
     * Reset user password
     */
    public function resetPassword(User $user)
    {
        $this->authorize('reset-user-password');

        $newPassword = Str::random(12);
        $user->update([
            'password' => Hash::make($newPassword),
            'password_changed_at' => now(),
        ]);

        // In a real application, you would send this via email
        // Mail::to($user->email)->send(new PasswordResetMail($newPassword));

        ActivityLogService::log('update', 'users', "Reset password for user: {$user->name}");

        return redirect()->back()
            ->with('success', 'Password reset successfully')
            ->with('new_password', $newPassword);
    }

    /**
     * View user activity
     */
    public function activity(User $user)
    {
        $this->authorize('view-user-activity');

        $activities = UserActivity::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $auditTrails = AuditTrail::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        ActivityLogService::log('view', 'users', "Viewed activity for user: {$user->name}");

        return view('admin.users.activity', compact('user', 'activities', 'auditTrails'));
    }

    /**
     * Bulk actions for users
     */
    public function bulkAction(Request $request)
    {
        $this->authorize('edit-users');

        $request->validate([
            'action' => 'required|in:activate,deactivate,delete,assign_role',
            'users' => 'required|array',
            'role_id' => 'required_if:action,assign_role|exists:roles,id',
        ]);

        $users = User::whereIn('id', $request->users)->get();
        $count = 0;

        foreach ($users as $user) {
            // Prevent actions on own account for safety
            if ($user->id === Auth::id() && in_array($request->action, ['deactivate', 'delete'])) {
                continue;
            }

            switch ($request->action) {
                case 'activate':
                    if (Auth::user()->hasPermission('manage-user-status')) {
                        $user->update(['status' => 'active']);
                        $count++;
                    }
                    break;
                case 'deactivate':
                    if (Auth::user()->hasPermission('manage-user-status')) {
                        $user->update(['status' => 'inactive']);
                        $count++;
                    }
                    break;
                case 'delete':
                    if (Auth::user()->hasPermission('delete-users')) {
                        $user->roles()->detach();
                        $user->delete();
                        $count++;
                    }
                    break;
                case 'assign_role':
                    if (Auth::user()->hasPermission('assign-roles')) {
                        $user->roles()->sync([$request->role_id]);
                        $count++;
                    }
                    break;
            }
        }

        ActivityLogService::log('bulk_action', 'users', "Performed bulk action '{$request->action}' on {$count} users");

        return redirect()->back()
            ->with('success', "Bulk action completed on {$count} users");
    }

    /**
     * Export users data
     */
    public function export(Request $request)
    {
        $this->authorize('export-data');

        $query = User::with('roles');

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('slug', $request->role);
            });
        }

        $users = $query->get();

        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($users) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'Name', 'Email', 'Status', 'Roles', '2FA Enabled', 'Created At', 'Last Login']);

            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->status ?? 'active',
                    $user->roles->pluck('name')->implode(', '),
                    $user->two_factor_enabled ? 'Yes' : 'No',
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : 'Never',
                ]);
            }

            fclose($file);
        };

        ActivityLogService::log('export', 'users', 'Exported users data');

        return response()->stream($callback, 200, $headers);
    }
}
