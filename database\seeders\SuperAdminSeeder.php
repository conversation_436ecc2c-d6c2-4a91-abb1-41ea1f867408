<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create super admin user
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>', // Change to your email
            'password' => Hash::make('password'), // Change to a secure password
        ]);

        // Get the administrator role
        $adminRole = Role::where('slug', 'administrator')->first();

        // Assign admin role to super admin user
        if ($adminRole) {
            $superAdmin->roles()->attach($adminRole);
        }
    }
}