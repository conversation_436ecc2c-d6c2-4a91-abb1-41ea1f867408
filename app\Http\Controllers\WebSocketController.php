<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use Illuminate\Support\Facades\Log;

class WebSocketController implements MessageComponentInterface
{
    protected $clients;
    private $logger;

    public function __construct()
    {
        $this->clients = new \SplObjectStorage;
        $this->logger = Log::channel('websocket');
    }

    public function onOpen(ConnectionInterface $conn)
    {
        $this->clients->attach($conn);
        echo "New connection established ({$conn->resourceId})\n";
    }

    public function onMessage(ConnectionInterface $from, $msg)
    {
        $data = json_decode(trim($msg, '$##'), true);

        if (isset($data['cmd']) && $data['cmd'] === 'login') {
            $response = [
                "status" => 1,
                "sn" => $data["sn"],
                "qrcode" => "https://pixelflow.com.pk/Lease?o=ng==&&t=" . $data["sn"],
                "lang" => "en",
                "volume" => 100,
                "model" => $data["model"] == 1 ? "horizontal" : "vertical",
                "stamp" => time(),
            ];
            $from->send('$##' . json_encode($response) . '##$');
        }

        if ($data['cmd'] === 'heart') {
            $response = [
                "cmd" => "heart",
                "stamp" => time(),
            ];
            $from->send('$##' . json_encode($response) . '##$');
        }

        if ($data['cmd'] === 'rent') {
            $this->handleRentCommand($data, $from);
        }
    }

    public function onClose(ConnectionInterface $conn)
    {
        $this->clients->detach($conn);
        echo "Connection {$conn->resourceId} closed\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e)
    {
        echo "Error: {$e->getMessage()}\n";
        $conn->close();
    }

    public function refreshAds()
    {
        $response = [
            "cmd" => "refresh",
            "stamp" => time(),
        ];

        foreach ($this->clients as $client) {
            $client->send('$##' . json_encode($response) . '##$');
        }
    }

    /**
     * Handle rent command
     */
    private function handleRentCommand($data, ConnectionInterface $from)
    {
        // Extract the device serial number
        $deviceSn = $data['sn'] ?? null;

        // Extract the AIMS value (default to 0 if not provided)
        $aims = $data['aims'] ?? 0;

        // Extract the slot number
        $slotNumber = $data['data']['n'] ?? null;

        // Extract the message ID
        $messageId = $data['data']['msg'] ?? null;

        if (!$deviceSn || !$slotNumber || !$messageId) {
            $this->logger->error("Invalid rent command: missing required parameters", [
                'data' => $data
            ]);
            return;
        }

        // Calculate the actual slot number based on AIMS
        $actualSlotNumber = $slotNumber + ($aims * 12);

        $this->logger->info("Received rent command", [
            'device_sn' => $deviceSn,
            'slot' => $slotNumber,
            'actual_slot' => $actualSlotNumber,
            'aims' => $aims,
            'message_id' => $messageId
        ]);

        // Find the device
        $device = \App\Models\Device::where('sn', $deviceSn)->first();

        if (!$device) {
            $this->logger->error("Device not found", [
                'device_sn' => $deviceSn
            ]);
            return;
        }

        // Find the powerbank in the specified slot
        $powerbank = \App\Models\Powerbank::where('device_id', $device->id)
            ->where('slot_number', $actualSlotNumber)
            ->where('aims', $aims)
            ->first();

        if (!$powerbank) {
            $this->logger->error("Powerbank not found in slot", [
                'device_id' => $device->id,
                'slot_number' => $actualSlotNumber,
                'aims' => $aims
            ]);
            return;
        }

        // Update the powerbank status to 'rented'
        $powerbank->status = 'rented';
        $powerbank->save();

        // Send response to the device
        $response = [
            'cmd' => 'rent',
            'sn' => $deviceSn,
            'data' => [
                'msg' => $messageId,
                'n' => $slotNumber,
                'r' => 1 // Success
            ]
        ];

        $from->send('$##' . json_encode($response) . '##$');

        $this->logger->info("Sent rent response", [
            'response' => $response
        ]);

        // Update the command status in the database
        \App\Models\DeviceCommand::where('device_sn', $deviceSn)
            ->where('command', 'rent')
            ->where('message_id', $messageId)
            ->update([
                'status' => 'completed',
                'response' => json_encode($response)
            ]);
    }
}
