<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Powerbank extends Model
{
    use HasFactory;

    protected $fillable = [
        'serial_number',
        'device_id',
        'capacity',
        'current_charge',
        'slot_number',
        'status',
        'charge_cycles',
        'aims',
        'health',
    ];

    /**
     * Get the device that owns the powerbank.
     */
    public function device()
    {
        return $this->belongsTo(Device::class);
    }

    /**
     * Get the rentals for the powerbank.
     */
    public function rentals()
    {
        return $this->hasMany(PowerbankRental::class)->orderBy('rented_at', 'desc');
    }

    /**
     * Get the latest rental for the powerbank.
     */
    public function latestRental()
    {
        return $this->hasOne(PowerbankRental::class)->latest('rented_at');
    }

    /**
     * Get the issues for the powerbank.
     */
    public function issues()
    {
        return $this->hasMany(PowerbankIssue::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get the recent issues for the powerbank.
     */
    public function recentIssues()
    {
        return $this->issues()->take(5);
    }
}
