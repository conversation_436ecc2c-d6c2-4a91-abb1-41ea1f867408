<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Existing permissions...

        // User Activity permissions
        Permission::firstOrCreate(['name' => 'View User Activity', 'slug' => 'view-user-activity']);

        // Audit Trail permissions
        Permission::firstOrCreate(['name' => 'View Audit Trail', 'slug' => 'view-audit-trail']);

        // Database Backup permissions
        Permission::firstOrCreate(['name' => 'Manage Backups', 'slug' => 'manage-backups']);
    }
}