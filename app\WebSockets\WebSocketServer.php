<?php

namespace App\WebSockets;

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use Illuminate\Support\Facades\Log;
use App\Models\Device;

class WebSocketServer implements MessageComponentInterface
{
    protected $clients;
    protected $deviceConnections = []; // Map device SN to connection
    protected $connectionDevices = []; // Map connection resource ID to device SN

    protected $isSendingRefresh = true;

    public function __construct()
    {
        $this->clients = new \SplObjectStorage;
        Log::channel('websocket')->info('WebSocket server initialized');
    }

    public function onOpen(ConnectionInterface $conn)
    {
        $this->clients->attach($conn);
        $clientId = $conn->resourceId;
        Log::channel('websocket')->info("New connection: ({$clientId})");
        echo "New connection: ({$clientId})\n";
    }

    public function onMessage(ConnectionInterface $from, $msg)
    {
        $clientId = $from->resourceId;
        echo "Message from client {$clientId}: {$msg}\n";
        Log::channel('websocket')->info("Message from client {$clientId}", ['message' => $msg]);

        try {
            $data = json_decode(trim($msg, '$##'), true);

            if (!$data || !isset($data['cmd'])) {
                Log::channel('websocket')->warning("Invalid message format from client {$clientId}", ['message' => $msg]);
                return;
            }

            // Store device connection if this is a login message
            if (isset($data['cmd']) && $data['cmd'] === 'login' && isset($data['sn'])) {
                $deviceSn = $data['sn'];
                $this->registerDeviceConnection($from, $deviceSn);

                // Update device in database
                $this->updateDeviceStatus($deviceSn, $data);

                $response = [
                    "status" => 1,
                    "sn" => $deviceSn,
                    "qrcode" => "https://pixelflow.com.pk/Lease?o=ng==&&t=" . $deviceSn,
                    "lang" => "en",
                    "volume" => 100,
                    "model" => $data["model"] == 0 ? "horizontal" : "vertical",
                    "cmd" => "login",
                    "stamp" => time(),
                ];
                $from->send('$##' . json_encode($response) . '##$');

                Log::channel('websocket')->info("Device login successful", [
                    'device_sn' => $deviceSn,
                    'client_id' => $clientId
                ]);
                echo "Device {$deviceSn} logged in via client {$clientId}\n";
                echo '$##' . json_encode($response) . '##$' . "\n";

                // Send refresh command to device
                if ($this->isSendingRefresh == true) {
                    $this->isSendingRefresh = false;

                    $this->refreshAds();
                    // $refresh = [
                    //     "cmd" => "refresh",
                    //     "stamp" => time(),
                    // ];

                    // $from->send('$##' . json_encode($refresh) . '##$');
                    // echo "Device {$deviceSn} logged in via client {$clientId}\n";
                    // echo '$##' . json_encode($refresh) . '##$' . "\n";
                }
            }

            // Handle heartbeat
            elseif ($data['cmd'] === 'heart') {
                $deviceSn = $data['sn'] ?? $this->getDeviceSn($from);

                if ($deviceSn) {
                    // Update last heartbeat timestamp in database
                    $this->updateDeviceHeartbeat($deviceSn);

                    $response = [
                        "cmd" => "heart",
                        "stamp" => time(),
                    ];
                    $from->send('$##' . json_encode($response) . '##$');

                    Log::channel('websocket')->info("Heartbeat received", [
                        'device_sn' => $deviceSn,
                        'client_id' => $clientId
                    ]);
                    echo "Heartbeat from device {$deviceSn} (client {$clientId})\n";
                    echo '$##' . json_encode($response) . '##$' . "\n";
                } else {
                    Log::channel('websocket')->warning("Heartbeat from unknown device", [
                        'client_id' => $clientId,
                        'message' => $msg
                    ]);
                }
            }

            // Handle other commands
            else {
                $deviceSn = $data['sn'] ?? $this->getDeviceSn($from);

                if ($deviceSn) {
                    Log::channel('websocket')->info("Command received", [
                        'device_sn' => $deviceSn,
                        'client_id' => $clientId,
                        'command' => $data['cmd'],
                        'message' => $msg
                    ]);

                    // Process other commands here
                    // ...

                    // Default response
                    $response = [
                        "cmd" => $data['cmd'],
                        "status" => 1,
                        "stamp" => time(),
                    ];
                    $from->send('$##' . json_encode($response) . '##$');
                }
            }
        } catch (\Exception $e) {
            Log::channel('websocket')->error("Error processing message", [
                'client_id' => $clientId,
                'message' => $msg,
                'error' => $e->getMessage()
            ]);
            echo "Error processing message: {$e->getMessage()}\n";
        }
    }

    public function onClose(ConnectionInterface $conn)
    {
        $clientId = $conn->resourceId;
        $deviceSn = $this->getDeviceSn($conn);

        // Clean up device connection mappings
        if ($deviceSn) {
            unset($this->deviceConnections[$deviceSn]);
            unset($this->connectionDevices[$clientId]);

            // Update device status in database
            $this->updateDeviceOfflineStatus($deviceSn);

            Log::channel('websocket')->info("Device disconnected", [
                'device_sn' => $deviceSn,
                'client_id' => $clientId
            ]);
            echo "Device {$deviceSn} disconnected (client {$clientId})\n";
        } else {
            Log::channel('websocket')->info("Connection closed", ['client_id' => $clientId]);
            echo "Connection closed: ({$clientId})\n";
        }

        $this->clients->detach($conn);
    }

    public function onError(ConnectionInterface $conn, \Exception $e)
    {
        $clientId = $conn->resourceId;
        $deviceSn = $this->getDeviceSn($conn);

        if ($deviceSn) {
            Log::channel('websocket')->error("Error on device connection", [
                'device_sn' => $deviceSn,
                'client_id' => $clientId,
                'error' => $e->getMessage()
            ]);
            echo "Error on device {$deviceSn} (client {$clientId}): {$e->getMessage()}\n";

            // Update device status in database
            $this->updateDeviceOfflineStatus($deviceSn);
        } else {
            Log::channel('websocket')->error("Connection error", [
                'client_id' => $clientId,
                'error' => $e->getMessage()
            ]);
            echo "Error: {$e->getMessage()} (client {$clientId})\n";
        }

        $conn->close();
    }

    /**
     * Register a device connection
     */
    protected function registerDeviceConnection(ConnectionInterface $conn, string $deviceSn)
    {
        $clientId = $conn->resourceId;

        // If this device was already connected, clean up the old connection
        if (isset($this->deviceConnections[$deviceSn])) {
            $oldConn = $this->deviceConnections[$deviceSn];
            $oldClientId = $oldConn->resourceId;

            if ($oldClientId != $clientId) {
                Log::channel('websocket')->info("Device reconnected from a different client", [
                    'device_sn' => $deviceSn,
                    'old_client_id' => $oldClientId,
                    'new_client_id' => $clientId
                ]);
                echo "Device {$deviceSn} reconnected from client {$oldClientId} to {$clientId}\n";

                // Remove old connection mapping
                unset($this->connectionDevices[$oldClientId]);

                // Close old connection if it's still in the clients list
                $found = false;
                $this->clients->rewind();
                while ($this->clients->valid()) {
                    $client = $this->clients->current();
                    if ($client->resourceId == $oldClientId) {
                        $found = true;
                        break;
                    }
                    $this->clients->next();
                }

                if ($found) {
                    try {
                        $client->close();
                    } catch (\Exception $e) {
                        Log::channel('websocket')->error("Error closing old connection", [
                            'device_sn' => $deviceSn,
                            'client_id' => $oldClientId,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
        }

        // Register the new connection
        $this->deviceConnections[$deviceSn] = $conn;
        $this->connectionDevices[$clientId] = $deviceSn;

        Log::channel('websocket')->info("Device connection registered", [
            'device_sn' => $deviceSn,
            'client_id' => $clientId
        ]);
    }

    /**
     * Get device SN for a connection
     */
    protected function getDeviceSn(ConnectionInterface $conn)
    {
        return $this->connectionDevices[$conn->resourceId] ?? null;
    }

    /**
     * Update device status in database
     */
    protected function updateDeviceStatus(string $deviceSn, array $data)
    {
        try {
            $device = Device::where('sn', $deviceSn)->first();

            $deviceAttributes = [
                'status' => 1, // Online
                'last_heartbeat_at' => now(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'firmware_version' => $data['fw'] ?? null,
                'model' => $data['model'] ?? null,
                'type' => $data['type'] ?? null,
            ];

            if ($device) {
                $device->update($deviceAttributes);
                Log::channel('websocket')->info("Updated existing device", ['device_sn' => $deviceSn]);
            } else {
                // Create new device
                $deviceAttributes['sn'] = $deviceSn;
                $deviceAttributes['name'] = "Device {$deviceSn}";

                Device::create($deviceAttributes);
                Log::channel('websocket')->info("Created new device", ['device_sn' => $deviceSn]);
            }
        } catch (\Exception $e) {
            Log::channel('websocket')->error("Error updating device in database", [
                'device_sn' => $deviceSn,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update device heartbeat timestamp
     */
    protected function updateDeviceHeartbeat(string $deviceSn)
    {
        try {
            $device = Device::where('sn', $deviceSn)->first();

            if ($device) {
                $device->update([
                    'status' => 1, // Online
                    'last_heartbeat_at' => now(),
                ]);
            }
        } catch (\Exception $e) {
            Log::channel('websocket')->error("Error updating device heartbeat", [
                'device_sn' => $deviceSn,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update device offline status
     */
    protected function updateDeviceOfflineStatus(string $deviceSn)
    {
        try {
            $device = Device::where('sn', $deviceSn)->first();

            if ($device) {
                $device->update([
                    'status' => 0, // Offline
                ]);
                Log::channel('websocket')->info("Device marked as offline", ['device_sn' => $deviceSn]);
            }
        } catch (\Exception $e) {
            Log::channel('websocket')->error("Error updating device offline status", [
                'device_sn' => $deviceSn,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send a message to a specific device
     */
    public function sendToDevice(string $deviceSn, array $message)
    {
        if (isset($this->deviceConnections[$deviceSn])) {
            $conn = $this->deviceConnections[$deviceSn];

            try {
                $conn->send('$##' . json_encode($message) . '##$');
                Log::channel('websocket')->info("Message sent to device", [
                    'device_sn' => $deviceSn,
                    'message' => $message
                ]);
                return true;
            } catch (\Exception $e) {
                Log::channel('websocket')->error("Error sending message to device", [
                    'device_sn' => $deviceSn,
                    'error' => $e->getMessage()
                ]);
                return false;
            }
        } else {
            Log::channel('websocket')->warning("Device not connected", ['device_sn' => $deviceSn]);
            return false;
        }
    }

    /**
     * Send a message to all connected devices
     */
    public function sendToAllDevices(array $message)
    {
        $successCount = 0;
        $failCount = 0;

        foreach ($this->deviceConnections as $deviceSn => $conn) {
            try {
                $conn->send('$##' . json_encode($message) . '##$');
                $successCount++;
            } catch (\Exception $e) {
                Log::channel('websocket')->error("Error sending message to device", [
                    'device_sn' => $deviceSn,
                    'error' => $e->getMessage()
                ]);
                $failCount++;
            }
        }

        Log::channel('websocket')->info("Broadcast message sent", [
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'message' => $message
        ]);

        return [
            'success_count' => $successCount,
            'fail_count' => $failCount
        ];
    }

    public function refreshAds()
    {
        $response = [
            "cmd" => "refresh",
            "stamp" => time(),
        ];

        foreach ($this->deviceConnections as $deviceSn => $conn) {
            $conn->send('$##' . json_encode($response) . '##$');
        }
    }


    /**
     * Get all connected devices
     */
    public function getConnectedDevices()
    {
        return array_keys($this->deviceConnections);
    }

    /**
     * Check if a device is connected
     */
    public function isDeviceConnected(string $deviceSn)
    {
        return isset($this->deviceConnections[$deviceSn]);
    }

    /**
     * Get connection count
     */
    public function getConnectionCount()
    {
        return count($this->clients);
    }

    /**
     * Get device connection count
     */
    public function getDeviceConnectionCount()
    {
        return count($this->deviceConnections);
    }
}
