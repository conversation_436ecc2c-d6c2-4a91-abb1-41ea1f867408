<?php

namespace App\Http\Controllers;

use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PermissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('view-permissions');

        $permissions = Permission::with('roles')->get();
        $groupedPermissions = $this->groupPermissions($permissions);
        return view('admin.permissions.index', compact('permissions', 'groupedPermissions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create-permissions');

        return view('admin.permissions.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create-permissions');

        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:permissions'],
            'description' => ['nullable', 'string', 'max:255'],
        ]);

        Permission::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
        ]);

        return redirect()->route('permission.index')
            ->with('success', 'Permission created successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $this->authorize('view-permissions');

        $permission = Permission::with('roles')->findOrFail($id);
        $roles = $permission->roles;
        return view('admin.permissions.show', compact('permission', 'roles'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $this->authorize('edit-permissions');

        $permission = Permission::findOrFail($id);
        return view('admin.permissions.edit', compact('permission'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $this->authorize('edit-permissions');

        $permission = Permission::findOrFail($id);

        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:permissions,name,' . $id],
            'description' => ['nullable', 'string', 'max:255'],
        ]);

        $permission->name = $request->name;
        $permission->slug = Str::slug($request->name);
        $permission->description = $request->description;
        $permission->save();

        return redirect()->route('permission.index')
            ->with('success', 'Permission updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->authorize('delete-permissions');

        $permission = Permission::findOrFail($id);

        // Check if permission is in use
        if ($permission->roles()->count() > 0) {
            return redirect()->route('permission.index')
                ->with('error', 'Cannot delete permission because it is assigned to roles');
        }

        $permission->delete();

        return redirect()->route('permission.index')
            ->with('success', 'Permission deleted successfully');
    }

    /**
     * Group permissions by category for better organization
     */
    private function groupPermissions($permissions)
    {
        $groups = [
            'Dashboard' => [
                'icon' => 'fe-home',
                'description' => 'Dashboard and overview access',
                'permissions' => []
            ],
            'User Management' => [
                'icon' => 'fe-users',
                'description' => 'User accounts and profile management',
                'permissions' => []
            ],
            'Role & Permission Management' => [
                'icon' => 'fe-shield',
                'description' => 'Roles and permissions administration',
                'permissions' => []
            ],
            'Device Management' => [
                'icon' => 'fe-monitor',
                'description' => 'Device operations and monitoring',
                'permissions' => []
            ],
            'Device Group Management' => [
                'icon' => 'fe-layers',
                'description' => 'Device Group operations',
                'permissions' => []
            ],
            'Customer Management' => [
                'icon' => 'fe-user-check',
                'description' => 'Customer accounts and services',
                'permissions' => []
            ],
            'Ad Management' => [
                'icon' => 'fe-image',
                'description' => 'Advertisement materials and scheduling',
                'permissions' => []
            ],
            'System Administration' => [
                'icon' => 'fe-settings',
                'description' => 'System monitoring and administration',
                'permissions' => []
            ]
        ];

        foreach ($permissions as $permission) {
            $slug = $permission->slug;
            $assigned = false;

            // Dashboard permissions
            if (str_contains($slug, 'dashboard')) {
                $groups['Dashboard']['permissions'][] = $permission;
                $assigned = true;
            }

            // User management permissions
            elseif (str_contains($slug, 'user') && !str_contains($slug, 'activity')) {
                $groups['User Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Role and permission management
            elseif (str_contains($slug, 'role') || str_contains($slug, 'permission')) {
                $groups['Role & Permission Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Device management
            elseif (str_contains($slug, 'device') && !str_contains($slug, 'device-group')) {
                $groups['Device Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Device Group Management
            elseif (str_contains($slug, 'device-group')) {
                $groups['Device Group Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Customer management
            elseif (str_contains($slug, 'customer')) {
                $groups['Customer Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // Ad management
            elseif (str_contains($slug, 'ad-')) {
                $groups['Ad Management']['permissions'][] = $permission;
                $assigned = true;
            }

            // System administration
            elseif (str_contains($slug, 'activity') || str_contains($slug, 'audit') || str_contains($slug, 'backup')) {
                $groups['System Administration']['permissions'][] = $permission;
                $assigned = true;
            }

            // If not assigned to any group, put in System Administration
            if (!$assigned) {
                $groups['System Administration']['permissions'][] = $permission;
            }
        }

        return $groups;
    }
}
