<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Jen<PERSON><PERSON>\Agent\Agent;

class UserActivity extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'action',
        'module',
        'description',
        'properties',
        'ip_address',
        'user_agent',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that performed the activity.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the browser name from user agent.
     *
     * @return string
     */
    public function getBrowser()
    {
        $agent = new Agent();
        $agent->setUserAgent($this->user_agent);
        return $agent->browser() . ' ' . $agent->version($agent->browser());
    }

    /**
     * Get the platform from user agent.
     *
     * @return string
     */
    public function getPlatform()
    {
        $agent = new Agent();
        $agent->setUserAgent($this->user_agent);
        return $agent->platform() . ' ' . $agent->version($agent->platform());
    }

    /**
     * Get the device type from user agent.
     *
     * @return string
     */
    public function getDevice()
    {
        $agent = new Agent();
        $agent->setUserAgent($this->user_agent);

        if ($agent->isDesktop()) {
            return 'Desktop';
        } elseif ($agent->isTablet()) {
            return 'Tablet';
        } elseif ($agent->isPhone()) {
            return 'Phone';
        } elseif ($agent->isRobot()) {
            return 'Robot';
        } else {
            return 'Unknown';
        }
    }
}
