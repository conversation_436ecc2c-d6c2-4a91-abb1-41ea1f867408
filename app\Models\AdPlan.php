<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'start_time',
        'end_time',
        'is_active',
        'priority'
    ];

    public function adMaterials()
    {
        return $this->belongsToMany(AdMaterial::class, 'ad_plan_materials')
            ->withPivot('display_order', 'display_time')
            ->withTimestamps();
    }

    /**
     * The device groups this plan is assigned to.
     */
    public function groups()
    {
        return $this->belongsToMany(DeviceGroup::class, 'ad_plan_device_group')
            ->withTimestamps();
    }
}
