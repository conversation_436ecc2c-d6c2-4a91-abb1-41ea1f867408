<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Device;
use App\Models\DeviceCommand;
use App\Models\Powerbank;
use App\Models\PowerbankRental;
use App\Models\PowerbankIssue;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PowerbankController extends Controller
{
    /**
     * Display a listing of all powerbanks.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $powerbanks = Powerbank::all();
        $devices = Device::all();
        return view('powerbanks.index', compact('powerbanks', 'devices'));
    }

    /**
     * Display a listing of powerbanks for a specific device.
     *
     * @param  int  $deviceId
     * @return \Illuminate\Http\Response
     */
    public function byDevice($deviceId)
    {
        $device = Device::findOrFail($deviceId);
        $powerbanks = Powerbank::where('device_id', $deviceId)->get();

        return view('powerbanks.by-device', compact('device', 'powerbanks'));
    }

    /**
     * Show the form for creating a new powerbank.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $devices = Device::all();
        $selectedDeviceId = $request->input('device_id');

        return view('powerbanks.create', compact('devices', 'selectedDeviceId'));
    }

    /**
     * Store a newly created powerbank in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'serial_number' => 'required|string|unique:powerbanks',
            'device_id' => 'nullable|exists:devices,id',
            'capacity' => 'required|integer|min:1',
            'current_charge' => 'required|integer|min:0|max:100',
            'slot_number' => 'nullable|integer|min:1',
            'status' => 'required|in:available,rented,charging,maintenance',
            'charge_cycles' => 'nullable|integer|min:0',
            'aims' => 'nullable|integer|min:0|max:4',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $powerbank = Powerbank::create($request->all());

        return redirect()->route('powerbanks.show', $powerbank->id)
            ->with('success', 'Powerbank created successfully.');
    }

    /**
     * Display the specified powerbank.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        $powerbank = Powerbank::with(['device', 'rentals.customer'])->findOrFail($id);

        // Get the latest rental if any
        $latestRental = $powerbank->rentals()->whereNull('returned_at')->latest('rented_at')->first();

        // Check if we need to show the issue resolution form
        $issueToResolve = null;
        if ($request->has('resolve_issue')) {
            $issueToResolve = PowerbankIssue::findOrFail($request->resolve_issue);
        }

        // Get recent issues
        $recentIssues = PowerbankIssue::where('powerbank_id', $id)
            ->latest()
            ->take(5)
            ->get();

        return view('powerbanks.show', compact('powerbank', 'latestRental', 'issueToResolve', 'recentIssues'));
    }

    /**
     * Show the form for editing the specified powerbank.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $powerbank = Powerbank::findOrFail($id);
        $devices = Device::all();

        return view('powerbanks.edit', compact('powerbank', 'devices'));
    }

    /**
     * Update the specified powerbank in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $powerbank = Powerbank::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'serial_number' => 'required|string|unique:powerbanks,serial_number,' . $id,
            'device_id' => 'nullable|exists:devices,id',
            'capacity' => 'required|integer|min:1',
            'current_charge' => 'required|integer|min:0|max:100',
            'slot_number' => 'nullable|integer|min:1',
            'status' => 'required|in:available,rented,charging,maintenance',
            'charge_cycles' => 'nullable|integer|min:0',
            'aims' => 'nullable|integer|min:0|max:4',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $powerbank->update($request->all());

        return redirect()->route('powerbanks.show', $powerbank->id)
            ->with('success', 'Powerbank updated successfully.');
    }

    /**
     * Remove the specified powerbank from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $powerbank = Powerbank::findOrFail($id);
        $deviceId = $powerbank->device_id;

        $powerbank->delete();

        if ($deviceId) {
            return redirect()->route('powerbanks.by-device', $deviceId)
                ->with('success', 'Powerbank deleted successfully.');
        }

        return redirect()->route('powerbanks.index')
            ->with('success', 'Powerbank deleted successfully.');
    }

    /**
     * Rent a powerbank to a customer.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function rent(Request $request, $id)
    {
        $powerbank = Powerbank::findOrFail($id);

        if ($powerbank->status !== 'available') {
            return redirect()->back()
                ->with('error', 'This powerbank is not available for rent.');
        }

        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Create rental record
        PowerbankRental::create([
            'powerbank_id' => $powerbank->id,
            'device_id' => $powerbank->device_id,
            'customer_id' => $request->customer_id,
            'rented_at' => now(),
            'initial_charge' => $powerbank->current_charge,
            'aims' => $powerbank->aims ?? 0,
            'slot_number' => $powerbank->slot_number,
        ]);

        // Update powerbank status
        $powerbank->update([
            'status' => 'rented'
        ]);

        return redirect()->route('powerbanks.show', $powerbank->id)
            ->with('success', 'Powerbank rented successfully.');
    }

    /**
     * Return a rented powerbank.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function returnPowerbank(Request $request, $id)
    {
        $powerbank = Powerbank::findOrFail($id);

        if ($powerbank->status !== 'rented') {
            return redirect()->back()
                ->with('error', 'This powerbank is not currently rented.');
        }

        $validator = Validator::make($request->all(), [
            'return_charge' => 'required|integer|min:0|max:100',
            'rental_fee' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update the latest rental record
        $rental = PowerbankRental::where('powerbank_id', $powerbank->id)
            ->whereNull('returned_at')
            ->latest('rented_at')
            ->first();

        if ($rental) {
            $rental->update([
                'returned_at' => now(),
                'return_charge' => $request->return_charge,
                'rental_fee' => $request->rental_fee,
            ]);
        }

        // Update powerbank
        $powerbank->update([
            'status' => $request->return_charge < 20 ? 'charging' : 'available',
            'current_charge' => $request->return_charge,
            'charge_cycles' => $powerbank->charge_cycles + 1,
        ]);

        return redirect()->route('powerbanks.show', $powerbank->id)
            ->with('success', 'Powerbank returned successfully.');
    }

    /**
     * Mark a powerbank as fully charged.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function markCharged($id)
    {
        $powerbank = Powerbank::findOrFail($id);

        if ($powerbank->status !== 'charging') {
            return redirect()->back()
                ->with('error', 'This powerbank is not in charging status.');
        }

        $powerbank->update([
            'status' => 'available',
            'current_charge' => 100,
        ]);

        return redirect()->route('powerbanks.show', $powerbank->id)
            ->with('success', 'Powerbank marked as fully charged.');
    }

    /**
     * Report an issue with a powerbank.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function reportIssue(Request $request, $id)
    {
        $powerbank = Powerbank::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'issue_type' => 'required|in:damage,malfunction,battery,other',
            'description' => 'required|string',
            'rental_id' => 'nullable|exists:powerbank_rentals,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Find the rental if it exists
        $rental = null;
        $customer = null;
        if ($request->rental_id) {
            $rental = PowerbankRental::find($request->rental_id);
            if ($rental) {
                $customer = $rental->customer;
            }
        }

        // Create issue record
        $issue = PowerbankIssue::create([
            'powerbank_id' => $powerbank->id,
            'rental_id' => $request->rental_id,
            'reported_by' => auth()->id(),
            'customer_id' => $customer ? $customer->id : null,
            'issue_type' => $request->issue_type,
            'description' => $request->description,
            'status' => 'reported',
        ]);

        // Update powerbank status to maintenance
        $powerbank->update([
            'status' => 'maintenance'
        ]);

        return redirect()->route('powerbanks.show', $powerbank->id)
            ->with('success', 'Issue reported successfully.');
    }

    /**
     * Resolve an issue with a powerbank.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $issueId
     * @return \Illuminate\Http\Response
     */
    public function resolveIssue(Request $request, $issueId)
    {
        $issue = PowerbankIssue::findOrFail($issueId);
        $powerbank = $issue->powerbank;

        $validator = Validator::make($request->all(), [
            'resolution' => 'required|string',
            'status' => 'required|in:resolved,replaced',
            'powerbank_status' => 'required|in:available,charging,maintenance',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update issue
        $issue->update([
            'status' => $request->status,
            'resolution' => $request->resolution,
            'resolved_by' => auth()->id(),
            'resolved_at' => now(),
        ]);

        // Update powerbank status
        $powerbank->update([
            'status' => $request->powerbank_status
        ]);

        return redirect()->route('powerbanks.show', $powerbank->id)
            ->with('success', 'Issue resolved successfully.');
    }

    /**
     * Show all issues for a specific powerbank.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function issues($id)
    {
        $powerbank = Powerbank::findOrFail($id);
        $issues = PowerbankIssue::where('powerbank_id', $id)
            ->latest()
            ->paginate(10);

        return view('powerbanks.issues', compact('powerbank', 'issues'));
    }

    /**
     * Display a listing of all powerbank issues.
     *
     * @return \Illuminate\Http\Response
     */
    public function allIssues()
    {
        $issues = PowerbankIssue::with(['powerbank', 'reporter'])
            ->latest()
            ->paginate(15);

        return view('powerbanks.all-issues', compact('issues'));
    }

    /**
     * Eject a powerbank from a device.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function ejectPowerbank(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'powerbank_id' => 'required|exists:powerbanks,id',
                'device_sn' => 'required|string',
                'slot_number' => 'required|integer|min:1|max:12',
                'aims' => 'required|integer|min:0|max:4',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            $powerbankId = $request->input('powerbank_id');
            $deviceSn = $request->input('device_sn');
            $slotNumber = $request->input('slot_number');
            $aims = $request->input('aims', 0);

            // Find the powerbank
            $powerbank = Powerbank::findOrFail($powerbankId);

            // Update powerbank status - explicitly set slot_number to NULL
            $powerbank->update([
                'status' => 'rented',
                'slot_number' => null, // Explicitly set to NULL, not 12
            ]);

            // If command was sent directly to device, we don't need to create a command record
            if (!$request->has('command_sent')) {
                // Create a device command record
                $messageId = mt_rand(10000, 99999);

                DeviceCommand::create([
                    'device_sn' => $deviceSn,
                    'command' => 'rent',
                    'message_id' => $messageId,
                    'data' => json_encode([
                        'n' => (int)$slotNumber,
                        'aims' => (int)$aims
                    ]),
                    'raw_command' => "#*{\"cmd\":\"rent\",\"sn\":\"{$deviceSn}\",\"aims\":{$aims},\"data\":{\"msg\":{$messageId},\"n\":{$slotNumber}}}*#",
                    'status' => 'pending',
                ]);
            }

            return redirect()->back()->with('success', 'Powerbank ejected successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to eject powerbank: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to eject powerbank: ' . $e->getMessage());
        }
    }

    /**
     * Display a listing of available powerbanks for customers.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function available(Request $request)
    {
        // Get the authenticated customer
        $customer = \App\Models\Customer::find(session('verified_customer_id'));

        // Check if customer already has a rented powerbank
        $activeRental = \App\Models\PowerbankRental::where('customer_id', $customer->id)
            ->whereNull('returned_at')
            ->first();

        // Check if a device SN is provided in the request or session
        $deviceSn = $request->get('t') ?? session('selected_device_sn');
        $selectedDevice = null;

        if ($deviceSn) {
            // Get the specific device
            $selectedDevice = \App\Models\Device::where('sn', $deviceSn)->first();

            // Clear the session variable after use
            session()->forget('selected_device_sn');
        }

        // Get all devices with available powerbanks
        $devices = \App\Models\Device::whereHas('powerbanks', function ($query) {
            $query->where('status', 'available');
        })->get();

        return view('powerbanks.available', compact('devices', 'customer', 'activeRental', 'selectedDevice'));
    }
}
