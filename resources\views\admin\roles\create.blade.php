@extends('layouts.master')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">Create New Role</h4>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('roles.store') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="name" class="form-label">Role Name</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                    id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description"
                                    rows="3">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Permissions</label>
                                <p class="text-muted small mb-3">Select the permissions you want to assign to this role.
                                    Permissions are organized by category for easier management.</p>

                                <!-- Select All Controls -->
                                <div class="mb-3 p-3 bg-light rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-bold">Quick Actions:</span>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-primary me-2"
                                                onclick="selectAllPermissions()">
                                                <i class="fe-check-square"></i> Select All
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                                onclick="deselectAllPermissions()">
                                                <i class="fe-square"></i> Deselect All
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Permission Groups -->
                                <div class="accordion" id="permissionAccordion">
                                    @foreach ($groupedPermissions as $groupName => $group)
                                        <div class="accordion-item mb-2">
                                            <h2 class="accordion-header" id="heading{{ Str::slug($groupName) }}">
                                                <button class="accordion-button {{ $loop->first ? '' : 'collapsed' }}"
                                                    type="button" data-bs-toggle="collapse"
                                                    data-bs-target="#collapse{{ Str::slug($groupName) }}"
                                                    aria-expanded="{{ $loop->first ? 'true' : 'false' }}"
                                                    aria-controls="collapse{{ Str::slug($groupName) }}">
                                                    <div class="d-flex align-items-center">
                                                        <i class="{{ $group['icon'] }} me-2 text-primary"></i>
                                                        <div>
                                                            <strong>{{ $groupName }}</strong>
                                                            <small
                                                                class="d-block text-muted">{{ $group['description'] }}</small>
                                                        </div>
                                                        <span
                                                            class="badge bg-secondary ms-auto me-3">{{ count($group['permissions']) }}
                                                            permissions</span>
                                                    </div>
                                                </button>
                                            </h2>
                                            <div id="collapse{{ Str::slug($groupName) }}"
                                                class="accordion-collapse collapse {{ $loop->first ? 'show' : '' }}"
                                                aria-labelledby="heading{{ Str::slug($groupName) }}"
                                                data-bs-parent="#permissionAccordion">
                                                <div class="accordion-body">
                                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                                        <span class="text-muted">{{ $group['description'] }}</span>
                                                        <div>
                                                            <button type="button"
                                                                class="btn btn-sm btn-outline-success me-1"
                                                                onclick="selectGroupPermissions('{{ Str::slug($groupName) }}')">
                                                                <i class="fe-check"></i> Select All
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                                onclick="deselectGroupPermissions('{{ Str::slug($groupName) }}')">
                                                                <i class="fe-x"></i> Deselect All
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        @foreach ($group['permissions'] as $permission)
                                                            <div class="col-md-6 col-lg-4 mb-3">
                                                                <div class="form-check p-3 border rounded border-light">
                                                                    <input
                                                                        class="form-check-input permission-checkbox group-{{ Str::slug($groupName) }}"
                                                                        type="checkbox" name="permissions[]"
                                                                        value="{{ $permission->id }}"
                                                                        id="permission_{{ $permission->id }}">
                                                                    <label class="form-check-label w-100"
                                                                        for="permission_{{ $permission->id }}">
                                                                        <div class="fw-bold">{{ $permission->name }}</div>
                                                                        <small
                                                                            class="text-muted">{{ $permission->description }}</small>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ route('roles.index') }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Create Role</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        // Select all permissions
        function selectAllPermissions() {
            document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
                checkbox.checked = true;
                updateCheckboxStyle(checkbox);
            });
        }

        // Deselect all permissions
        function deselectAllPermissions() {
            document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
                checkbox.checked = false;
                updateCheckboxStyle(checkbox);
            });
        }

        // Select all permissions in a specific group
        function selectGroupPermissions(groupSlug) {
            document.querySelectorAll('.group-' + groupSlug).forEach(function(checkbox) {
                checkbox.checked = true;
                updateCheckboxStyle(checkbox);
            });
        }

        // Deselect all permissions in a specific group
        function deselectGroupPermissions(groupSlug) {
            document.querySelectorAll('.group-' + groupSlug).forEach(function(checkbox) {
                checkbox.checked = false;
                updateCheckboxStyle(checkbox);
            });
        }

        // Update checkbox styling based on checked state
        function updateCheckboxStyle(checkbox) {
            const container = checkbox.closest('.form-check');
            if (checkbox.checked) {
                container.classList.remove('border-light');
                container.classList.add('border-primary', 'bg-primary', 'bg-opacity-10');
            } else {
                container.classList.remove('border-primary', 'bg-primary', 'bg-opacity-10');
                container.classList.add('border-light');
            }
        }

        // Add event listeners to all checkboxes for styling updates
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    updateCheckboxStyle(this);
                });
            });

            // Update selected count
            updateSelectedCount();
        });

        // Update selected permissions count
        function updateSelectedCount() {
            const totalPermissions = document.querySelectorAll('.permission-checkbox').length;
            const selectedPermissions = document.querySelectorAll('.permission-checkbox:checked').length;

            // You can add a counter display here if needed
            console.log(`Selected ${selectedPermissions} of ${totalPermissions} permissions`);
        }

        // Add change event to update count
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('permission-checkbox')) {
                updateSelectedCount();
            }
        });
    </script>
@endsection
