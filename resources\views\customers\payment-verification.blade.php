@extends('layouts.customer')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Payment Verification</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">We'll verify your payment method with a small authorization (no actual charge).</p>

                        @error('payment')
                            <div class="alert alert-danger">{{ $message }}</div>
                        @enderror

                        <div id="paypal-button-container" class="my-4"></div>

                        <div class="text-center mt-3">
                            <p>- OR -</p>
                            <button type="button" id="show-manual-entry" class="btn btn-link">
                                Enter card details manually
                            </button>
                        </div>

                        <form id="credit-card-form" action="{{ route('customer.verify-payment') }}" method="POST"
                            style="display: none;">
                            @csrf
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="card_holder_name"
                                            name="card_holder_name" placeholder="Card Holder Name" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-credit-card"></i></span>
                                        <input type="text" class="form-control" id="card_number" name="card_number"
                                            placeholder="1234 1234 1234 1234" required>
                                        <div id="card-brand" class="input-group-text">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        <input type="text" class="form-control" id="card_expiry" name="card_expiry"
                                            placeholder="MM/YY" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="text" class="form-control" id="card_cvc" name="card_cvc"
                                            placeholder="CVC" required>
                                    </div>
                                </div>
                            </div>

                            <button id="manual-submit" type="submit" class="btn btn-primary mt-3">
                                Verify Card
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script src="https://www.paypal.com/sdk/js?client-id={{ config('services.paypal.client_id') }}&currency=USD"></script>
        <script>
            // Show manual entry form when button is clicked
            document.getElementById('show-manual-entry').addEventListener('click', function() {
                document.getElementById('credit-card-form').style.display = 'block';
                document.getElementById('show-manual-entry').style.display = 'none';
            });

            // Initialize PayPal buttons
            paypal.Buttons({
                createOrder: function() {
                    // Call your server to create a PayPal order
                    return fetch('{{ route('customer.create-paypal-order') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            }
                        })
                        .then(function(response) {
                            return response.json();
                        })
                        .then(function(data) {
                            if (data.error) {
                                throw new Error(data.error);
                            }
                            return data.id;
                        });
                },
                onApprove: function(data) {
                    // Payment approved, redirect to callback URL
                    window.location.href = '{{ route('customer.verify-payment.callback') }}?token=' + data.orderID;
                },
                onError: function(err) {
                    console.error('PayPal error:', err);
                    alert('There was an error processing your payment. Please try again or use manual entry.');
                }
            }).render('#paypal-button-container');

            // Credit card input formatting
            document.addEventListener('DOMContentLoaded', function() {
                const cardBrandElement = document.getElementById('card-brand');

                document.getElementById('card_number').addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 16) value = value.slice(0, 16);

                    // Add spaces every 4 digits
                    let formattedValue = '';
                    for (let i = 0; i < value.length; i++) {
                        if (i > 0 && i % 4 === 0) formattedValue += ' ';
                        formattedValue += value[i];
                    }

                    e.target.value = formattedValue;

                    // Update card brand icon based on first digits
                    updateCardBrandIcon(value);
                });

                document.getElementById('card_expiry').addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 4) value = value.slice(0, 4);

                    // Format as MM/YY
                    if (value.length > 2) {
                        value = value.slice(0, 2) + '/' + value.slice(2);
                    }

                    e.target.value = value;
                });

                document.getElementById('card_cvc').addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 4) value = value.slice(0, 4);
                    e.target.value = value;
                });

                // Detect card type based on number
                function detectCardType(number) {
                    const re = {
                        visa: /^4/,
                        mastercard: /^5[1-5]/,
                        amex: /^3[47]/,
                        discover: /^(6011|65|64[4-9]|622)/
                    };

                    for (const [type, pattern] of Object.entries(re)) {
                        if (pattern.test(number)) {
                            return type;
                        }
                    }
                    return 'unknown';
                }

                // Update card brand icon
                function updateCardBrandIcon(cardNumber) {
                    const brand = detectCardType(cardNumber);
                    let iconClass = 'fa-credit-card'; // default

                    // Set icon based on card brand
                    switch (brand) {
                        case 'visa':
                            iconClass = 'fa-cc-visa';
                            cardBrandElement.style.color = '#1A1F71';
                            break;
                        case 'mastercard':
                            iconClass = 'fa-cc-mastercard';
                            cardBrandElement.style.color = '#EB001B';
                            break;
                        case 'amex':
                            iconClass = 'fa-cc-amex';
                            cardBrandElement.style.color = '#006FCF';
                            break;
                        case 'discover':
                            iconClass = 'fa-cc-discover';
                            cardBrandElement.style.color = '#FF6600';
                            break;
                        default:
                            cardBrandElement.style.color = '';
                    }

                    cardBrandElement.innerHTML = `<i class="fab ${iconClass}"></i>`;
                }
            });
        </script>
    @endpush

    @push('styles')
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
        <style>
            #paypal-button-container {
                max-width: 500px;
                margin: 0 auto;
            }

            .input-group-text {
                background-color: #f8f9fa;
            }

            .input-group-text i {
                font-size: 18px;
            }

            #card-brand {
                min-width: 46px;
                justify-content: center;
            }

            #card-brand i {
                font-size: 20px;
            }

            .form-control:focus {
                border-color: #80bdff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }

            .is-valid {
                border-color: #28a745 !important;
            }

            .is-invalid {
                border-color: #dc3545 !important;
            }

            #manual-submit {
                min-width: 120px;
            }
        </style>
    @endpush
@endsection
