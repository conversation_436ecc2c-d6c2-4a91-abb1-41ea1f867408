@extends('layouts.master')

@section('title', 'Users Management')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Users Management</h4>
                        <div class="card-tools">
                            @can('create-users')
                                <a href="{{ route('user.create') }}" class="btn btn-primary">
                                    <i class="fe-plus-circle mr-1"></i> Add User
                                </a>
                            @endcan
                        </div>
                    </div>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        <div class="table-responsive">
                            <table class="table table-centered table-striped dt-responsive nowrap w-100">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Roles</th>
                                        <th>Created At</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($users as $user)
                                        <tr>
                                            <td>{{ $user->id }}</td>
                                            <td>{{ $user->name }}</td>
                                            <td>{{ $user->email }}</td>
                                            <td>
                                                @foreach ($user->roles as $role)
                                                    <span class="badge badge-info">{{ $role->name }}</span>
                                                @endforeach
                                            </td>
                                            <td>{{ $user->created_at->format('M d, Y') }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    @can('view-users')
                                                        <a href="{{ route('user.show', $user->id) }}"
                                                            class="btn btn-sm btn-info">
                                                            <i class="fe-eye"></i>
                                                        </a>
                                                    @endcan

                                                    @can('edit-users')
                                                        <a href="{{ route('user.edit', $user->id) }}"
                                                            class="btn btn-sm btn-primary">
                                                            <i class="fe-edit"></i>
                                                        </a>
                                                    @endcan

                                                    @can('assign-roles')
                                                        <a href="{{ route('users.roles.edit', $user) }}"
                                                            class="btn btn-sm btn-warning">
                                                            <i class="fe-users"></i>
                                                        </a>
                                                    @endcan

                                                    @can('delete-users')
                                                        @if (auth()->id() != $user->id)
                                                            <form action="{{ route('user.destroy', $user->id) }}"
                                                                method="POST" class="d-inline">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-danger delid">
                                                                    <i class="fe-trash-2"></i>
                                                                </button>
                                                            </form>
                                                        @endif
                                                    @endcan
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination justify-content-center mt-3">
                            {{ $users->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            $('.table').DataTable({
                "pageLength": 10,
                "lengthMenu": [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                "order": [
                    [0, "desc"]
                ],
                "columnDefs": [{
                    "orderable": false,
                    "targets": 5
                }]
            });
        });
    </script>
@endsection
