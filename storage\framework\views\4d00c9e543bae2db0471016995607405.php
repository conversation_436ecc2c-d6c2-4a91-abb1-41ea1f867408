<!-- ========== Left Sidebar Start ========== -->
<div class="left-side-menu">

    <div class="slimscroll-menu">

        <!--- Sidemenu -->
        <div id="sidebar-menu">

            <ul class="metismenu" id="side-menu">

                <li class="menu-title">Navigation</li>

                <li>
                    <a href="<?php echo e(route('dashboard')); ?>">
                        <i class="fe-home"></i>
                        <span> Dashboard </span>
                    </a>
                </li>

                <!-- User, Role & Permission Management Section -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-users')): ?>
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-users"></i>
                            <span>User Management</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-users')): ?>
                                <li><a href="<?php echo e(route('user.index')); ?>">List Users</a></li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-users')): ?>
                                <li><a href="<?php echo e(route('user.create')); ?>">Create User</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-roles')): ?>
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-shield"></i>
                            <span>Role Management</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-roles')): ?>
                                <li><a href="<?php echo e(route('role.index')); ?>">List Roles</a></li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-roles')): ?>
                                <li><a href="<?php echo e(route('role.create')); ?>">Create Role</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-permissions')): ?>
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-lock"></i>
                            <span>Permission Management</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-permissions')): ?>
                                <li><a href="<?php echo e(route('permission.index')); ?>">List Permissions</a></li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-permissions')): ?>
                                <li><a href="<?php echo e(route('permission.create')); ?>">Create Permission</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-devices')): ?>
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-monitor"></i>
                            <span>Devices</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-devices')): ?>
                                <li><a href="<?php echo e(route('device.index')); ?>">List of Devices</a></li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-devices')): ?>
                                <li><a href="<?php echo e(route('device.create')); ?>">Add Device</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-device-groups')): ?>
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-layers"></i>
                            <span>Device Groups</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-device-groups')): ?>
                                <li><a href="<?php echo e(route('devicegroups.index')); ?>">List Groups</a></li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-device-groups')): ?>
                                <li><a href="<?php echo e(route('devicegroups.create')); ?>">Create Group</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-ad-materials')): ?>
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-image"></i>
                            <span>Ad Materials</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-ad-materials')): ?>
                                <li><a href="<?php echo e(route('admaterial.index')); ?>">List Materials</a></li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-ad-materials')): ?>
                                <li><a href="<?php echo e(route('admaterial.create')); ?>">Add Material</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-ad-plans')): ?>
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-calendar"></i>
                            <span>Ad Schedules</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-ad-plans')): ?>
                                <li><a href="<?php echo e(route('adplan.index')); ?>">List Schedules</a></li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-ad-plans')): ?>
                                <li><a href="<?php echo e(route('adplan.create')); ?>">Create Schedule</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-customers')): ?>
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-users"></i>
                            <span>Customers</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-customers')): ?>
                                <li><a href="<?php echo e(route('customer.index')); ?>">List Customers</a></li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-customers')): ?>
                                <li><a href="<?php echo e(route('customer.create')); ?>">Add Customer</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <!-- Powerbanks Menu -->
                <li>
                    <a href="javascript: void(0);">
                        <i class="fe-battery"></i>
                        <span>Powerbanks</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('powerbanks.index')); ?>">All Powerbanks</a></li>
                        <li><a href="<?php echo e(route('powerbanks.create')); ?>">Add New Powerbank</a></li>
                        
                    </ul>
                </li>

                <!-- System Administration Menu -->
                <?php if(auth()->user()->hasPermission('view-user-activity') ||
                        auth()->user()->hasPermission('view-audit-trail') ||
                        auth()->user()->hasPermission('manage-backups')): ?>
                    <li>
                        <a href="javascript: void(0);">
                            <i class="fe-settings"></i>
                            <span>System Administration</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if(auth()->user()->hasPermission('view-user-activity')): ?>
                                <li><a href="<?php echo e(route('admin.user-activity.index')); ?>">User Activity</a></li>
                            <?php endif; ?>
                            <?php if(auth()->user()->hasPermission('view-audit-trail')): ?>
                                <li><a href="<?php echo e(route('admin.audit-trail.index')); ?>">Audit Trail</a></li>
                            <?php endif; ?>
                            <?php if(auth()->user()->hasPermission('manage-backups')): ?>
                                <li><a href="<?php echo e(route('admin.backups.index')); ?>">Database Backups</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <li>
                    <a href="javascript: void(0);">
                        <i class="fe-shield"></i>
                        <span> Security </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('profile.edit')); ?>">Change Password</a></li>
                        <li><a href="<?php echo e(route('two-factor.manage')); ?>">Two-Factor Authentication</a></li>
                    </ul>
                </li>

                

            </ul>

        </div>
        <!-- End Sidebar -->

        <div class="clearfix"></div>

    </div>
    <!-- Sidebar -left -->

</div>
<!-- Left Sidebar End -->
<?php /**PATH C:\laragon\www\websocket-api\resources\views/include/sidebar.blade.php ENDPATH**/ ?>