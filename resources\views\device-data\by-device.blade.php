@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title m-0">Device Data for SN: {{ $sn }}</h4>
                        <div>
                            <a href="{{ route('device-data.index') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-list mr-1"></i> Back to All Data
                            </a>
                        </div>
                    </div>

                    @if(count($deviceData) > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered dt-responsive nowrap">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Command</th>
                                        <th>Message</th>
                                        <th>Status</th>
                                        <th>Created At</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($deviceData as $data)
                                        <tr>
                                            <td>{{ $data->id }}</td>
                                            <td>{{ $data->cmd }}</td>
                                            <td>{{ $data->msg }}</td>
                                            <td>{{ $data->st }}</td>
                                            <td>{{ $data->created_at->format('Y-m-d H:i:s') }}</td>
                                            <td>
                                                <a href="{{ route('device-data.show', $data->id) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <form action="{{ route('device-data.destroy', $data->id) }}" method="POST" style="display: inline-block">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this data?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-3">
                            {{ $deviceData->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-database text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">No Data Available</h5>
                            <p class="text-muted">There is no data recorded for this device yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection