@extends('layouts.master')

@section('title', 'Activity Details')

@section('content')
    <div class="container-fluid">
        <!-- Page Heading -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <h1 class="h3 mb-0 text-gray-800">Activity Details</h1>
            <a href="{{ route('admin.user-activity.index') }}" class="btn btn-sm btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
            </a>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Activity #{{ $activity->id }}</h6>
                        <span
                            class="badge badge-{{ $activity->action == 'login' ? 'success' : ($activity->action == 'logout' ? 'warning' : ($activity->action == 'delete' ? 'danger' : 'info')) }}">
                            {{ ucfirst($activity->action) }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <h5 class="font-weight-bold">Basic Information</h5>
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">User</th>
                                            <td>
                                                @if ($activity->user)
                                                    <a href="{{ route('admin.users.show', $activity->user->id) }}">
                                                        {{ $activity->user->name }}
                                                    </a>
                                                @else
                                                    System
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Module</th>
                                            <td>{{ ucfirst($activity->module) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Action</th>
                                            <td>{{ ucfirst($activity->action) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Description</th>
                                            <td>{{ $activity->description }}</td>
                                        </tr>
                                        <tr>
                                            <th>Date & Time</th>
                                            <td>{{ $activity->created_at->format('Y-m-d H:i:s') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <h5 class="font-weight-bold">Technical Details</h5>
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">IP Address</th>
                                            <td>{{ $activity->ip_address }}</td>
                                        </tr>
                                        <tr>
                                            <th>Browser</th>
                                            <td>{{ $activity->getBrowser() }}</td>
                                        </tr>
                                        <tr>
                                            <th>Platform</th>
                                            <td>{{ $activity->getPlatform() }}</td>
                                        </tr>
                                        <tr>
                                            <th>Device</th>
                                            <td>{{ $activity->getDevice() }}</td>
                                        </tr>
                                        <tr>
                                            <th>Full User Agent</th>
                                            <td>
                                                <div style="max-height: 100px; overflow-y: auto; font-size: 0.8rem;">
                                                    {{ $activity->user_agent }}
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        @if ($activity->properties)
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-4">
                                        <h5 class="font-weight-bold">Additional Properties</h5>
                                        <div class="card">
                                            <div class="card-body">
                                                <pre class="mb-0" style="max-height: 300px; overflow-y: auto;"><code>{{ json_encode(json_decode($activity->properties), JSON_PRETTY_PRINT) }}</code></pre>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if ($activity->action === 'login' || $activity->action === 'logout')
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-4">
                                        <h5 class="font-weight-bold">Related Session Activities</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Action</th>
                                                        <th>Date & Time</th>
                                                        <th>IP Address</th>
                                                        <th>Details</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @php
                                                        $relatedActivities = \App\Models\UserActivity::where(
                                                            'user_id',
                                                            $activity->user_id,
                                                        )
                                                            ->whereIn('action', ['login', 'logout'])
                                                            ->where('id', '!=', $activity->id)
                                                            ->orderBy('created_at', 'desc')
                                                            ->limit(5)
                                                            ->get();
                                                    @endphp

                                                    @forelse($relatedActivities as $related)
                                                        <tr>
                                                            <td>
                                                                <span
                                                                    class="badge badge-{{ $related->action == 'login' ? 'success' : 'warning' }}">
                                                                    {{ ucfirst($related->action) }}
                                                                </span>
                                                            </td>
                                                            <td>{{ $related->created_at->format('Y-m-d H:i:s') }}</td>
                                                            <td>{{ $related->ip_address }}</td>
                                                            <td>
                                                                <a href="{{ route('admin.user-activity.show', $related->id) }}"
                                                                    class="btn btn-sm btn-info">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    @empty
                                                        <tr>
                                                            <td colspan="4" class="text-center">No related activities
                                                                found</td>
                                                        </tr>
                                                    @endforelse
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
