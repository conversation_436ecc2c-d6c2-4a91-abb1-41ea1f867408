@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">Activity Details</h4>
                    <a href="{{ route('admin.user-activity.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">ID</th>
                                    <td>{{ $activity->id }}</td>
                                </tr>
                                <tr>
                                    <th>User</th>
                                    <td>{{ $activity->user ? $activity->user->name : 'System' }}</td>
                                </tr>
                                <tr>
                                    <th>Action</th>
                                    <td>{{ ucfirst($activity->action) }}</td>
                                </tr>
                                <tr>
                                    <th>Module</th>
                                    <td>{{ ucfirst($activity->module) }}</td>
                                </tr>
                                <tr>
                                    <th>Description</th>
                                    <td>{{ $activity->description }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">IP Address</th>
                                    <td>{{ $activity->ip_address }}</td>
                                </tr>
                                <tr>
                                    <th>User Agent</th>
                                    <td>{{ $activity->user_agent }}</td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ $activity->created_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($activity->properties)
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Additional Properties</h5>
                            <div class="card">
                                <div class="card-body">
                                    <pre class="mb-0">{{ json_encode($activity->properties, JSON_PRETTY_PRINT) }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection