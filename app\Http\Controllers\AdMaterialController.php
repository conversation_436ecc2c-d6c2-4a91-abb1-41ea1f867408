<?php

namespace App\Http\Controllers;

use App\Models\AdMaterial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AdMaterialController extends Controller
{
    /**
     * Display a listing of the materials.
     */
    public function index()
    {
        $materials = AdMaterial::latest()->get();
        return view('admaterial.admateriallst', compact('materials'));
    }

    /**
     * Show the form for creating a new material.
     */
    public function create()
    {
        return view('admaterial.admaterialadd');
    }

    /**
     * Store a newly created material in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:image,video',
            'file' => 'required|file|mimes:jpeg,png,jpg,gif,mp4,webm,ogg|max:35840',
            'description' => 'nullable|string',
            'video_duration' => 'nullable|numeric', // Add this to validate client-provided duration
        ]);

        $file = $request->file('file');
        $fileName = time() . '_' . Str::slug($request->name) . '.' . $file->getClientOriginalExtension();

        // Determine storage path based on file type
        $path = $request->type === 'image' ? 'images' : 'videos';
        $file->storeAs("public/{$path}", $fileName);

        $data = [
            'name' => $request->name,
            'type' => $request->type,
            'file_url' => Storage::url("{$path}/{$fileName}"),
            'description' => $request->description,
        ];

        // Extract video duration if it's a video
        if ($request->type === 'video') {
            // First try to use client-provided duration
            if ($request->has('video_duration') && is_numeric($request->video_duration)) {
                $data['duration'] = (float) $request->video_duration;
            } else {
                // Fall back to server-side detection
                $data['duration'] = $this->getVideoDuration(storage_path("app/public/{$path}/{$fileName}"));
            }
        }

        AdMaterial::create($data);

        return redirect()->route('admaterial.index')
            ->with('success', 'Advertisement material created successfully.');
    }

    /**
     * Display the specified material.
     */
    public function show(AdMaterial $admaterial)
    {
        return view('admaterial.admaterialshow', compact('admaterial'));
    }

    /**
     * Show the form for editing the specified material.
     */
    public function edit(AdMaterial $admaterial)
    {
        return view('admaterial.admaterialedt', compact('admaterial'));
    }

    /**
     * Update the specified material in storage.
     */
    public function update(Request $request, AdMaterial $admaterial)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'file' => 'nullable|file|mimes:' . ($admaterial->type === 'image' ? 'jpeg,png,jpg,gif' : 'mp4,webm,ogg') . '|max:250000',
            'description' => 'nullable|string',
        ]);

        $data = [
            'name' => $request->name,
            'description' => $request->description,
        ];

        // Handle file update if provided
        if ($request->hasFile('file')) {
            // Delete old file if it exists and is not a URL
            $oldPath = str_replace(Storage::url(''), '', $admaterial->file_url);
            if (Storage::exists($oldPath)) {
                Storage::delete($oldPath);
            }

            $file = $request->file('file');
            $fileName = time() . '_' . Str::slug($request->name) . '.' . $file->getClientOriginalExtension();

            // Determine storage path based on existing type
            $path = $admaterial->type === 'image' ? 'images' : 'videos';
            $file->storeAs("public/{$path}", $fileName);

            $data['file_url'] = Storage::url("{$path}/{$fileName}");

            // Update video duration if it's a video
            if ($admaterial->type === 'video') {
                $duration = $this->getVideoDuration(storage_path("app/public/{$path}/{$fileName}"));
                $data['duration'] = $duration;
            }
        }

        $admaterial->update($data);

        return redirect()->route('admaterial.index')
            ->with('success', 'Advertisement material updated successfully.');
    }

    /**
     * Remove the specified material from storage.
     */
    public function destroy(AdMaterial $admaterial)
    {
        // Delete file if it exists and is not a URL
        $path = str_replace(Storage::url(''), '', $admaterial->file_url);
        if (Storage::exists($path)) {
            Storage::delete($path);
        }

        $admaterial->delete();

        return redirect()->route('admaterial.index')
            ->with('success', 'Advertisement material deleted successfully.');
    }

    /**
     * Get video duration using multiple fallback methods
     *
     * @param string $videoPath
     * @return float
     */
    private function getVideoDuration($videoPath)
    {
        try {
            // Method 1: Try using getID3 library
            if (!class_exists('getID3')) {
                // If getID3 is not available, try to load it
                $getID3Path = base_path('vendor/james-heinrich/getid3/getid3/getid3.php');
                if (file_exists($getID3Path)) {
                    require_once $getID3Path;
                }
            }

            if (class_exists('getID3')) {
                $getID3 = new \getID3();
                $fileInfo = $getID3->analyze($videoPath);

                if (isset($fileInfo['playtime_seconds'])) {
                    return (float) $fileInfo['playtime_seconds'];
                }
            }

            // Method 2: Use PHP's native functions to analyze the file
            // This is a very basic implementation for MP4 files
            if (function_exists('fopen') && function_exists('fread') && function_exists('fseek')) {
                $file = fopen($videoPath, 'rb');
                if ($file) {
                    // Skip to the 'mvhd' atom which contains duration info
                    // This is a simplified approach and may not work for all MP4 files
                    $found = false;
                    $buffer = '';

                    // Read the file in chunks to find the mvhd atom
                    while (!feof($file) && !$found) {
                        $buffer = fread($file, 4096);
                        $pos = strpos($buffer, 'mvhd');
                        if ($pos !== false) {
                            $found = true;
                            fseek($file, ftell($file) - 4096 + $pos + 16);
                            $timeScale = unpack('N', fread($file, 4))[1];
                            $duration = unpack('N', fread($file, 4))[1];

                            if ($timeScale > 0) {
                                fclose($file);
                                return $duration / $timeScale;
                            }
                        }
                    }
                    fclose($file);
                }
            }

            // Method 3: Estimate based on file size (very rough)
            $fileSize = filesize($videoPath);
            $estimatedDuration = ($fileSize * 8) / (2000 * 1024); // Size in bits / bitrate (2000 kbps)

            return $estimatedDuration > 0 ? $estimatedDuration : 10; // Default to 10 seconds if estimation fails

        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error getting video duration: ' . $e->getMessage());
            return 10; // Default duration in seconds
        }
    }


    /**
     * Show the bulk upload form.
     */
    public function showBulkUpload()
    {
        return view('admaterial.bulk-upload');
    }

    /**
     * Store multiple materials in bulk.
     */
    public function bulkStore(Request $request)
    {
        $request->validate([
            'type' => 'required|in:image,video',
            'files.*' => 'required|file|mimes:' . ($request->type === 'image' ? 'jpeg,png,jpg,gif' : 'mp4,webm,ogg') . '|max:35840',
            'description' => 'nullable|string',
        ]);

        $results = [];
        $files = $request->file('files');

        foreach ($files as $file) {
            try {
                $fileName = time() . '_' . Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $file->getClientOriginalExtension();
                
                // Determine storage path based on file type
                $path = $request->type === 'image' ? 'images' : 'videos';
                $file->storeAs("public/{$path}", $fileName);

                $data = [
                    'name' => pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME),
                    'type' => $request->type,
                    'file_url' => Storage::url("{$path}/{$fileName}"),
                    'description' => $request->description,
                ];

                // Extract video duration if it's a video
                if ($request->type === 'video') {
                    $data['duration'] = $this->getVideoDuration(storage_path("app/public/{$path}/{$fileName}"));
                }

                AdMaterial::create($data);

                $results[] = [
                    'success' => true,
                    'message' => 'File uploaded successfully'
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'success' => false,
                    'message' => $e->getMessage()
                ];
            }
        }

        return response()->json($results);
    }


}
