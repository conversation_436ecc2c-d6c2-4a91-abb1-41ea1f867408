<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Http\Controllers\RoleController;

class PermissionGroupingTest extends TestCase
{
    private function createMockPermission($slug, $name)
    {
        $permission = new \stdClass();
        $permission->slug = $slug;
        $permission->name = $name;
        $permission->description = "Description for {$name}";
        return $permission;
    }

    /** @test */
    public function permission_grouping_organizes_permissions_correctly()
    {
        // Create mock permission objects
        $permissions = collect([
            $this->createMockPermission('view-dashboard', 'View Dashboard'),
            $this->createMockPermission('view-users', 'View Users'),
            $this->createMockPermission('create-users', 'Create Users'),
            $this->createMockPermission('view-devices', 'View Devices'),
            $this->createMockPermission('control-devices', 'Control Devices'),
            $this->createMockPermission('view-customers', 'View Customers'),
            $this->createMockPermission('view-powerbanks', 'View Powerbanks'),
            $this->createMockPermission('rent-powerbanks', 'Rent Powerbanks'),
            $this->createMockPermission('view-user-activity', 'View User Activity'),
            $this->createMockPermission('manage-backups', 'Manage Backups'),
            $this->createMockPermission('view-roles', 'View Roles'),
            $this->createMockPermission('view-ad-materials', 'View Ad Materials'),
        ]);

        $controller = new RoleController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('groupPermissions');
        $method->setAccessible(true);

        $groupedPermissions = $method->invoke($controller, $permissions);

        // Check that permissions are grouped correctly
        $this->assertArrayHasKey('Dashboard', $groupedPermissions);
        $this->assertArrayHasKey('User Management', $groupedPermissions);
        $this->assertArrayHasKey('Device Management', $groupedPermissions);
        $this->assertArrayHasKey('Customer Management', $groupedPermissions);
        $this->assertArrayHasKey('Powerbank Management', $groupedPermissions);
        $this->assertArrayHasKey('System Administration', $groupedPermissions);
        $this->assertArrayHasKey('Role & Permission Management', $groupedPermissions);
        $this->assertArrayHasKey('Ad Management', $groupedPermissions);

        // Check specific permission assignments
        $dashboardPermissions = collect($groupedPermissions['Dashboard']['permissions'])->pluck('slug');
        $this->assertTrue($dashboardPermissions->contains('view-dashboard'));

        $userPermissions = collect($groupedPermissions['User Management']['permissions'])->pluck('slug');
        $this->assertTrue($userPermissions->contains('view-users'));
        $this->assertTrue($userPermissions->contains('create-users'));

        $devicePermissions = collect($groupedPermissions['Device Management']['permissions'])->pluck('slug');
        $this->assertTrue($devicePermissions->contains('view-devices'));
        $this->assertTrue($devicePermissions->contains('control-devices'));

        $systemPermissions = collect($groupedPermissions['System Administration']['permissions'])->pluck('slug');
        $this->assertTrue($systemPermissions->contains('view-user-activity'));
        $this->assertTrue($systemPermissions->contains('manage-backups'));
    }

    /** @test */
    public function permission_groups_have_required_metadata()
    {
        $permissions = collect([
            $this->createMockPermission('view-dashboard', 'View Dashboard'),
        ]);

        $controller = new RoleController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('groupPermissions');
        $method->setAccessible(true);

        $groupedPermissions = $method->invoke($controller, $permissions);

        foreach ($groupedPermissions as $group) {
            $this->assertArrayHasKey('icon', $group);
            $this->assertArrayHasKey('description', $group);
            $this->assertArrayHasKey('permissions', $group);

            $this->assertNotEmpty($group['icon']);
            $this->assertNotEmpty($group['description']);
            $this->assertIsArray($group['permissions']);
        }
    }

    /** @test */
    public function empty_groups_are_filtered_out()
    {
        // Create permissions that don't match any specific group patterns
        $permissions = collect([
            $this->createMockPermission('some-random-permission', 'Random Permission'),
        ]);

        $controller = new RoleController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('groupPermissions');
        $method->setAccessible(true);

        $groupedPermissions = $method->invoke($controller, $permissions);

        // Should have at least one group (fallback works)
        $this->assertGreaterThanOrEqual(1, count($groupedPermissions));

        // All permissions should be assigned somewhere
        $totalGroupedPermissions = 0;
        foreach ($groupedPermissions as $group) {
            $totalGroupedPermissions += count($group['permissions']);
        }
        $this->assertEquals($permissions->count(), $totalGroupedPermissions);
    }

    /** @test */
    public function all_permissions_are_assigned_to_groups()
    {
        $permissions = collect([
            $this->createMockPermission('view-dashboard', 'View Dashboard'),
            $this->createMockPermission('view-users', 'View Users'),
            $this->createMockPermission('unknown-permission', 'Unknown Permission'),
        ]);

        $controller = new RoleController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('groupPermissions');
        $method->setAccessible(true);

        $groupedPermissions = $method->invoke($controller, $permissions);

        // Count total permissions in groups
        $totalGroupedPermissions = 0;
        foreach ($groupedPermissions as $group) {
            $totalGroupedPermissions += count($group['permissions']);
        }

        // Should equal original permission count
        $this->assertEquals($permissions->count(), $totalGroupedPermissions);
    }

    /** @test */
    public function permission_groups_have_correct_icons()
    {
        $permissions = collect([
            $this->createMockPermission('view-dashboard', 'View Dashboard'),
            $this->createMockPermission('view-users', 'View Users'),
            $this->createMockPermission('view-devices', 'View Devices'),
        ]);

        $controller = new RoleController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('groupPermissions');
        $method->setAccessible(true);

        $groupedPermissions = $method->invoke($controller, $permissions);

        // Check that groups have appropriate icons
        if (isset($groupedPermissions['Dashboard'])) {
            $this->assertEquals('fe-home', $groupedPermissions['Dashboard']['icon']);
        }

        if (isset($groupedPermissions['User Management'])) {
            $this->assertEquals('fe-users', $groupedPermissions['User Management']['icon']);
        }

        if (isset($groupedPermissions['Device Management'])) {
            $this->assertEquals('fe-monitor', $groupedPermissions['Device Management']['icon']);
        }
    }
}
