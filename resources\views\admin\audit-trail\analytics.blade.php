@extends('layouts.master')

@section('title', 'Audit Trail Analytics')

@section('content')
    <div class="container-fluid">
        <!-- Page Heading -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <h1 class="h3 mb-0 text-gray-800">Audit Trail Analytics</h1>
            <a href="{{ route('admin.audit-trail.index') }}" class="btn btn-sm btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Audit Trail
            </a>
        </div>

        <!-- Content Row -->
        <div class="row">
            <!-- Activity Over Time Chart -->
            <div class="col-xl-8 col-lg-7">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Activity Over Time (Last 30 Days)</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-area">
                            <canvas id="activityOverTimeChart" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity by Severity Chart -->
            <div class="col-xl-4 col-lg-5">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Activity by Severity</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-pie">
                            <canvas id="activityBySeverityChart" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Row -->
        <div class="row">
            <!-- Activity by Action Chart -->
            <div class="col-xl-6 col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Top 10 Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-bar">
                            <canvas id="activityByActionChart" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity by Module Chart -->
            <div class="col-xl-6 col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Top 10 Modules</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-pie">
                            <canvas id="activityByModuleChart" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Row -->
        <div class="row">
            <!-- Top Users -->
            <div class="col-xl-6 col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Top 10 Users by Activity</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Activity Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($topUsers as $user)
                                        <tr>
                                            <td>{{ $user->user ? $user->user->name : 'Unknown' }}</td>
                                            <td>{{ $user->count }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="2" class="text-center">No data available</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent High Severity Events -->
            <div class="col-xl-6 col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Recent High Severity Events</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Date/Time</th>
                                        <th>User</th>
                                        <th>Action</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($highSeverityEvents as $event)
                                        <tr>
                                            <td>{{ $event->created_at->format('Y-m-d H:i') }}</td>
                                            <td>{{ $event->user ? $event->user->name : 'System' }}</td>
                                            <td>{!! $event->action_badge !!}</td>
                                            <td>{{ Str::limit($event->notes, 50) }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="4" class="text-center">No high severity events found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Activity Over Time Chart
            const timeLabels = @json($activityOverTime->pluck('date'));
            const timeData = @json($activityOverTime->pluck('count'));

            new Chart(document.getElementById('activityOverTimeChart'), {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: [{
                        label: 'Activity Count',
                        data: timeData,
                        borderColor: '#4e73df',
                        backgroundColor: 'rgba(78, 115, 223, 0.05)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // Activity by Severity Chart
            const severityLabels = @json($activityBySeverity->pluck('severity'));
            const severityData = @json($activityBySeverity->pluck('count'));
            const severityColors = {
                'low': '#36b9cc',
                'medium': '#f6c23e',
                'high': '#e74a3b'
            };

            new Chart(document.getElementById('activityBySeverityChart'), {
                type: 'doughnut',
                data: {
                    labels: severityLabels.map(label => label.charAt(0).toUpperCase() + label.slice(1)),
                    datasets: [{
                        data: severityData,
                        backgroundColor: severityLabels.map(label => severityColors[label] ||
                            '#858796'),
                        hoverBackgroundColor: severityLabels.map(label => severityColors[label] ||
                            '#858796'),
                        hoverBorderColor: "rgba(234, 236, 244, 1)",
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    cutout: '70%'
                }
            });

            // Activity by Action Chart
            const actionLabels = @json($activityByAction->pluck('action'));
            const actionData = @json($activityByAction->pluck('count'));
            const actionColors = [
                '#4e73df', '#1cc88a', '#e74a3b', '#f6c23e', '#36b9cc',
                '#6f42c1', '#fd7e14', '#20c9a6', '#5a5c69', '#858796'
            ];

            new Chart(document.getElementById('activityByActionChart'), {
                type: 'bar',
                data: {
                    labels: actionLabels.map(label => label.charAt(0).toUpperCase() + label.slice(1)),
                    datasets: [{
                        label: 'Count',
                        data: actionData,
                        backgroundColor: actionLabels.map((_, i) => actionColors[i % actionColors
                            .length]),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // Activity by Module Chart
            const moduleLabels = @json($activityByModule->pluck('module'));
            const moduleData = @json($activityByModule->pluck('count'));

            new Chart(document.getElementById('activityByModuleChart'), {
                type: 'pie',
                data: {
                    labels: moduleLabels.map(label => label ? (label.charAt(0).toUpperCase() + label.slice(
                        1)) : 'Other'),
                    datasets: [{
                        data: moduleData,
                        backgroundColor: moduleLabels.map((_, i) => actionColors[i % actionColors
                            .length]),
                        hoverBackgroundColor: moduleLabels.map((_, i) => actionColors[i %
                            actionColors.length]),
                        hoverBorderColor: "rgba(234, 236, 244, 1)",
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        });
    </script>
@endsection
