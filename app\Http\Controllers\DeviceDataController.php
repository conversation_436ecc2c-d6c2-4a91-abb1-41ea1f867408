<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DeviceData;
use Illuminate\Support\Facades\Log;

class DeviceDataController extends Controller
{
    /**
     * Display a listing of device data.
     */
    public function index()
    {
        $deviceData = DeviceData::latest()->paginate(15);
        return view('device-data.index', compact('deviceData'));
    }

    /**
     * Store device data from API request.
     */
    public function store(Request $request)
    {
        $rawData = $request->getContent(); // Get raw JSON string
        $jsonString = trim($rawData, "#*"); // Remove #* and *#
        $data = json_decode($jsonString, true);

        if (!$data) {
            return response()->json(['error' => 'Invalid JSON'], 400);
        }

        // Store data in the database
        $deviceData = DeviceData::create([
            'msg'  => $data['msg'],
            'sn'   => $data['sn'],
            'cmd'  => $data['cmd'],
            'aims' => $data['aims'] ?? 0,
            'n'    => $data['data']['n'] ?? null,
            'r'    => $data['data']['r'] ?? null,
            'data' => $data['data']['d'] ?? [], // Store full "d" array
            'st'   => $data['data']['st'] ?? null,
        ]);

        // Process powerbank details if this is a detailup command
        if ($data['cmd'] === 'detailup') {
            $this->processPowerbankDetails($data);
        }

        return response()->json(['message' => 'Data saved', 'id' => $deviceData->id], 201);
    }

    /**
     * Display the specified device data.
     */
    public function show(string $id)
    {
        $deviceData = DeviceData::findOrFail($id);
        return view('device-data.show', compact('deviceData'));
    }

    /**
     * Get device data for a specific device by serial number.
     */
    public function getByDeviceSn(string $sn)
    {
        $deviceData = DeviceData::where('sn', $sn)
            ->latest()
            ->paginate(15);

        return view('device-data.by-device', compact('deviceData', 'sn'));
    }

    /**
     * Get device data as JSON for API requests.
     */
    public function apiGetByDeviceSn(string $sn)
    {
        $deviceData = DeviceData::where('sn', $sn)
            ->latest()
            ->paginate(15);

        return response()->json($deviceData);
    }

    /**
     * Delete the specified device data.
     */
    public function destroy(string $id)
    {
        $deviceData = DeviceData::findOrFail($id);
        $deviceData->delete();

        return redirect()->route('device-data.index')
            ->with('success', 'Device data deleted successfully');
    }

    /**
     * Process powerbank details from detailup command
     */
    public function processPowerbankDetails($data)
    {
        // Extract device serial number
        $deviceSn = $data['sn'] ?? null;

        // Get the AIMS value (default to 0 if not provided)
        $aims = $data['aims'] ?? 0;

        if (!$deviceSn) {
            return response()->json(['error' => 'No device serial number provided'], 400);
        }

        // Get the device from the database
        $device = \App\Models\Device::where('sn', $deviceSn)->first();

        if (!$device) {
            return response()->json(['error' => 'Device not found'], 404);
        }

        // Get the slot details from the request
        $slots = $data['data']['d'] ?? [];

        if (empty($slots)) {
            return response()->json(['error' => 'No slot data found'], 400);
        }

        $updatedPowerbanks = [];

        // Process each slot
        foreach ($slots as $slot) {
            $slotNumber = $slot['n'] ?? null;
            $status = $slot['s'] ?? 0;
            $serialNumber = $slot['sn'] ?? null;
            $errorCode = $slot['e'] ?? 0;

            // Skip if no slot number or serial number
            if (!$slotNumber || !$serialNumber) {
                continue;
            }

            // Ensure AIMS is within valid range (0-4)
            $validAims = max(0, min(4, $aims));

            // Calculate the actual slot number based on AIMS
            // AIMS 0 = slots 1-12, AIMS 1 = slots 13-24, AIMS 2 = slots 25-36, etc.
            $actualSlotNumber = $slotNumber + ($validAims * 12);

            // Check if powerbank exists
            $powerbank = \App\Models\Powerbank::where('serial_number', $serialNumber)->first();

            if ($powerbank) {
                // Update existing powerbank
                $powerbank->update([
                    'device_id' => $device->id,
                    'slot_number' => $actualSlotNumber,
                    'status' => $this->mapStatusCode($status, $errorCode),
                    'aims' => $validAims, // Store the validated AIMS value
                ]);

                $updatedPowerbanks[] = $powerbank->fresh();
            } else {
                // Create new powerbank
                $powerbank = \App\Models\Powerbank::create([
                    'serial_number' => $serialNumber,
                    'device_id' => $device->id,
                    'capacity' => 5000, // Default capacity
                    'current_charge' => 100, // Default charge
                    'slot_number' => $actualSlotNumber,
                    'status' => $this->mapStatusCode($status, $errorCode),
                    'charge_cycles' => 0,
                    'aims' => $validAims, // Store the validated AIMS value
                ]);

                $updatedPowerbanks[] = $powerbank;
            }
        }

        return $updatedPowerbanks;
    }

    /**
     * Map status code to powerbank status
     */
    private function mapStatusCode($status, $errorCode)
    {
        // If status is 0, the slot is empty or powerbank is not available
        if ($status == 0) {
            return 'maintenance';
        }

        // If error code is 9, it's charging
        if ($errorCode == 9) {
            return 'charging';
        }

        // If error code is not 0 and not 9, there's an issue
        if ($errorCode != 0 && $errorCode != 9) {
            return 'maintenance';
        }

        // Otherwise, it's available
        return 'available';
    }
}
