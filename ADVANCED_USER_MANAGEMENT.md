# Advanced User Management System

## Overview

This system provides comprehensive user management with role-based permissions for all menu options in the WebSocket API application.

## Features

### 🔐 **Role-Based Access Control (RBAC)**
- **5 Predefined Roles** with hierarchical permissions
- **79 Granular Permissions** covering all system features
- **Dynamic Permission Checking** with middleware protection

### 👥 **Advanced User Management**
- **User Status Management** (Active/Inactive)
- **Password Reset Functionality**
- **User Activity Tracking**
- **Bulk User Operations**
- **Advanced Search & Filtering**
- **Data Export Capabilities**

### 📊 **Comprehensive Permissions**
- **Dashboard Access**
- **User & Role Management**
- **Device & Device Group Control**
- **Powerbank Operations**
- **Customer Management**
- **Rental Management**
- **Ad Material & Plan Management**
- **System Administration**
- **Audit Trail & Activity Logs**
- **Database Backup Management**

## Roles & Permissions

### **Super Administrator**
- **All 79 permissions**
- Complete system access
- Can manage system settings
- Can delete users, roles, and permissions

### **Administrator**
- **75 permissions** (excludes super admin only features)
- Full operational access
- Cannot delete core system components
- Cannot manage system settings

### **Manager**
- **Operational permissions** for daily management
- User, device, powerbank, customer management
- Cannot delete critical data
- Limited to operational features

### **Operator**
- **Daily operation permissions**
- Device and powerbank operations
- Customer service functions
- Read-only access to most data

### **Viewer**
- **Read-only permissions**
- Can view most system data
- Cannot modify any data
- Perfect for reporting and monitoring

## Permission Categories

### **User Management (9 permissions)**
- `view-users`, `create-users`, `edit-users`, `delete-users`
- `assign-roles`, `manage-user-status`, `reset-user-password`
- `view-user-profile`

### **Device Management (7 permissions)**
- `view-devices`, `create-devices`, `edit-devices`, `delete-devices`
- `control-devices`, `view-device-data`, `delete-device-data`

### **Powerbank Management (10 permissions)**
- `view-powerbanks`, `create-powerbanks`, `edit-powerbanks`, `delete-powerbanks`
- `rent-powerbanks`, `return-powerbanks`, `mark-powerbanks-charged`
- `report-powerbank-issues`, `resolve-powerbank-issues`, `view-powerbank-issues`

### **Customer Management (7 permissions)**
- `view-customers`, `create-customers`, `edit-customers`, `delete-customers`
- `view-customer-registrations`, `view-customer-reports`, `send-customer-reminders`

### **System Administration (11 permissions)**
- `view-user-activity`, `view-audit-trail`
- `manage-backups`, `download-backups`, `delete-backups`
- `send-device-commands`, `eject-powerbanks`, `reboot-devices`
- `view-reports`, `export-data`, `manage-system-settings`

## Usage

### **Default Super Admin Account**
```
Email: <EMAIL>
Password: password
```

### **Creating Users with Roles**
```php
$user = User::create([
    'name' => 'John Manager',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'status' => 'active'
]);

$managerRole = Role::where('slug', 'manager')->first();
$user->roles()->attach($managerRole);
```

### **Checking Permissions**
```php
// In controllers
$this->authorize('view-users');

// In middleware
Route::get('/users')->middleware('permission:view-users');

// In models/views
if (auth()->user()->hasPermission('edit-users')) {
    // Show edit button
}
```

### **Route Protection**
All routes are protected with appropriate permissions:
```php
Route::get('/users', [UserController::class, 'index'])
    ->middleware('permission:view-users');
```

## Advanced Features

### **User Status Management**
- Toggle user active/inactive status
- Prevent inactive users from logging in
- Track status changes in audit trail

### **Password Management**
- Admin can reset user passwords
- Track password change dates
- Force password changes

### **Activity Tracking**
- All user actions logged
- Detailed audit trail
- IP address and user agent tracking

### **Bulk Operations**
- Activate/deactivate multiple users
- Assign roles to multiple users
- Delete multiple users (with safety checks)

### **Data Export**
- Export user data to CSV
- Apply same filters as user list
- Include role and activity information

## Security Features

### **Self-Protection**
- Users cannot delete their own accounts
- Users cannot deactivate themselves
- Super admin role cannot be deleted

### **Permission Inheritance**
- Roles inherit permissions through relationships
- Dynamic permission checking
- Cached permission lookups

### **Audit Trail**
- All user management actions logged
- IP address and timestamp tracking
- Detailed change history

## Database Schema

### **New User Fields**
- `status` (enum: active/inactive)
- `last_login_at` (timestamp)
- `password_changed_at` (timestamp)

### **Permission System**
- `permissions` table with name, slug, description
- `roles` table with hierarchical structure
- `role_user` and `permission_role` pivot tables

## API Endpoints

### **User Management**
- `GET /users` - List users with filters
- `POST /users` - Create user
- `GET /users/{user}` - View user details
- `PUT /users/{user}` - Update user
- `DELETE /users/{user}` - Delete user
- `PATCH /users/{user}/toggle-status` - Toggle status
- `POST /users/{user}/reset-password` - Reset password
- `GET /users/{user}/activity` - View user activity
- `POST /users/bulk-action` - Bulk operations
- `GET /users/export` - Export data

## Installation & Setup

1. **Run Migrations**
   ```bash
   php artisan migrate
   ```

2. **Seed Permissions & Roles**
   ```bash
   php artisan db:seed --class=AdvancedPermissionsSeeder
   ```

3. **Login with Super Admin**
   - Email: <EMAIL>
   - Password: password

4. **Create Additional Users**
   - Use the user management interface
   - Assign appropriate roles
   - Configure permissions as needed

## Customization

### **Adding New Permissions**
1. Add to `AdvancedPermissionsSeeder`
2. Run the seeder
3. Assign to appropriate roles
4. Protect routes with middleware

### **Creating Custom Roles**
1. Create role in database
2. Assign permissions
3. Update role assignments

### **Extending User Model**
- Add new fields to migration
- Update fillable array
- Add appropriate casts

This advanced user management system provides enterprise-level security and flexibility for managing users and permissions across all system features.
