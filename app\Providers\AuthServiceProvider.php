<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use App\Models\User;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Define gates for user management
        Gate::define('view-users', function (User $user) {
            return $user->hasPermission('view-users');
        });

        Gate::define('create-users', function (User $user) {
            return $user->hasPermission('create-users');
        });

        Gate::define('edit-users', function (User $user) {
            return $user->hasPermission('edit-users');
        });

        Gate::define('delete-users', function (User $user) {
            return $user->hasPermission('delete-users');
        });

        Gate::define('assign-roles', function (User $user) {
            return $user->hasPermission('assign-roles');
        });

        // Define gates for role management
        Gate::define('view-roles', function (User $user) {
            return $user->hasPermission('view-roles');
        });

        Gate::define('create-roles', function (User $user) {
            return $user->hasPermission('create-roles');
        });

        Gate::define('edit-roles', function (User $user) {
            return $user->hasPermission('edit-roles');
        });

        Gate::define('delete-roles', function (User $user) {
            return $user->hasPermission('delete-roles');
        });

        // Define gates for permission management
        Gate::define('view-permissions', function (User $user) {
            return $user->hasPermission('view-permissions');
        });

        Gate::define('create-permissions', function (User $user) {
            return $user->hasPermission('create-permissions');
        });

        Gate::define('edit-permissions', function (User $user) {
            return $user->hasPermission('edit-permissions');
        });

        Gate::define('delete-permissions', function (User $user) {
            return $user->hasPermission('delete-permissions');
        });

        // Define gates for device management
        Gate::define('view-devices', function (User $user) {
            return $user->hasPermission('view-devices');
        });

        Gate::define('create-devices', function (User $user) {
            return $user->hasPermission('create-devices');
        });

        Gate::define('edit-devices', function (User $user) {
            return $user->hasPermission('edit-devices');
        });

        Gate::define('delete-devices', function (User $user) {
            return $user->hasPermission('delete-devices');
        });

        Gate::define('control-devices', function (User $user) {
            return $user->hasPermission('control-devices');
        });

        // Define gates for ad material management
        Gate::define('view-ad-materials', function (User $user) {
            return $user->hasPermission('view-ad-materials');
        });

        Gate::define('create-ad-materials', function (User $user) {
            return $user->hasPermission('create-ad-materials');
        });

        Gate::define('edit-ad-materials', function (User $user) {
            return $user->hasPermission('edit-ad-materials');
        });

        Gate::define('delete-ad-materials', function (User $user) {
            return $user->hasPermission('delete-ad-materials');
        });

        // Define gates for ad plan management
        Gate::define('view-ad-plans', function (User $user) {
            return $user->hasPermission('view-ad-plans');
        });

        Gate::define('create-ad-plans', function (User $user) {
            return $user->hasPermission('create-ad-plans');
        });

        Gate::define('edit-ad-plans', function (User $user) {
            return $user->hasPermission('edit-ad-plans');
        });

        Gate::define('delete-ad-plans', function (User $user) {
            return $user->hasPermission('delete-ad-plans');
        });
    }
}
