<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DeviceGroup;
use Illuminate\Http\Request;

class DeviceGroupController extends Controller
{
    /**
     * Display a listing of device groups.
     */
    public function index()
    {
        $groups = DeviceGroup::withCount('devices')->get();

        return response()->json([
            'status' => 1,
            'data' => $groups
        ]);
    }

    /**
     * Display the specified device group.
     */
    public function show(DeviceGroup $devicegroup)
    {
        $devicegroup->load(['devices', 'adPlans']);

        return response()->json([
            'status' => 1,
            'data' => $devicegroup
        ]);
    }

    /**
     * Store a newly created device group.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'devices' => 'array',
            'devices.*' => 'exists:devices,id',
        ]);

        $group = DeviceGroup::create([
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => $request->is_active ?? true,
        ]);

        if ($request->has('devices')) {
            $group->devices()->attach($request->devices);
        }

        return response()->json([
            'status' => 1,
            'message' => 'Device group created successfully',
            'data' => $group->load('devices')
        ], 201);
    }

    /**
     * Update the specified device group.
     */
    public function update(Request $request, DeviceGroup $devicegroup)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $devicegroup->update([
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => $request->is_active ?? $devicegroup->is_active,
        ]);

        return response()->json([
            'status' => 1,
            'message' => 'Device group updated successfully',
            'data' => $devicegroup
        ]);
    }

    /**
     * Remove the specified device group.
     */
    public function destroy(DeviceGroup $devicegroup)
    {
        $devicegroup->delete();

        return response()->json([
            'status' => 1,
            'message' => 'Device group deleted successfully'
        ]);
    }

    /**
     * Assign devices to a device group.
     */
    public function assignDevices(Request $request, DeviceGroup $devicegroup)
    {
        $request->validate([
            'device_ids' => 'required|array',
            'device_ids.*' => 'exists:devices,id',
        ]);

        // Sync the devices with the device group
        $devicegroup->devices()->sync($request->device_ids);

        return response()->json([
            'status' => 1,
            'message' => 'Devices assigned successfully',
            'device_group' => $devicegroup->load('devices')
        ]);
    }

    /**
     * Assign ad plans to a device group via API.
     */
    public function assignPlans(Request $request, DeviceGroup $devicegroup)
    {
        $request->validate([
            'plan_ids' => 'required|array',
            'plan_ids.*' => 'exists:ad_plans,id',
        ]);

        // Sync the plans with the device group
        $devicegroup->adPlans()->sync($request->plan_ids);

        return response()->json([
            'status' => 1,
            'message' => 'Ad plans assigned successfully',
            'device_group' => $devicegroup->load('adPlans')
        ]);
    }
}
