<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Crypt;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'two_factor_enabled' => 'boolean',
    ];

    /**
     * Get the two factor secret.
     *
     * @return string|null
     */
    public function getTwoFactorSecretAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    /**
     * Set the two factor secret.
     *
     * @param  string  $value
     * @return void
     */
    public function setTwoFactorSecretAttribute($value)
    {
        $this->attributes['two_factor_secret'] = $value ? Crypt::encrypt($value) : null;
    }

    /**
     * Get the two factor recovery codes.
     *
     * @return array|null
     */
    public function getTwoFactorRecoveryCodesAttribute($value)
    {
        return $value ? json_decode(decrypt($value)) : null;
    }

    /**
     * Set the two factor recovery codes.
     *
     * @param  array  $value
     * @return void
     */
    public function setTwoFactorRecoveryCodesAttribute($value)
    {
        $this->attributes['two_factor_recovery_codes'] = $value ? encrypt(json_encode($value)) : null;
    }

    /**
     * Generate recovery codes for the user.
     *
     * @return array
     */
    public function generateRecoveryCodes()
    {
        $recoveryCodes = [];

        for ($i = 0; $i < 8; $i++) {
            $recoveryCodes[] = $this->generateRecoveryCode();
        }

        $this->two_factor_recovery_codes = $recoveryCodes;
        $this->save();

        return $recoveryCodes;
    }

    /**
     * Generate a single recovery code.
     *
     * @return string
     */
    private function generateRecoveryCode()
    {
        return substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 10);
    }

    /**
     * The roles that belong to the user.
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    /**
     * Check if the user has a specific role.
     *
     * @param string|array $role
     * @return bool
     */
    public function hasRole($role)
    {
        if (is_string($role)) {
            return $this->roles->contains('slug', $role);
        }

        return !! $role->intersect($this->roles)->count();
    }

    /**
     * Check if the user has any of the given permissions.
     *
     * @param string|array $permissions
     * @return bool
     */
    public function hasPermission($permissions)
    {
        $userPermissions = $this->permissions();

        if (is_string($permissions)) {
            return $userPermissions->contains('slug', $permissions);
        }

        foreach ($permissions as $permission) {
            if ($userPermissions->contains('slug', $permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get all permissions for the user through roles.
     *
     * @return \Illuminate\Support\Collection
     */
    public function permissions()
    {
        return $this->roles->flatMap(function ($role) {
            return $role->permissions;
        })->unique('id');
    }
}
