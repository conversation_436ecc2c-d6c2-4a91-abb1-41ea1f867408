<?php

namespace Database\Seeders;

use App\Models\Device;
use App\Models\DeviceData;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DashboardDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create device status history for the past 14 days
        $this->createDeviceStatusHistory();

        // Create recent heartbeats
        $this->createRecentHeartbeats();
    }

    /**
     * Create device status history for chart
     */
    private function createDeviceStatusHistory(): void
    {
        // Clear existing data
        DB::table('device_status_logs')->truncate();

        // Get total device count or use a default
        $totalDevices = Device::count() ?: 20;

        // Generate data for the past 14 days
        for ($i = 14; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');

            // Calculate random online/offline counts
            $onlineCount = rand(intval($totalDevices * 0.6), $totalDevices);
            $offlineCount = $totalDevices - $onlineCount;

            // Insert log entry
            DB::table('device_status_logs')->insert([
                'date' => $date,
                'device_id' => 1, // Assuming device_id 1 exists
                'status' => 1, // Assuming status 1 is online
                'online_count' => $onlineCount,
                'offline_count' => $offlineCount,
                'created_at' => now()->subDays($i),
                'updated_at' => now()->subDays($i),
            ]);
        }
    }

    /**
     * Create recent heartbeats for devices
     */
    private function createRecentHeartbeats(): void
    {
        // Get all devices or create some if none exist
        $devices = Device::all();

        if ($devices->count() === 0) {
            // Create 10 sample devices
            for ($i = 1; $i <= 10; $i++) {
                Device::create([
                    'sn' => 'DEV' . str_pad($i, 6, '0', STR_PAD_LEFT),
                    'model' => fake()->randomElement(['Model A', 'Model B', 'Model C']),
                    'status' => fake()->boolean(80), // 80% online
                    'last_heartbeat_at' => now()->subMinutes(rand(1, 60)),
                ]);
            }

            $devices = Device::all();
        }

        // Update last_heartbeat_at for devices
        foreach ($devices as $device) {
            $device->update([
                'last_heartbeat_at' => now()->subMinutes(rand(1, 120)),
                'status' => fake()->boolean(80), // 80% online
            ]);
        }
    }
}
