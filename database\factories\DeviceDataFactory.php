<?php

namespace Database\Factories;

use App\Models\Device;
use App\Models\DeviceData;
use Illuminate\Database\Eloquent\Factories\Factory;

class DeviceDataFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DeviceData::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Get a random device serial number or create a fallback
        $device = Device::inRandomOrder()->first();
        $sn = $device ? $device->serial_number : 'DEV' . fake()->unique()->numerify('######');
        
        // Define possible commands
        $commands = ['heartbeat', 'status', 'config', 'update', 'reboot', 'log'];
        $cmd = fake()->randomElement($commands);
        
        // Create appropriate message based on command
        $msg = match($cmd) {
            'heartbeat' => 'Device check-in',
            'status' => 'Status report',
            'config' => 'Configuration update',
            'update' => 'Software update',
            'reboot' => 'Device reboot',
            'log' => 'Log upload',
            default => 'General message'
        };
        
        // Generate random status
        $status = fake()->randomElement(['success', 'error', 'pending', 'warning']);
        
        // Create sample data JSON
        $data = [
            'timestamp' => fake()->unixTime(),
            'uptime' => fake()->numberBetween(1, 8640000), // Up to 100 days in seconds
            'memory' => [
                'total' => 1024 * fake()->numberBetween(1, 8),
                'used' => fake()->numberBetween(100, 800),
                'free' => fake()->numberBetween(100, 800),
            ],
            'cpu' => [
                'load' => fake()->randomFloat(2, 0, 100),
                'temperature' => fake()->randomFloat(1, 30, 80),
            ],
            'network' => [
                'ip' => fake()->ipv4(),
                'mac' => fake()->macAddress(),
                'connected' => fake()->boolean(90),
                'signal_strength' => fake()->numberBetween(-100, -30),
            ],
            'storage' => [
                'total' => 1024 * fake()->numberBetween(8, 64),
                'used' => fake()->numberBetween(1000, 8000),
                'free' => fake()->numberBetween(1000, 8000),
            ],
            'version' => 'v' . fake()->numerify('#.#.#'),
        ];
        
        return [
            'sn' => $sn,
            'cmd' => $cmd,
            'msg' => $msg,
            'st' => $status,
            'n' => fake()->randomNumber(5),
            'r' => fake()->randomNumber(5),
            'aims' => fake()->uuid(),
            'data' => $data,
            'created_at' => fake()->dateTimeBetween('-3 months', 'now'),
            'updated_at' => fake()->dateTimeBetween('-3 months', 'now'),
        ];
    }
    
    /**
     * Indicate the device data is a heartbeat.
     */
    public function heartbeat(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'cmd' => 'heartbeat',
                'msg' => 'Device check-in',
                'st' => 'success',
            ];
        });
    }
    
    /**
     * Indicate the device data has an error status.
     */
    public function error(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'st' => 'error',
                'msg' => fake()->randomElement([
                    'Connection failed',
                    'Authentication error',
                    'Timeout error',
                    'Hardware failure',
                    'Memory error',
                ]),
            ];
        });
    }
}