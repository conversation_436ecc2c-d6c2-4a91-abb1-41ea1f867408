<?php

namespace App\Http\Controllers;

use App\Models\Device;
use App\Models\Powerbank;
use App\Models\DeviceCommand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DeviceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('view-devices');
        $devices = Device::all();
        return view('device.devicelst', compact('devices'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create-devices');
        return view('device.deviceadd');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create-devices');
        $device = Device::create($request->all());
        return redirect()->route('device.index')->with('success', 'Device created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $this->authorize('view-devices');
        $device = Device::with(['powerbanks'])->findOrFail($id);

        // Group powerbanks by AIMS value
        $aimsGroups = $device->powerbanks->groupBy('aims');

        // If there are no powerbanks, create an empty default group
        if ($aimsGroups->isEmpty()) {
            $aimsGroups = collect([0 => collect([])]);
        }

        return view('device.deviceshow', compact('device', 'aimsGroups'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $this->authorize('edit-devices');
        return view('device.deviceedit', ['device' => Device::find($id)]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $this->authorize('edit-devices');
        $device = Device::find($id);
        $device->update($request->all());
        return redirect()->route('device.index')->with('success', 'Device updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->authorize('delete-devices');
        $device = Device::find($id);
        $device->delete();
        return redirect()->route('device.index')->with('success', 'Device deleted successfully.');
    }

    public function heartbeat(Request $request)
    {
        // Get the device data from request
        $data = $request->all();

        // Find the device by serial number
        $device = Device::where('sn', $data['sn'])->first();

        if ($device) {
            // Update device heartbeat
            $device->updateHeartbeat();

            // Log the heartbeat
            Log::channel('device_logs')->info('Device heartbeat received:', [
                'sn' => $data['sn'],
                'timestamp' => now()->toDateTimeString()
            ]);
        }

        // Return the standard heartbeat response
        return response()->json([
            "cmd" => "heart",
            "data" => ["msgack" => rand(10000, 99999), "int" => 45],
            "aims" => 0,
            "stamp" => time()
        ]);
    }

    /**
     * Eject all powerbanks from a device.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function ejectAllPowerbanks(Request $request)
    {
        $this->authorize('control-devices');
        $deviceId = $request->input('device_id');
        $aims = $request->input('aims', 0); // Default to 0 if not provided

        try {
            if ($deviceId) {
                // Eject all powerbanks from a specific device
                $device = Device::findOrFail($deviceId);

                // Create a device command record if it doesn't exist yet
                // This is a fallback in case the AJAX request failed
                if (!$request->has('command_sent')) {
                    // Check if a similar command was recently created (within the last 5 seconds)
                    $recentCommand = DeviceCommand::where('device_sn', $device->sn)
                        ->where('command', 'force')
                        ->where('data', json_encode(['n' => 0]))
                        ->where('aims', $aims)
                        ->where('created_at', '>=', now()->subSeconds(5))
                        ->first();

                    if (!$recentCommand) {
                        $messageId = mt_rand(10000, 99999);
                        $forceCommand = "#*{\"cmd\":\"force\",\"sn\":\"{$device->sn}\",\"aims\":{$aims},\"data\":{\"msg\":{$messageId},\"n\":0}}*#";

                        DeviceCommand::create([
                            'device_sn' => $device->sn,
                            'command' => 'force',
                            'message_id' => $messageId,
                            'data' => json_encode(['n' => 0]),
                            'raw_command' => $forceCommand,
                            'status' => 'pending',
                            'aims' => $aims,
                            'created_by' => auth()->id(),
                        ]);

                        // Log the command
                        Log::channel('device_logs')->info('Force eject all command created via form', [
                            'device_sn' => $device->sn,
                            'command' => 'force',
                            'message_id' => $messageId,
                            'aims' => $aims
                        ]);
                    } else {
                        Log::channel('device_logs')->info('Skipped duplicate force command', [
                            'device_sn' => $device->sn,
                            'command' => 'force',
                            'existing_command_id' => $recentCommand->id,
                            'aims' => $aims
                        ]);
                    }
                }

                // Update all powerbanks in this device with the specified AIMS
                $powerbanks = Powerbank::where('device_id', $device->id)
                    ->where('aims', $aims)
                    ->update(['status' => 'maintenance', 'slot_number' => null]);

                return redirect()->back()
                    ->with('success', "Force eject command sent to device {$device->sn} for AIMS {$aims}");
            } else {
                // Eject all powerbanks from all devices
                return redirect()->back()
                    ->with('error', 'Please select a specific device to eject all powerbanks');
            }
        } catch (\Exception $e) {
            Log::error('Error in ejectAllPowerbanks: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Reboot a device.
     */
    public function rebootDevice(Request $request)
    {
        $this->authorize('control-devices');
        $deviceId = $request->input('device_id');
        $deviceSn = $request->input('device_sn');

        try {
            $device = Device::findOrFail($deviceId);

            // Create a device command record if it doesn't exist yet
            // This is a fallback in case the AJAX request failed
            if (!$request->has('command_sent')) {
                // Check if a similar command was recently created (within the last 5 seconds)
                $recentCommand = DeviceCommand::where('device_sn', $device->sn)
                    ->where('command', 'reboot')
                    ->where('created_at', '>=', now()->subSeconds(5))
                    ->first();

                if (!$recentCommand) {
                    $messageId = mt_rand(10000, 99999);
                    $rebootCommand = "#*{\"cmd\":\"reboot\",\"sn\":\"{$device->sn}\",\"data\":{\"msg\":{$messageId},\"dev\":1}}*#";

                    DeviceCommand::create([
                        'device_sn' => $device->sn,
                        'command' => 'reboot',
                        'message_id' => $messageId,
                        'data' => json_encode(['dev' => 1]),
                        'raw_command' => $rebootCommand,
                        'status' => 'pending',
                        'created_by' => auth()->id(),
                    ]);

                    // Log the command
                    Log::channel('device_logs')->info('Reboot command created via form', [
                        'device_sn' => $device->sn,
                        'command' => 'reboot',
                        'message_id' => $messageId
                    ]);
                } else {
                    Log::channel('device_logs')->info('Skipped duplicate reboot command', [
                        'device_sn' => $device->sn,
                        'command' => 'reboot',
                        'existing_command_id' => $recentCommand->id
                    ]);
                }
            }

            return redirect()->back()->with('success', "Reboot command sent to device {$device->sn}");
        } catch (\Exception $e) {
            Log::error("Error rebooting device: " . $e->getMessage());
            return redirect()->back()->with('error', "Error rebooting device: " . $e->getMessage());
        }
    }

    /**
     * Get powerbanks status for AJAX refresh
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPowerbanksStatus($id)
    {
        $device = Device::findOrFail($id);

        // Get all powerbanks for this device
        $powerbanks = Powerbank::where('device_id', $device->id)->get();

        // Group powerbanks by AIMS value
        $aimsGroups = $powerbanks->groupBy('aims');

        // Prepare data for the response
        $aimsGroupsData = [];
        foreach ($aimsGroups as $aims => $group) {
            $aimsGroupsData[] = [
                'aims' => $aims,
                'count' => $group->count()
            ];
        }

        // Render the powerbanks table partial
        $html = view('device.partials.powerbanks-table', [
            'aimsGroups' => $aimsGroups,
            'device' => $device,
        ])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'aimsGroups' => $aimsGroupsData
        ]);
    }
}
