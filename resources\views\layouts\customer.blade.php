<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>


    <style>
        :root {
            --primary-color: #34D399; /* Light green */
            --secondary-color: #F97316; /* Orange */
            --accent-color: #D1FAE5; /* Light green background */
            --text-color: #1F2937;
            --light-bg: #F9FAFB;
            --hover-color: #059669; /* Darker green for hover states */
            --orange-light: #FFEDD5; /* Light orange for hover states */
        }

        body {
            font-family: 'Inter', sans-serif;
            color: var(--text-color);
            background-color: var(--light-bg);
        }

        .navbar {
            background: white !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.04);
            padding: 1rem 0;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 600;
            color: var(--primary-color) !important;
        }

        .navbar-brand img {
            height: 40px;
            width: auto;
        }

        .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1);
            border-radius: 0.5rem;
        }

        .dropdown-item {
            padding: 0.75rem 1.5rem;
            font-weight: 500;
        }

        .dropdown-item:hover {
            background-color: var(--accent-color);
            color: var(--hover-color);
        }

        main {
            min-height: calc(100vh - 76px);
            padding: 2rem 0;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--hover-color);
            border-color: var(--hover-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-secondary:hover {
            background-color: #EA580C;
            border-color: #EA580C;
        }

        .alert-warning {
            background-color: var(--orange-light);
            border-color: #FDBA74;
            color: #C2410C;
        }

        /* Custom card styling */
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        /* Custom form styling */
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 211, 153, 0.25);
        }

        /* Custom badge styling */
        .badge.bg-primary {
            background-color: var(--primary-color) !important;
        }

        .badge.bg-secondary {
            background-color: var(--secondary-color) !important;
        }

        /* Custom table styling */
        .table thead th {
            background-color: var(--accent-color);
            color: var(--text-color);
            font-weight: 600;
        }

        .table-hover tbody tr:hover {
            background-color: var(--accent-color);
        }
    </style>

    <!-- Styles -->
    @yield('styles')
</head>

<body>
    @if (env('SKIP_OTP_VERIFICATION', false) || env('SKIP_PAYMENT_VERIFICATION', false))
        <div class="alert alert-warning text-center mb-0 py-2">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Testing Mode:</strong>
            @if (env('SKIP_OTP_VERIFICATION', false) && env('SKIP_PAYMENT_VERIFICATION', false))
                OTP and payment verification are disabled
            @elseif(env('SKIP_OTP_VERIFICATION', false))
                OTP verification is disabled
            @elseif(env('SKIP_PAYMENT_VERIFICATION', false))
                Payment verification is disabled
            @endif
        </div>
    @endif

    <div id="app">
        <nav class="navbar navbar-expand-md navbar-light shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="{{ url('/') }}">
                    <img src="{{ asset('assets/images/logo-sm.png') }}" alt="{{ config('app.name', 'Laravel') }}" onerror="this.src='https://ui-avatars.com/api/?name='+encodeURIComponent('{{ config('app.name', 'Laravel') }}')+'&background=34D399&color=fff'">
                    <span>{{ config('app.name', 'Laravel') }}</span>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                    aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Authentication Links -->
                        @if (session()->has('verified_customer_id'))
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button"
                                    data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <i class="fas fa-user-circle me-1"></i>
                                    Customer
                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="{{ route('customer.logout') }}"
                                        onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        {{ __('Logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('customer.logout') }}" method="POST"
                                        class="d-none">
                                        @csrf
                                    </form>
                                </div>
                            </li>
                        @else
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('customer.login') }}">
                                    <i class="fas fa-sign-in-alt me-1"></i>
                                    {{ __('Login') }}
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('customer.register') }}">
                                    <i class="fas fa-user-plus me-1"></i>
                                    {{ __('Register') }}
                                </a>
                            </li>
                        @endif
                    </ul>
                </div>
            </div>
        </nav>

        <main>
            <div class="container">
                @yield('content')
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Scripts -->
    @yield('scripts')
</body>

</html>


