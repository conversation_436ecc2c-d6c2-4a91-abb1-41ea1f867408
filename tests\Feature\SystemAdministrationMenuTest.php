<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use App\Models\UserActivity;
use App\Models\AuditTrail;
use App\Models\DatabaseBackup;
use Tests\TestCase;

class SystemAdministrationMenuTest extends TestCase
{
    protected $superAdmin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a super admin user for testing
        $this->superAdmin = User::factory()->create();
        $superAdminRole = Role::where('slug', 'super-administrator')->first();
        if ($superAdminRole) {
            $this->superAdmin->roles()->attach($superAdminRole);
        }
    }

    /** @test */
    public function user_activity_menu_is_accessible_with_permission()
    {
        $this->actingAs($this->superAdmin);
        
        $response = $this->get(route('admin.user-activity.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.user-activity.index');
        $response->assertViewHas(['activities', 'users', 'modules', 'actions', 'stats']);
    }

    /** @test */
    public function user_activity_shows_statistics()
    {
        $this->actingAs($this->superAdmin);
        
        // Create some test activities
        UserActivity::factory()->count(5)->create();
        UserActivity::factory()->count(3)->create(['created_at' => today()]);
        
        $response = $this->get(route('admin.user-activity.index'));
        
        $response->assertStatus(200);
        $stats = $response->viewData('stats');
        
        $this->assertArrayHasKey('total_activities', $stats);
        $this->assertArrayHasKey('today_activities', $stats);
        $this->assertArrayHasKey('active_users', $stats);
        $this->assertArrayHasKey('week_activities', $stats);
    }

    /** @test */
    public function audit_trail_menu_is_accessible_with_permission()
    {
        $this->actingAs($this->superAdmin);
        
        $response = $this->get(route('admin.audit-trail.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.audit-trail.index');
        $response->assertViewHas(['auditTrails', 'users', 'models', 'actions', 'stats']);
    }

    /** @test */
    public function audit_trail_shows_statistics()
    {
        $this->actingAs($this->superAdmin);
        
        // Create some test audit records
        AuditTrail::factory()->count(10)->create();
        AuditTrail::factory()->count(2)->create(['created_at' => today()]);
        
        $response = $this->get(route('admin.audit-trail.index'));
        
        $response->assertStatus(200);
        $stats = $response->viewData('stats');
        
        $this->assertArrayHasKey('total_audits', $stats);
        $this->assertArrayHasKey('today_audits', $stats);
        $this->assertArrayHasKey('data_changes', $stats);
        $this->assertArrayHasKey('active_users', $stats);
    }

    /** @test */
    public function database_backups_menu_is_accessible_with_permission()
    {
        $this->actingAs($this->superAdmin);
        
        $response = $this->get(route('admin.backups.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.backups.index');
        $response->assertViewHas(['backups', 'stats', 'users']);
    }

    /** @test */
    public function database_backups_shows_statistics()
    {
        $this->actingAs($this->superAdmin);
        
        // Create some test backups
        DatabaseBackup::factory()->count(3)->create();
        
        $response = $this->get(route('admin.backups.index'));
        
        $response->assertStatus(200);
        $stats = $response->viewData('stats');
        
        $this->assertArrayHasKey('total_backups', $stats);
        $this->assertArrayHasKey('total_size', $stats);
        $this->assertArrayHasKey('latest_backup', $stats);
        $this->assertArrayHasKey('success_rate', $stats);
    }

    /** @test */
    public function system_administration_menu_appears_in_sidebar()
    {
        $this->actingAs($this->superAdmin);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        $response->assertSee('System Administration');
        $response->assertSee('User Activity');
        $response->assertSee('Audit Trail');
        $response->assertSee('Database Backups');
    }

    /** @test */
    public function unauthorized_user_cannot_access_system_administration()
    {
        // Create a user without permissions
        $user = User::factory()->create();
        $this->actingAs($user);
        
        // Test user activity access
        $response = $this->get(route('admin.user-activity.index'));
        $response->assertStatus(403);
        
        // Test audit trail access
        $response = $this->get(route('admin.audit-trail.index'));
        $response->assertStatus(403);
        
        // Test backups access
        $response = $this->get(route('admin.backups.index'));
        $response->assertStatus(403);
    }

    /** @test */
    public function user_activity_filtering_works()
    {
        $this->actingAs($this->superAdmin);
        
        // Create activities with different actions
        UserActivity::factory()->create(['action' => 'login']);
        UserActivity::factory()->create(['action' => 'logout']);
        
        // Filter by action
        $response = $this->get(route('admin.user-activity.index', ['action' => 'login']));
        
        $response->assertStatus(200);
        $activities = $response->viewData('activities');
        
        foreach ($activities as $activity) {
            $this->assertEquals('login', $activity->action);
        }
    }

    /** @test */
    public function audit_trail_filtering_works()
    {
        $this->actingAs($this->superAdmin);
        
        // Create audit records with different actions
        AuditTrail::factory()->create(['action' => 'created']);
        AuditTrail::factory()->create(['action' => 'updated']);
        
        // Filter by action
        $response = $this->get(route('admin.audit-trail.index', ['action' => 'created']));
        
        $response->assertStatus(200);
        $auditTrails = $response->viewData('auditTrails');
        
        foreach ($auditTrails as $audit) {
            $this->assertEquals('created', $audit->action);
        }
    }

    /** @test */
    public function menu_permissions_are_properly_checked()
    {
        // Create user with specific permission
        $user = User::factory()->create();
        $permission = Permission::where('slug', 'view-user-activity')->first();
        
        if ($permission) {
            $role = Role::factory()->create();
            $role->permissions()->attach($permission);
            $user->roles()->attach($role);
        }
        
        $this->actingAs($user);
        
        // Should be able to access user activity
        $response = $this->get(route('admin.user-activity.index'));
        $response->assertStatus(200);
        
        // Should not be able to access audit trail (no permission)
        $response = $this->get(route('admin.audit-trail.index'));
        $response->assertStatus(403);
    }

    /** @test */
    public function breadcrumbs_are_displayed_correctly()
    {
        $this->actingAs($this->superAdmin);
        
        // Test user activity breadcrumbs
        $response = $this->get(route('admin.user-activity.index'));
        $response->assertSee('Dashboard');
        $response->assertSee('System Administration');
        $response->assertSee('User Activity');
        
        // Test audit trail breadcrumbs
        $response = $this->get(route('admin.audit-trail.index'));
        $response->assertSee('Dashboard');
        $response->assertSee('System Administration');
        $response->assertSee('Audit Trail');
        
        // Test backups breadcrumbs
        $response = $this->get(route('admin.backups.index'));
        $response->assertSee('Dashboard');
        $response->assertSee('System Administration');
        $response->assertSee('Database Backups');
    }
}
