<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('communication_logs', function (Blueprint $table) {
            $table->id();
            $table->string('device_sn')->index(); // Device Serial Number
            $table->string('command'); // Command Type (login, rent, return, etc.)
            $table->json('request_data'); // Data Sent by Device
            $table->json('response_data')->nullable(); // Data Sent Back to Device
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('communication_logs');
    }
};
