<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;
use Exception;

class SocialLoginController extends Controller
{
    public function redirectToProvider($provider)
    {
        return Socialite::driver($provider)->redirect();
    }

    public function handleProviderCallback($provider)
    {
        try {
            $user = Socialite::driver($provider)->user();
            
            // Check if user already exists
            $customer = Customer::where('email', $user->getEmail())->first();
            
            if (!$customer) {
                // Store social data in session for registration form
                session()->put('social_data', [
                    'name' => $user->getName(),
                    'email' => $user->getEmail(),
                    'social_id' => $user->getId(),
                    'social_type' => $provider,
                ]);

                // Redirect to registration form
                return redirect()->route('customer.register');
            }

            // Store customer ID in session
            session()->put('verified_customer_id', $customer->id);
            session()->put('registered_customer', $customer);

            // Check if we need to redirect back to a lease page
            if (session()->has('lease_return_url')) {
                $returnUrl = session()->get('lease_return_url');
                session()->forget('lease_return_url');
                return redirect($returnUrl);
            }

            return redirect()->route('customer.registration-complete');

        } catch (Exception $e) {
            return redirect()->route('customer.register')
                ->withErrors(['error' => 'Social login failed: ' . $e->getMessage()]);
        }
    }
} 