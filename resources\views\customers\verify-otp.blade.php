@extends('layouts.customer')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
        <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                    <h4 class="mb-0">Verify Your WhatsApp</h4>
                    </div>
                    <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            </div>
                        @endif

                    <div class="text-center mb-4">
                        <i class="fab fa-whatsapp text-success" style="font-size: 3rem;"></i>
                        <p class="mt-3">We've sent a 6-digit verification code to your WhatsApp number.</p>
                            </div>

                    <form method="POST" action="{{ route('customer.verify-otp') }}" id="otpForm">
                            @csrf
                        <div class="otp-inputs mb-4">
                            <div class="row justify-content-center g-2">
                                @for ($i = 1; $i <= 6; $i++)
                                    <div class="col-2 col-sm-2 col-md-2">
                                        <input type="text" 
                                               class="form-control  text-center otp-input" 
                                               name="otp[]" 
                                               maxlength="1" 
                                               pattern="[0-9]" 
                                               inputmode="numeric"
                                               autocomplete="off"
                                               required>
                                    </div>
                                @endfor
                            </div>
                            </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                Verify OTP
                            </button>
                            </div>
                        </form>

                    <div class="text-center mt-4">
                        <p class="mb-2">Didn't receive the code?</p>
                        <form method="POST" action="{{ route('customer.resend-otp') }}" class="d-inline">
                                    @csrf
                            <button type="submit" class="btn btn-link">Resend OTP</button>
                                </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@push('styles')
<style>
.otp-input {
    font-size: 1.5rem;
    height: 3.5rem;
    width: 100%;
    text-align: center;
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    font-weight: bold;
    color: #333;
}

.otp-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    outline: none;
}

.otp-inputs {
    margin: 2rem 0;
}

.btn-link {
    text-decoration: none;
    padding: 0;
}

.btn-link:hover {
    text-decoration: underline;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
    .otp-input {
        font-size: 1.8rem;
        height: 4rem;
        min-width: 50px;
        margin: 0 0.125rem;
    }
    
    .otp-inputs .row {
        justify-content: center;
        gap: 0.5rem;
    }
    
    .otp-inputs .col-2 {
        flex: 0 0 auto;
        width: auto;
        min-width: 50px;
    }
}

@media (max-width: 576px) {
    .otp-input {
        font-size: 2rem;
        height: 4.5rem;
        min-width: 55px;
        margin: 0 0.25rem;
    }
    
    .otp-inputs .col-2 {
        min-width: 55px;
    }
}

/* Ensure input boxes are visible and properly sized */
.otp-inputs .form-control {
    display: block;
    width: 100%;
    min-width: 50px;
}

/* Improve touch targets on mobile */
@media (max-width: 768px) {
    .otp-input {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
}
</style>
@endpush
@endsection

@section('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
    const otpInputs = document.querySelectorAll('.otp-input');
    const form = document.getElementById('otpForm');

    // Focus the first input box when page loads
    otpInputs[0].focus();

    // Function to focus next input
    function focusNextInput(currentInput) {
        const currentIndex = Array.from(otpInputs).indexOf(currentInput);
        if (currentIndex < otpInputs.length - 1) {
            otpInputs[currentIndex + 1].focus();
        }
    }

    // Function to focus previous input
    function focusPrevInput(currentInput) {
        const currentIndex = Array.from(otpInputs).indexOf(currentInput);
        if (currentIndex > 0) {
            otpInputs[currentIndex - 1].focus();
        }
    }

    // Function to handle input value
    function handleInputValue(input) {
        // Only allow numbers
        input.value = input.value.replace(/[^0-9]/g, '');
        
        // Ensure the value is visible
        input.style.color = '#333';
        input.style.fontWeight = 'bold';
        
        if (input.value.length === 1) {
            focusNextInput(input);
        }
    }

    // Handle input for each OTP box
    otpInputs.forEach((input, index) => {
        // Handle keyup for immediate response
        input.addEventListener('keyup', function(e) {
            handleInputValue(this);
        });

        // Handle keydown for backspace
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace') {
                if (this.value.length === 0) {
                    focusPrevInput(this);
                }
            }
        });

        // Handle paste
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedData = e.clipboardData.getData('text').slice(0, 6);
            const digits = pastedData.split('');
            
            digits.forEach((digit, i) => {
                if (otpInputs[i]) {
                    otpInputs[i].value = digit;
                    handleInputValue(otpInputs[i]);
                }
            });
            
            if (digits.length === 6) {
                form.submit();
            }
        });

        // Handle input event for mobile devices
        input.addEventListener('input', function(e) {
            handleInputValue(this);
        });

        // Handle focus to ensure visibility
        input.addEventListener('focus', function(e) {
            this.style.backgroundColor = '#f8f9fa';
            this.style.borderColor = '#0d6efd';
        });

        // Handle blur
        input.addEventListener('blur', function(e) {
            this.style.backgroundColor = '';
            if (this.value.length === 0) {
                this.style.borderColor = '#dee2e6';
            }
        });

        // Handle touch events for mobile
        input.addEventListener('touchstart', function(e) {
            this.focus();
        });
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        const otp = Array.from(otpInputs).map(input => input.value).join('');
        
        if (otp.length === 6) {
            // Add the OTP to a hidden input
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'phone_otp';
            hiddenInput.value = otp;
            form.appendChild(hiddenInput);
            
            // Submit the form
            form.submit();
        }
    });

    // Auto-focus first empty input
    function focusFirstEmpty() {
        for (let input of otpInputs) {
            if (input.value.length === 0) {
                input.focus();
                break;
            }
        }
    }

    // Focus first empty input on page load
    setTimeout(focusFirstEmpty, 100);
            });
        </script>
@endsection
