@extends('layouts.customer')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Verify OTP</h5>
                    </div>
                    <div class="card-body">
                        @if (session('sms_otp'))
                            <div class="alert alert-info">
                                <strong>Demo Mode:</strong> Your SMS OTP is: {{ session('sms_otp') }}
                            </div>
                        @endif

                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif

                        <form action="{{ route('customer.verify-otp.submit') }}" method="POST">
                            @csrf
                            <div class="form-group mb-3">
                                <label for="email_otp">Email OTP</label>
                                <input type="text" class="form-control @error('email_otp') is-invalid @enderror"
                                    id="email_otp" name="email_otp" placeholder="Enter your email OTP" required>
                                <small class="form-text text-muted">Enter the 6-digit code sent to your email.</small>
                                @error('email_otp')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group mb-3">
                                <label for="phone_otp">WhatsApp OTP</label>
                                <input type="text" class="form-control @error('phone_otp') is-invalid @enderror"
                                    id="phone_otp" name="phone_otp" placeholder="Enter your WhatsApp OTP" required>
                                <small class="form-text text-muted">Enter the 6-digit code sent to your WhatsApp.</small>
                                @error('phone_otp')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            @error('otp')
                                <div class="alert alert-danger">{{ $message }}</div>
                            @enderror

                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <button type="submit" class="btn btn-primary">Verify</button>
                            </div>
                        </form>

                        <div class="text-end mt-3">
                            <div id="timer-container">
                                <span id="timer-text" class="text-muted d-block mb-1">Resend in <span
                                        id="countdown">60</span> seconds</span>
                                <form id="resend-form" action="{{ route('customer.resend-otp') }}" method="POST">
                                    @csrf
                                    <button type="submit" id="resend-button" class="btn btn-outline-secondary btn-sm"
                                        disabled>
                                        <i class="fas fa-sync-alt me-1"></i> Resend OTP
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                let countdown = 60;
                const timerElement = document.getElementById('countdown');
                const resendButton = document.getElementById('resend-button');
                const timerText = document.getElementById('timer-text');

                // Start countdown
                const timer = setInterval(function() {
                    countdown--;
                    timerElement.textContent = countdown;

                    if (countdown <= 0) {
                        clearInterval(timer);
                        resendButton.disabled = false;
                        resendButton.classList.remove('btn-outline-secondary');
                        resendButton.classList.add('btn-primary');
                        timerText.classList.add('d-none');
                    }
                }, 1000);

                // If resend was successful, restart the timer
                @if (session('success'))
                    countdown = 60;
                    timerElement.textContent = countdown;
                    resendButton.disabled = true;
                    resendButton.classList.add('btn-outline-secondary');
                    resendButton.classList.remove('btn-primary');
                    timerText.classList.remove('d-none');
                @endif
            });
        </script>
    @endpush
@endsection
