<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserActivity;
use Illuminate\Http\Request;

class UserActivityController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize('view-user-activity');

        $query = UserActivity::with('user')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('action') && $request->action) {
            $query->where('action', $request->action);
        }

        if ($request->has('module') && $request->module) {
            $query->where('module', $request->module);
        }

        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $activities = $query->paginate(15);

        return view('admin.user-activity.index', [
            'activities' => $activities,
            'users' => \App\Models\User::orderBy('name')->get(),
            'modules' => UserActivity::select('module')->distinct()->pluck('module'),
            'actions' => UserActivity::select('action')->distinct()->pluck('action'),
        ]);
    }

    public function show($id)
    {
        $this->authorize('view-user-activity');

        $activity = UserActivity::with('user')->findOrFail($id);

        return view('admin.user-activity.show', [
            'activity' => $activity,
        ]);
    }
}
