<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserActivity;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Symfony\Component\HttpFoundation\Response;
use Carbon\Carbon;

class UserActivityController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('permission:view-user-activity');
    }

    /**
     * Display a listing of user activities.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Get filter options
        $users = User::orderBy('name')->get();
        $actions = UserActivity::distinct()->pluck('action');
        $modules = UserActivity::distinct()->pluck('module');

        return view('admin.user-activity.index', compact(
            'users',
            'actions',
            'modules'
        ));
    }

    /**
     * Test endpoint for DataTables
     */
    public function getTestData(Request $request)
    {
        return response()->json([
            'draw' => intval($request->draw ?? 1),
            'recordsTotal' => 3,
            'recordsFiltered' => 3,
            'data' => [
                ['<PERSON> Doe', 'login', 'auth', 'User logged in', '127.0.0.1', '2025-06-10 16:00:00', 'View'],
                ['Jane Smith', 'logout', 'auth', 'User logged out', '192.168.1.1', '2025-06-10 15:30:00', 'View'],
                ['System', 'create', 'users', 'User created', '10.0.0.1', '2025-06-10 14:00:00', 'View']
            ]
        ]);
    }

    /**
     * Get user activities data for DataTables.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getData(Request $request)
    {
        \Log::info('UserActivity getData called', ['request' => $request->all()]);

        // Validate filter parameters
        $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'action' => 'nullable|string',
            'module' => 'nullable|string',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date',
        ]);

        // Build query with filters
        $query = UserActivity::with('user')
            ->when($request->filled('user_id'), function ($q) use ($request) {
                return $q->where('user_id', $request->user_id);
            })
            ->when($request->filled('action'), function ($q) use ($request) {
                return $q->where('action', $request->action);
            })
            ->when($request->filled('module'), function ($q) use ($request) {
                return $q->where('module', $request->module);
            })
            ->when($request->filled('date_from'), function ($q) use ($request) {
                return $q->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->filled('date_to'), function ($q) use ($request) {
                return $q->whereDate('created_at', '<=', $request->date_to);
            });

        // Handle DataTables search
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('action', 'like', "%{$searchValue}%")
                    ->orWhere('module', 'like', "%{$searchValue}%")
                    ->orWhere('description', 'like', "%{$searchValue}%")
                    ->orWhere('ip_address', 'like', "%{$searchValue}%")
                    ->orWhereHas('user', function ($userQuery) use ($searchValue) {
                        $userQuery->where('name', 'like', "%{$searchValue}%");
                    });
            });
        }

        // Handle DataTables ordering
        if ($request->has('order')) {
            $orderColumn = $request->order[0]['column'];
            $orderDirection = $request->order[0]['dir'];

            // Map column indices to database columns
            $columns = ['user_id', 'action', 'module', 'description', 'ip_address', 'created_at', 'actions'];

            if (isset($columns[$orderColumn])) {
                if ($columns[$orderColumn] === 'user_id') {
                    $query->leftJoin('users', 'user_activities.user_id', '=', 'users.id')
                        ->orderBy('users.name', $orderDirection)
                        ->select('user_activities.*');
                } elseif ($columns[$orderColumn] !== 'actions') {
                    $query->orderBy($columns[$orderColumn], $orderDirection);
                }
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        // Get total count before pagination
        $totalRecords = UserActivity::count();

        // Clone query for filtered count to avoid issues with joins
        $filteredQuery = clone $query;
        $filteredRecords = $filteredQuery->count();

        // Handle DataTables pagination
        if ($request->has('start') && $request->has('length')) {
            $query->skip($request->start)->take($request->length);
        }

        $activities = $query->get();

        // Format data for DataTables with enhanced formatting
        $data = [];
        foreach ($activities as $activity) {
            // User info with photo
            $userInfo = $activity->user
                ? '<div class="d-flex align-items-center">
                     <div class="mr-2">
                       <img class="rounded-circle" width="32" height="32"
                            src="' . ($activity->user->profile_photo_url ?? asset('assets/images/users/default-avatar.png')) . '"
                            alt="' . htmlspecialchars($activity->user->name) . '"
                            onerror="this.src=\'' . asset('assets/images/users/default-avatar.png') . '\'">
                     </div>
                     <div>
                       <div class="font-weight-bold">' . htmlspecialchars($activity->user->name) . '</div>
                       <small class="text-muted">' . htmlspecialchars($activity->user->email ?? '') . '</small>
                     </div>
                   </div>'
                : '<div class="d-flex align-items-center">
                     <div class="mr-2">
                       <i class="fas fa-cog text-muted" style="width: 32px; height: 32px; line-height: 32px; text-align: center; border: 1px solid #dee2e6; border-radius: 50%; background: #f8f9fa;"></i>
                     </div>
                     <div>
                       <div class="font-weight-bold text-muted">System</div>
                       <small class="text-muted">Automated</small>
                     </div>
                   </div>';

            // Colored action badge
            $actionBadge = $this->getActionBadge($activity->action);

            // Action buttons with proper functionality
            $actions = '<div class="btn-group" role="group">
            <a href="' . route('admin.user-activity.show', $activity->id) . '"
            class="btn btn-sm btn-info"
            title="View Details">
            <i class="fas fa-eye"></i> View
            </a>';

            // Add additional actions based on permissions - Always show for testing
            $actions .= '<button type="button"
            class="btn btn-sm btn-info dropdown-toggle dropdown-toggle-split"
            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
            style="border-left: 1px solid rgba(255,255,255,0.3) !important; min-width: 20px;">
            <i class="fas fa-chevron-down"></i>
            <span class="sr-only">Toggle Dropdown</span>
            </button>
            <div class="dropdown-menu dropdown-menu-right">
            <h6 class="dropdown-header">Activity Actions</h6>
            <a class="dropdown-item" href="' . route('admin.user-activity.show', $activity->id) . '">
            <i class="fas fa-info-circle text-info"></i> View Full Details
            </a>';

            if ($activity->user) {
                // Check if user route exists and user is accessible
                try {
                    $userRoute = route('user.show', $activity->user->id);
                    $actions .= '<a class="dropdown-item" href="' . $userRoute . '">
            <i class="fas fa-user text-primary"></i> View User Profile
            </a>';
                } catch (\Exception $e) {
                    // Fallback if route doesn't exist
                    $actions .= '<span class="dropdown-item text-muted">
            <i class="fas fa-user text-muted"></i> User Profile (Not Available)
            </span>';
                }
            }

            $actions .= '<div class="dropdown-divider"></div>
            <h6 class="dropdown-header">Utilities</h6>
            <a class="dropdown-item" href="#" onclick="copyToClipboard(\'' . $activity->ip_address . '\'); return false;">
            <i class="fas fa-copy text-secondary"></i> Copy IP Address
            </a>
            </div>';

            $actions .= '</div>';

            $data[] = [
                'user' => $userInfo,
                'action' => $actionBadge,
                'module' => '<span class="badge badge-light">' . ucfirst($activity->module) . '</span>',
                'description' => '<span title="' . htmlspecialchars($activity->description) . '">' .
                    \Str::limit($activity->description, 60) . '</span>',
                'ip_address' => '<code class="text-muted">' . $activity->ip_address . '</code>',
                'created_at' => '<span title="' . $activity->created_at->format('Y-m-d H:i:s') . '">' .
                    $activity->created_at->diffForHumans() . '</span>',
                'actions' => $actions
            ];
        }

        $response = [
            'draw' => intval($request->draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ];

        \Log::info('UserActivity getData response', [
            'draw' => $response['draw'],
            'recordsTotal' => $response['recordsTotal'],
            'recordsFiltered' => $response['recordsFiltered'],
            'data_count' => count($response['data'])
        ]);

        return response()->json($response);
    }

    /**
     * Get action badge HTML.
     *
     * @param string $action
     * @return string
     */
    private function getActionBadge($action)
    {
        $class = 'light';
        switch (strtolower($action)) {
            case 'create':
            case 'created':
                $class = 'success';
                break;
            case 'update':
            case 'updated':
            case 'edit':
                $class = 'warning';
                break;
            case 'delete':
            case 'deleted':
                $class = 'danger';
                break;
            case 'login':
            case 'view':
            case 'read':
                $class = 'info';
                break;
            case 'logout':
                $class = 'secondary';
                break;
            case 'export':
                $class = 'primary';
                break;
        }
        return '<span class="badge badge-' . $class . ' action-badge">' . ucfirst($action) . '</span>';
    }

    /**
     * Display the specified activity.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $activity = UserActivity::with('user')->findOrFail($id);
        return view('admin.user-activity.show', compact('activity'));
    }

    /**
     * Export activities to CSV.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function export(Request $request)
    {
        if (!Gate::allows('manage-user-activity')) {
            abort(Response::HTTP_FORBIDDEN);
        }

        // Build query with filters (same as getData)
        $query = UserActivity::with('user')
            ->when($request->filled('user_id'), function ($q) use ($request) {
                return $q->where('user_id', $request->user_id);
            })
            ->when($request->filled('action'), function ($q) use ($request) {
                return $q->where('action', $request->action);
            })
            ->when($request->filled('module'), function ($q) use ($request) {
                return $q->where('module', $request->module);
            })
            ->when($request->filled('date_from'), function ($q) use ($request) {
                return $q->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->filled('date_to'), function ($q) use ($request) {
                return $q->whereDate('created_at', '<=', $request->date_to);
            })
            ->orderBy('created_at', 'desc');

        $activities = $query->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="user-activities-' . date('Y-m-d') . '.csv"',
        ];

        $callback = function () use ($activities) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, [
                'ID',
                'User',
                'Action',
                'Module',
                'Description',
                'IP Address',
                'Date & Time'
            ]);

            // Add data rows
            foreach ($activities as $activity) {
                fputcsv($file, [
                    $activity->id,
                    $activity->user ? $activity->user->name : 'System',
                    $activity->action,
                    $activity->module,
                    $activity->description,
                    $activity->ip_address,
                    $activity->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Clear old activity logs.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function clear(Request $request)
    {
        if (!Gate::allows('manage-user-activity')) {
            abort(Response::HTTP_FORBIDDEN);
        }

        $request->validate([
            'days' => 'required|integer|min:7|max:365',
        ]);

        $cutoffDate = Carbon::now()->subDays($request->days);
        $count = UserActivity::where('created_at', '<', $cutoffDate)->delete();

        return redirect()->route('admin.user-activity.index')
            ->with('success', "Successfully cleared {$count} activity logs older than {$request->days} days.");
    }

    /**
     * Get the Bootstrap badge class for a given action.
     *
     * @param  string $action
     * @return string
     */
    public static function getActionBadgeClass($action)
    {
        switch (strtolower($action)) {
            case 'create':
            case 'created':
                return 'success';
            case 'update':
            case 'updated':
            case 'edit':
                return 'warning';
            case 'delete':
            case 'deleted':
                return 'danger';
            case 'login':
            case 'view':
            case 'read':
                return 'info';
            case 'logout':
                return 'secondary';
            case 'export':
                return 'primary';
            default:
                return 'light';
        }
    }
}
