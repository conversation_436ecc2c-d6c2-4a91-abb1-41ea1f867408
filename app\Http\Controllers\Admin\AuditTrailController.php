<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AuditTrail;
use Illuminate\Http\Request;

class AuditTrailController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize('view-audit-trail');

        $query = AuditTrail::with('user')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('action') && $request->action) {
            $query->where('action', $request->action);
        }

        if ($request->has('model_type') && $request->model_type) {
            $query->where('auditable_type', 'LIKE', '%' . $request->model_type . '%');
        }

        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $auditTrails = $query->paginate(15);

        // Statistics
        $stats = [
            'total_audits' => AuditTrail::count(),
            'today_audits' => AuditTrail::whereDate('created_at', today())->count(),
            'data_changes' => AuditTrail::whereIn('action', ['updated', 'deleted'])->count(),
            'active_users' => AuditTrail::distinct('user_id')->whereDate('created_at', '>=', now()->subDays(7))->count('user_id'),
        ];

        return view('admin.audit-trail.index', [
            'auditTrails' => $auditTrails,
            'users' => \App\Models\User::orderBy('name')->get(),
            'models' => AuditTrail::select('auditable_type')->distinct()->pluck('auditable_type'),
            'actions' => AuditTrail::select('action')->distinct()->pluck('action'),
            'stats' => $stats,
        ]);
    }

    public function show($id)
    {
        $this->authorize('view-audit-trail');

        $auditTrail = AuditTrail::with('user')->findOrFail($id);

        return view('admin.audit-trail.show', [
            'auditTrail' => $auditTrail,
        ]);
    }
}
