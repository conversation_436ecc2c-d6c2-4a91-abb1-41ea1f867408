<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AuditTrail;
use Illuminate\Http\Request;

class AuditTrailController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize('view-audit-trail');

        $query = AuditTrail::with('user')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('action') && $request->action) {
            $query->where('action', $request->action);
        }

        if ($request->has('model_type') && $request->model_type) {
            $query->where('auditable_type', 'LIKE', '%' . $request->model_type . '%');
        }

        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $auditTrails = $query->paginate(15);

        return view('admin.audit-trail.index', [
            'auditTrails' => $auditTrails,
            'users' => \App\Models\User::orderBy('name')->get(),
        ]);
    }

    public function show($id)
    {
        $this->authorize('view-audit-trail');

        $auditTrail = AuditTrail::with('user')->findOrFail($id);

        return view('admin.audit-trail.show', [
            'auditTrail' => $auditTrail,
        ]);
    }
}