<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AuditTrail;
use App\Models\User;
use App\Services\AuditTrailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;

class AuditTrailController extends Controller
{
    /**
     * Display a listing of audit trails.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $this->authorize('view-audit-trail');

        // Get filter parameters
        $filters = $request->only(['user_id', 'action', 'module', 'severity', 'date_from', 'date_to']);

        // Build query with filters
        $query = AuditTrail::with('user')
            ->when($request->filled('user_id'), function ($q) use ($request) {
                return $q->where('user_id', $request->user_id);
            })
            ->when($request->filled('action'), function ($q) use ($request) {
                return $q->where('action', $request->action);
            })
            ->when($request->filled('module'), function ($q) use ($request) {
                return $q->where('module', $request->module);
            })
            ->when($request->filled('severity'), function ($q) use ($request) {
                return $q->where('severity', $request->severity);
            })
            ->when($request->filled('date_from'), function ($q) use ($request) {
                return $q->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->filled('date_to'), function ($q) use ($request) {
                return $q->whereDate('created_at', '<=', $request->date_to);
            });

        // Get paginated results
        $auditTrails = $query->orderBy('created_at', 'desc')->paginate(25);

        // Get data for filter dropdowns
        $users = User::orderBy('name')->get();
        $actions = AuditTrail::distinct('action')->pluck('action')->filter()->sort();
        $modules = AuditTrail::distinct('module')->pluck('module')->filter()->sort();

        // Get statistics
        $stats = [
            'total' => AuditTrail::count(),
            'today' => AuditTrail::whereDate('created_at', today())->count(),
            'high_severity' => AuditTrail::where('severity', 'high')->count(),
            'unique_users' => AuditTrail::distinct('user_id')->count('user_id')
        ];

        return view('admin.audit-trail.index', compact('auditTrails', 'users', 'actions', 'modules', 'filters', 'stats'));
    }

    /**
     * Display the specified audit trail record.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $this->authorize('view-audit-trail');

        $auditTrail = AuditTrail::with('user')->findOrFail($id);

        // Log the view
        AuditTrailService::logCustom(
            'viewed',
            'audit_trail',
            'low',
            "Viewed audit trail record #$id"
        );

        return view('admin.audit-trail.show', compact('auditTrail'));
    }

    /**
     * Export audit trail data to CSV.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $this->authorize('export-audit-trail');

        // Build query with filters
        $query = AuditTrail::with('user')
            ->when($request->filled('user_id'), function ($q) use ($request) {
                return $q->where('user_id', $request->user_id);
            })
            ->when($request->filled('action'), function ($q) use ($request) {
                return $q->where('action', $request->action);
            })
            ->when($request->filled('module'), function ($q) use ($request) {
                return $q->where('module', $request->module);
            })
            ->when($request->filled('severity'), function ($q) use ($request) {
                return $q->where('severity', $request->severity);
            })
            ->when($request->filled('date_from'), function ($q) use ($request) {
                return $q->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->filled('date_to'), function ($q) use ($request) {
                return $q->whereDate('created_at', '<=', $request->date_to);
            });

        // Order by created_at descending
        $auditTrails = $query->orderBy('created_at', 'desc')->get();

        // Create CSV filename
        $filename = 'audit_trail_' . now()->format('Y-m-d_H-i-s') . '.csv';

        // Set headers for download
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        // Create a callback for streaming the CSV
        $callback = function () use ($auditTrails) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, [
                'ID',
                'User',
                'Email',
                'Action',
                'Module',
                'Severity',
                'Notes',
                'IP Address',
                'User Agent',
                'Date & Time'
            ]);

            // Add data rows
            foreach ($auditTrails as $audit) {
                fputcsv($file, [
                    $audit->id,
                    $audit->user ? $audit->user->name : 'System',
                    $audit->user ? $audit->user->email : 'N/A',
                    ucfirst($audit->action),
                    ucfirst($audit->module ?? 'N/A'),
                    ucfirst($audit->severity),
                    $audit->notes,
                    $audit->ip_address ?? 'N/A',
                    $audit->user_agent ?? 'N/A',
                    $audit->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        // Log the export
        AuditTrailService::logCustom(
            'exported',
            'audit_trail',
            'low',
            'Exported audit trail data',
            null,
            [
                'filters' => $request->all(),
                'record_count' => $auditTrails->count()
            ]
        );

        // Return streaming response
        return Response::stream($callback, 200, $headers);
    }

    /**
     * Show the analytics dashboard for audit trail.
     *
     * @return \Illuminate\View\View
     */
    public function analytics()
    {
        $this->authorize('view-audit-trail');

        // Activity over time (last 30 days)
        $activityOverTime = AuditTrail::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as count')
        )
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Activity by severity
        $activityBySeverity = AuditTrail::select(
            'severity',
            DB::raw('COUNT(*) as count')
        )
            ->groupBy('severity')
            ->orderBy('count', 'desc')
            ->get();

        // Activity by action
        $activityByAction = AuditTrail::select(
            'action',
            DB::raw('COUNT(*) as count')
        )
            ->groupBy('action')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        // Activity by module
        $activityByModule = AuditTrail::select(
            'module',
            DB::raw('COUNT(*) as count')
        )
            ->whereNotNull('module')
            ->groupBy('module')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        // Top users
        $topUsers = AuditTrail::select(
            'user_id',
            DB::raw('COUNT(*) as count')
        )
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->with('user')
            ->get();

        // Recent high severity events
        $highSeverityEvents = AuditTrail::where('severity', 'high')
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.audit-trail.analytics', compact(
            'activityOverTime',
            'activityBySeverity',
            'activityByAction',
            'activityByModule',
            'topUsers',
            'highSeverityEvents'
        ));
    }

    /**
     * Clear old audit trail records.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clear(Request $request)
    {
        $this->authorize('manage-audit-trail');

        $request->validate([
            'days_to_keep' => 'required|integer|min:30|max:365',
        ]);

        $daysToKeep = $request->days_to_keep;
        $count = AuditTrailService::clearOldRecords($daysToKeep);

        return redirect()->route('admin.audit-trail.index')
            ->with('success', "Successfully cleared {$count} audit trail records older than {$daysToKeep} days.");
    }

    /**
     * Clear all audit trail records.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearAll(Request $request)
    {
        $this->authorize('clear-audit-trail');

        // Validate confirmation
        $request->validate([
            'confirm' => 'required|string|in:CLEAR'
        ]);

        try {
            // Get count before deletion
            $count = AuditTrail::count();

            // Delete all records
            AuditTrail::truncate();

            // Log the action (this will be the first new audit trail record)
            AuditTrailService::logCustom(
                'cleared',
                'audit_trail',
                'high',
                "Cleared all audit trail records ($count records deleted)"
            );

            return redirect()->route('admin.audit-trail.index')
                ->with('success', "Successfully cleared all audit trail records ($count records deleted)");
        } catch (\Exception $e) {
            return redirect()->route('admin.audit-trail.index')
                ->with('error', 'Failed to clear audit trail: ' . $e->getMessage());
        }
    }
}
