@extends('layouts.customer')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
        <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                    <h4 class="mb-0">Login: Verify WhatsApp OTP</h4>
                    </div>
                    <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            </div>
                        @endif
                    <div class="text-center mb-4">
                        <i class="fab fa-whatsapp text-success" style="font-size: 3rem;"></i>
                        <p class="mt-3">We've sent a 6-digit verification code to your WhatsApp number.</p>
                            </div>
                    <form method="POST" action="{{ route('customer.login.verify-otp') }}" id="otpForm">
                            @csrf
                        <div class="otp-inputs mb-4">
                            <div class="row justify-content-center">
                                @for ($i = 1; $i <= 6; $i++)
                                    <div class="col-2">
                                        <input type="text" 
                                               class="form-control form-control-lg text-center otp-input" 
                                               name="otp[]" 
                                               maxlength="1" 
                                               pattern="[0-9]" 
                                               inputmode="numeric"
                                               autocomplete="off"
                                               required>
                                    </div>
                                @endfor
                            </div>
                            </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                Verify OTP
                            </button>
                            </div>
                        </form>
                    <div class="text-center mt-4">
                        <p class="mb-2">Didn't receive the code?</p>
                        <form method="POST" action="{{ route('customer.login.mobile.submit') }}" class="d-inline">
                                    @csrf
                            <button type="submit" class="btn btn-link">Resend OTP</button>
                                </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.otp-input {
    font-size: 1.5rem;
    height: 3.5rem;
    width: 100%;
    text-align: center;
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    margin: 0 0.25rem;
}
.otp-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.otp-inputs {
    margin: 2rem 0;
}
.btn-link {
    text-decoration: none;
    padding: 0;
}
.btn-link:hover {
    text-decoration: underline;
}
</style>
@endpush

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const otpInputs = document.querySelectorAll('.otp-input');
    const form = document.getElementById('otpForm');
    otpInputs[0].focus();
    function focusNextInput(currentInput) {
        const currentIndex = Array.from(otpInputs).indexOf(currentInput);
        if (currentIndex < otpInputs.length - 1) {
            otpInputs[currentIndex + 1].focus();
        }
    }
    function focusPrevInput(currentInput) {
        const currentIndex = Array.from(otpInputs).indexOf(currentInput);
        if (currentIndex > 0) {
            otpInputs[currentIndex - 1].focus();
        }
    }
    otpInputs.forEach((input, index) => {
        input.addEventListener('keyup', function(e) {
            this.value = this.value.replace(/[^0-9]/g, '');
            if (this.value.length === 1) {
                focusNextInput(this);
            }
        });
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace') {
                if (this.value.length === 0) {
                    focusPrevInput(this);
                }
            }
        });
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedData = e.clipboardData.getData('text').slice(0, 6);
            const digits = pastedData.split('');
            digits.forEach((digit, i) => {
                if (otpInputs[i]) {
                    otpInputs[i].value = digit;
                }
            });
            if (digits.length === 6) {
                form.submit();
            }
        });
        input.addEventListener('input', function(e) {
            this.value = this.value.replace(/[^0-9]/g, '');
            if (this.value.length === 1) {
                focusNextInput(this);
            }
        });
    });
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        const otp = Array.from(otpInputs).map(input => input.value).join('');
        if (otp.length === 6) {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'phone_otp';
            hiddenInput.value = otp;
            form.appendChild(hiddenInput);
            form.submit();
        }
    });
});
</script>
@endsection 