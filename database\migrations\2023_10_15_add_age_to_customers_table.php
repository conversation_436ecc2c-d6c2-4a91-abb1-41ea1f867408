<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('customers') && !Schema::hasColumn('customers', 'age')) {
            Schema::table('customers', function (Blueprint $table) {
                if (Schema::hasColumn('customers', 'contact_no')) {
                    $table->unsignedTinyInteger('age')->after('contact_no')->nullable();
                } else {
                    $table->unsignedTinyInteger('age')->nullable();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            if (Schema::hasColumn('customers', 'age')) {
                $table->dropColumn('age');
            }
        });
    }
};
