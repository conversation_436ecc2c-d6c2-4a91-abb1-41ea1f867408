@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card-box">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="header-title">Advertisement Materials</h4>
                    <div>
                        <a href="{{ route('admaterial.bulk-upload') }}" class="btn btn-success mr-2">
                            <i class="fas fa-upload mr-1"></i> Bulk Upload
                        </a>
                    <a href="{{ route('admaterial.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus-circle mr-1"></i> Add New Material
                    </a>
                    </div>
                </div>

                @if (session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                @endif

                <div class="table-responsive">
                    <table id="datatable-buttons" class="table table-striped table-bordered dt-responsive nowrap"
                        style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Preview</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($materials as $material)
                                <tr>
                                    <td>#{{ $material->id }}</td>
                                    <td>{{ $material->name }}</td>
                                    <td>
                                        <span class="badge badge-{{ $material->type === 'image' ? 'info' : 'danger' }}">
                                            {{ ucfirst($material->type) }}
                                        </span>
                                        @if ($material->type === 'video' && $material->duration)
                                            <span class="badge badge-light">{{ gmdate('i:s', $material->duration) }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if ($material->type === 'image')
                                            <a href="{{ env('APP_URL') . $material->file_url }}" target="_blank"
                                                class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-image mr-1"></i> View
                                            </a>
                                        @else
                                            <a href="{{ env('APP_URL') . $material->file_url }}" target="_blank"
                                                class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-video mr-1"></i> Play
                                            </a>
                                        @endif
                                    </td>
                                    <td>{{ $material->created_at->format('M d, Y') }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('admaterial.show', $material->id) }}"
                                                class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admaterial.edit', $material->id) }}"
                                                class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger"
                                                onclick="confirmDelete('{{ $material->id }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <form id="delete-form-{{ $material->id }}"
                                                action="{{ route('admaterial.destroy', $material->id) }}" method="POST"
                                                style="display: none;">
                                                @csrf
                                                @method('DELETE')
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">No advertisement materials found</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function confirmDelete(id) {
            if (confirm('Are you sure you want to delete this material? This action cannot be undone.')) {
                document.getElementById('delete-form-' + id).submit();
            }
        }
    </script>
@endsection
