<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">Edit Role: <?php echo e($role->name); ?></h4>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(route('role.update', $role)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <div class="mb-3">
                                <label for="name" class="form-label">Role Name</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="name" name="name" value="<?php echo e(old('name', $role->name)); ?>" required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="description" name="description"
                                    rows="3"><?php echo e(old('description', $role->description)); ?></textarea>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Permissions</label>
                                <p class="text-muted small mb-3">Select the permissions you want to assign to this role.
                                    Permissions are organized by category for easier management.</p>

                                <!-- Select All Controls -->
                                <div class="mb-3 p-3 bg-light rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-bold">Quick Actions:</span>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-primary me-2"
                                                onclick="selectAllPermissions()">
                                                <i class="fe-check-square"></i> Select All
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                                onclick="deselectAllPermissions()">
                                                <i class="fe-square"></i> Deselect All
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Permission Groups -->
                                <div class="accordion" id="permissionAccordion">
                                    <?php $__currentLoopData = $groupedPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $groupName => $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="accordion-item mb-2">
                                            <h2 class="accordion-header" id="heading<?php echo e(Str::slug($groupName)); ?>">
                                                <button class="accordion-button <?php echo e($loop->first ? '' : 'collapsed'); ?>"
                                                    type="button" data-bs-toggle="collapse"
                                                    data-bs-target="#collapse<?php echo e(Str::slug($groupName)); ?>"
                                                    aria-expanded="<?php echo e($loop->first ? 'true' : 'false'); ?>"
                                                    aria-controls="collapse<?php echo e(Str::slug($groupName)); ?>">
                                                    <div class="d-flex align-items-center">
                                                        <i class="<?php echo e($group['icon']); ?> me-2 text-primary"></i>
                                                        <div>
                                                            <strong><?php echo e($groupName); ?></strong>
                                                            <small
                                                                class="d-block text-muted"><?php echo e($group['description']); ?></small>
                                                        </div>
                                                        <span
                                                            class="badge bg-secondary ms-auto me-3"><?php echo e(count($group['permissions'])); ?>

                                                            permissions</span>
                                                    </div>
                                                </button>
                                            </h2>
                                            <div id="collapse<?php echo e(Str::slug($groupName)); ?>"
                                                class="accordion-collapse collapse <?php echo e($loop->first ? 'show' : ''); ?>"
                                                aria-labelledby="heading<?php echo e(Str::slug($groupName)); ?>"
                                                data-bs-parent="#permissionAccordion">
                                                <div class="accordion-body">
                                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                                        <span class="text-muted"><?php echo e($group['description']); ?></span>
                                                        <div>
                                                            <button type="button"
                                                                class="btn btn-sm btn-outline-success me-1"
                                                                onclick="selectGroupPermissions('<?php echo e(Str::slug($groupName)); ?>')">
                                                                <i class="fe-check"></i> Select All
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                                onclick="deselectGroupPermissions('<?php echo e(Str::slug($groupName)); ?>')">
                                                                <i class="fe-x"></i> Deselect All
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <?php $__currentLoopData = $group['permissions']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="col-md-6 col-lg-4 mb-3">
                                                                <div
                                                                    class="form-check p-3 border rounded <?php echo e(in_array($permission->id, $rolePermissions) ? 'border-primary bg-primary bg-opacity-10' : 'border-light'); ?>">
                                                                    <input
                                                                        class="form-check-input permission-checkbox group-<?php echo e(Str::slug($groupName)); ?>"
                                                                        type="checkbox" name="permissions[]"
                                                                        value="<?php echo e($permission->id); ?>"
                                                                        id="permission_<?php echo e($permission->id); ?>"
                                                                        <?php echo e(in_array($permission->id, $rolePermissions) ? 'checked' : ''); ?>>
                                                                    <label class="form-check-label w-100"
                                                                        for="permission_<?php echo e($permission->id); ?>">
                                                                        <div class="fw-bold"><?php echo e($permission->name); ?></div>
                                                                        <small
                                                                            class="text-muted"><?php echo e($permission->description); ?></small>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="<?php echo e(route('role.index')); ?>" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Update Role</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        // Select all permissions
        function selectAllPermissions() {
            document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
                checkbox.checked = true;
                updateCheckboxStyle(checkbox);
            });
        }

        // Deselect all permissions
        function deselectAllPermissions() {
            document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
                checkbox.checked = false;
                updateCheckboxStyle(checkbox);
            });
        }

        // Select all permissions in a specific group
        function selectGroupPermissions(groupSlug) {
            document.querySelectorAll('.group-' + groupSlug).forEach(function(checkbox) {
                checkbox.checked = true;
                updateCheckboxStyle(checkbox);
            });
        }

        // Deselect all permissions in a specific group
        function deselectGroupPermissions(groupSlug) {
            document.querySelectorAll('.group-' + groupSlug).forEach(function(checkbox) {
                checkbox.checked = false;
                updateCheckboxStyle(checkbox);
            });
        }

        // Update checkbox styling based on checked state
        function updateCheckboxStyle(checkbox) {
            const container = checkbox.closest('.form-check');
            if (checkbox.checked) {
                container.classList.remove('border-light');
                container.classList.add('border-primary', 'bg-primary', 'bg-opacity-10');
            } else {
                container.classList.remove('border-primary', 'bg-primary', 'bg-opacity-10');
                container.classList.add('border-light');
            }
        }

        // Add event listeners to all checkboxes for styling updates
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    updateCheckboxStyle(this);
                });
            });

            // Update selected count
            updateSelectedCount();
        });

        // Update selected permissions count
        function updateSelectedCount() {
            const totalPermissions = document.querySelectorAll('.permission-checkbox').length;
            const selectedPermissions = document.querySelectorAll('.permission-checkbox:checked').length;

            // You can add a counter display here if needed
            console.log(`Selected ${selectedPermissions} of ${totalPermissions} permissions`);
        }

        // Add change event to update count
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('permission-checkbox')) {
                updateSelectedCount();
            }
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\websocket-api\resources\views/admin/roles/edit.blade.php ENDPATH**/ ?>