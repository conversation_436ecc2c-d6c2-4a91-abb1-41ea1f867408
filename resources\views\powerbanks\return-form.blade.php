<div class="card">
    <div class="card-header">
        <h5 class="card-title">Return Powerbank</h5>
    </div>
    <div class="card-body">
        <form action="{{ route('powerbanks.return', $powerbank->id) }}" method="POST">
            @csrf
            <div class="form-group">
                <label for="return_charge">Current Charge (%)</label>
                <input type="number" class="form-control @error('return_charge') is-invalid @enderror" id="return_charge"
                    name="return_charge" value="{{ old('return_charge', $powerbank->current_charge) }}" required>
                @error('return_charge')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="form-group">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="needs_charging" name="needs_charging"
                        value="1">
                    <label class="custom-control-label" for="needs_charging">Needs Charging</label>
                </div>
            </div>
            <button type="submit" class="btn btn-primary">Return Powerbank</button>
        </form>
    </div>
</div>
