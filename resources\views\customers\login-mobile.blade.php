@extends('layouts.customer')

@section('content')
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Customer Login</h4>
                    </div>
                    <div class="card-body p-4">
                        <p class="text-muted mb-4">Enter your mobile number to receive a WhatsApp OTP</p>
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        <form action="{{ route('customer.login.mobile.submit') }}" method="POST" class="needs-validation"
                            novalidate>
                            @csrf
                            <div class="col-12 col-md-6">
                                <label for="phone" class="form-label">Phone Number:</label>
                                <input id="phone" type="tel" name="contact_no"
                                    class="form-control form-control-lg phonenum" maxlength="80" placeholder="Phone Number"
                                    value="{{ old('contact_no') }}" required>
                                <div class="form-text">We'll send a verification code to this phone number</div>
                            </div>
                            <!-- Submit Button -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    Send OTP <i class="bi bi-whatsapp"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <p class="text-muted">
                        Don't have an account? <a href="{{ route('customer.register') }}"
                            class="text-decoration-none">Register now</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        // COUNTRY CODE
        const inputs = document.querySelectorAll(".phonenum");

        inputs.forEach(input => {
            window.intlTelInput(input, {
                initialCountry: "pk",
                separateDialCode: true,
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js", // For formatting
            });
        });
        // COUNTRY CODE
    </script>
@endsection
