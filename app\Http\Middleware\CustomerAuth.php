<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CustomerAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$request->session()->has('verified_customer_id')) {
            return redirect()->route('customer.login')
                ->with('error', 'Please login to continue.');
        }

        // Check if this is an API request
        if ($request->expectsJson() || $request->is('api/*')) {
            return $next($request);
        }

        // For regular requests, get customer info
        $customerId = $request->session()->get('verified_customer_id');
        $customer = \App\Models\Customer::find($customerId);

        if (!$customer) {
            $request->session()->forget('verified_customer_id');
            return redirect()->route('customer.login')
                ->with('error', 'Customer account not found. Please login again.');
        }

        // Share customer data with all views
        view()->share('customer', $customer);

        return $next($request);
    }
}
