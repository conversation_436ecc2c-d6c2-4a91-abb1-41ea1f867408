<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ad_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->time('start_time')->default('00:00');
            $table->time('end_time')->default('23:59');
            $table->boolean('is_active')->default(true);
            $table->integer('priority')->default(0);
            $table->timestamps();
        });

        Schema::create('ad_plan_materials', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ad_plan_id')->constrained()->onDelete('cascade');
            $table->foreignId('ad_material_id')->constrained()->onDelete('cascade');
            $table->integer('display_order')->default(0);
            $table->integer('display_time')->nullable(); // Allow NULL for videos
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ad_plan_materials');
        Schema::dropIfExists('ad_plans');
    }
};
