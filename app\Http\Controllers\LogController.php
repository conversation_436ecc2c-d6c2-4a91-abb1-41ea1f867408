<?php

namespace App\Http\Controllers;

use App\Models\CommunicationLog;
use Illuminate\Http\Request;

class LogController extends Controller
{
    public function index()
    {
        return response()->json(CommunicationLog::latest()->paginate(50));
    }

    public function showByDevice($sn)
    {
        return response()->json(
            CommunicationLog::where('device_sn', $sn)->latest()->paginate(50)
        );
    }
}
