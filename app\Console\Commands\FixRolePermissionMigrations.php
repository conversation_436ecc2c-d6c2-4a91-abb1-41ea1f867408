<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;

class FixRolePermissionMigrations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrations:fix-roles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix role and permission migration issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting role and permission migration fix process...');

        // 1. Check for duplicate migrations
        $this->checkDuplicateMigrations();

        // 2. Fix migration entries in the migrations table
        $this->fixMigrationEntries();

        // 3. Check for table conflicts
        $this->checkTableConflicts();

        $this->info('Role and permission migration fix process completed!');
        
        return Command::SUCCESS;
    }

    /**
     * Check for duplicate migrations.
     */
    private function checkDuplicateMigrations()
    {
        $this->info('Checking for duplicate role and permission migrations...');
        
        $migrationFiles = File::glob(database_path('migrations/*_create_*role*_table.php'));
        $migrationFiles = array_merge($migrationFiles, File::glob(database_path('migrations/*_create_*permission*_table.php')));
        
        $migrations = [];
        $duplicates = [];
        
        foreach ($migrationFiles as $file) {
            $filename = basename($file);
            $this->info("Found migration: {$filename}");
            
            // Check file content to determine what tables it creates
            $content = File::get($file);
            $tables = [];
            
            if (strpos($content, 'create(\'roles\'') !== false) {
                $tables[] = 'roles';
            }
            if (strpos($content, 'create(\'permissions\'') !== false) {
                $tables[] = 'permissions';
            }
            if (strpos($content, 'create(\'role_user\'') !== false || 
                strpos($content, 'create(\'user_role\'') !== false) {
                $tables[] = 'role_user';
            }
            if (strpos($content, 'create(\'permission_role\'') !== false || 
                strpos($content, 'create(\'role_permission\'') !== false) {
                $tables[] = 'permission_role';
            }
            
            foreach ($tables as $table) {
                if (isset($migrations[$table])) {
                    $duplicates[$table][] = $filename;
                    $duplicates[$table][] = $migrations[$table];
                } else {
                    $migrations[$table] = $filename;
                }
            }
        }
        
        if (count($duplicates) > 0) {
            $this->warn('Found duplicate migrations:');
            foreach ($duplicates as $table => $files) {
                $this->warn("- Table '{$table}' is created in: " . implode(', ', array_unique($files)));
            }
            
            $this->info('Recommendation: Keep only the most comprehensive migration and delete or disable others.');
        } else {
            $this->info('No duplicate migrations found.');
        }
    }

    /**
     * Fix migration entries in the migrations table.
     */
    private function fixMigrationEntries()
    {
        $this->info('Checking migration entries in the migrations table...');
        
        if (Schema::hasTable('migrations')) {
            // Get all role and permission related migrations
            $roleMigrations = DB::table('migrations')
                ->where('migration', 'like', '%role%')
                ->orWhere('migration', 'like', '%permission%')
                ->get();
                
            if ($roleMigrations->count() > 0) {
                $this->info('Found role and permission related migrations:');
                
                foreach ($roleMigrations as $migration) {
                    $this->info("- {$migration->migration}");
                    
                    // Check if the migration file exists
                    $migrationFile = database_path("migrations/{$migration->migration}.php");
                    if (!File::exists($migrationFile)) {
                        $this->warn("  Migration file does not exist: {$migrationFile}");
                    }
                }
            } else {
                $this->info('No role and permission related migrations found in the migrations table.');
            }
        } else {
            $this->warn('Migrations table does not exist yet.');
        }
    }

    /**
     * Check for table conflicts.
     */
    private function checkTableConflicts()
    {
        $this->info('Checking for table conflicts...');
        
        $tables = [
            'roles' => 'Roles table',
            'permissions' => 'Permissions table',
            'role_user' => 'Role-User pivot table',
            'user_role' => 'User-Role pivot table (alternative name)',
            'permission_role' => 'Permission-Role pivot table',
            'role_permission' => 'Role-Permission pivot table (alternative name)',
        ];
        
        foreach ($tables as $table => $description) {
            if (Schema::hasTable($table)) {
                $this->info("- {$description} ({$table}) exists");
                
                // Check column structure
                $columns = Schema::getColumnListing($table);
                $this->info("  Columns: " . implode(', ', $columns));
                
                // Check for unique constraints on pivot tables
                if (in_array($table, ['role_user', 'user_role', 'permission_role', 'role_permission'])) {
                    $sm = Schema::getConnection()->getDoctrineSchemaManager();
                    $indexes = $sm->listTableIndexes($table);
                    
                    $hasUniqueConstraint = false;
                    foreach ($indexes as $name => $index) {
                        if ($index->isUnique() && count($index->getColumns()) > 1) {
                            $hasUniqueConstraint = true;
                            $this->info("  Has unique constraint: {$name} on columns " . implode(', ', $index->getColumns()));
                        }
                    }
                    
                    if (!$hasUniqueConstraint) {
                        $this->warn("  Missing unique constraint on relationship columns");
                    }
                }
            } else {
                $this->info("- {$description} ({$table}) does not exist");
            }
        }
        
        // Check for conflicts between alternative table names
        if (Schema::hasTable('role_user') && Schema::hasTable('user_role')) {
            $this->warn('Both role_user and user_role tables exist. This may cause confusion.');
            
            // Compare row counts
            $roleUserCount = DB::table('role_user')->count();
            $userRoleCount = DB::table('user_role')->count();
            
            $this->info("  role_user has {$roleUserCount} rows");
            $this->info("  user_role has {$userRoleCount} rows");
            
            $this->info('Recommendation: Merge data into role_user and drop user_role.');
        }
        
        if (Schema::hasTable('permission_role') && Schema::hasTable('role_permission')) {
            $this->warn('Both permission_role and role_permission tables exist. This may cause confusion.');
            
            // Compare row counts
            $permissionRoleCount = DB::table('permission_role')->count();
            $rolePermissionCount = DB::table('role_permission')->count();
            
            $this->info("  permission_role has {$permissionRoleCount} rows");
            $this->info("  role_permission has {$rolePermissionCount} rows");
            
            $this->info('Recommendation: Merge data into permission_role and drop role_permission.');
        }
    }
}