@extends('layouts.master')

@section('title', 'Manage User Roles')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Manage Roles for User: {{ $user->name }}</h4>
                        <div class="card-tools">
                            <a href="{{ route('user.index') }}" class="btn btn-secondary">
                                <i class="fe-arrow-left mr-1"></i> Back to Users
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>User Information</h5>
                                <table class="table">
                                    <tr>
                                        <th style="width: 150px;">Name</th>
                                        <td>{{ $user->name }}</td>
                                    </tr>
                                    <tr>
                                        <th>Email</th>
                                        <td>{{ $user->email }}</td>
                                    </tr>
                                    <tr>
                                        <th>Registered</th>
                                        <td>{{ $user->created_at->format('M d, Y H:i') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <form action="{{ route('users.roles.update', $user) }}" method="POST">
                            @csrf
                            @method('PUT')

                            <div class="row">
                                <div class="col-md-12">
                                    <h5>Assign Roles</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th style="width: 50px;">Assign</th>
                                                    <th>Role</th>
                                                    <th>Description</th>
                                                    <th>Permissions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($roles as $role)
                                                    <tr>
                                                        <td class="text-center">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" class="custom-control-input"
                                                                    id="role_{{ $role->id }}" name="roles[]"
                                                                    value="{{ $role->id }}"
                                                                    {{ in_array($role->id, $userRoles) ? 'checked' : '' }}>
                                                                <label class="custom-control-label"
                                                                    for="role_{{ $role->id }}"></label>
                                                            </div>
                                                        </td>
                                                        <td>{{ $role->name }}</td>
                                                        <td>{{ $role->description }}</td>
                                                        <td>
                                                            @if ($role->permissions->count() > 0)
                                                                @foreach ($role->permissions as $permission)
                                                                    <span
                                                                        class="badge badge-info mr-1">{{ $permission->name }}</span>
                                                                @endforeach
                                                            @else
                                                                <span class="text-muted">No permissions</span>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="text-right mt-3">
                                <a href="{{ route('user.index') }}" class="btn btn-secondary mr-2">Cancel</a>
                                <button type="submit" class="btn btn-primary">Update Roles</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
