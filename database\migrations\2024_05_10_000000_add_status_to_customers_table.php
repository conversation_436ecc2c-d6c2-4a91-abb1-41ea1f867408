<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if customers table exists first
        if (Schema::hasTable('customers')) {
            Schema::table('customers', function (Blueprint $table) {
                if (!Schema::hasColumn('customers', 'status')) {
                    // Try to add after contact_no, but fallback to just adding if contact_no doesn't exist
                    if (Schema::hasColumn('customers', 'contact_no')) {
                        $table->boolean('status')->default(1)->after('contact_no');
                    } else {
                        $table->boolean('status')->default(1);
                    }
                }
                if (!Schema::hasColumn('customers', 'notes')) {
                    if (Schema::hasColumn('customers', 'status')) {
                        $table->text('notes')->nullable()->after('status');
                    } else {
                        $table->text('notes')->nullable();
                    }
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            if (Schema::hasColumn('customers', 'status')) {
                $table->dropColumn('status');
            }
            if (Schema::hasColumn('customers', 'notes')) {
                $table->dropColumn('notes');
            }
        });
    }
};
