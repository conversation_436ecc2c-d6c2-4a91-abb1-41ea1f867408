<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('device_data', function (Blueprint $table) {
            $table->id();
            $table->string('sn')->index(); // Device serial number
            $table->string('cmd'); // Command type
            $table->string('msg'); // Message
            $table->string('aims'); // AIMS identifier
            $table->integer('n'); // Sequence number
            $table->integer('r'); // Response code
            $table->json('data'); // JSON data
            $table->string('st'); // Status
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('device_data');
    }
};
