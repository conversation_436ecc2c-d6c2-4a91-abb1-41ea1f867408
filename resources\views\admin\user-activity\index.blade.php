@extends('layouts.master')

@section('title', 'User Activity Log')

@section('styles')
    <style>
        .user-avatar {
            width: 32px;
            height: 32px;
            object-fit: cover;
        }

        .activity-user {
            min-width: 200px;
        }

        .activity-description {
            max-width: 300px;
            word-wrap: break-word;
        }

        .ip-address {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
        }

        .action-badge {
            font-size: 0.75em;
            padding: 0.25em 0.6em;
        }

        .module-badge {
            font-size: 0.7em;
            padding: 0.2em 0.5em;
        }

        .btn-group .dropdown-menu {
            min-width: 200px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .table td {
            vertical-align: middle;
        }

        .dropdown-toggle-split {
            padding-left: 0.375rem !important;
            padding-right: 0.375rem !important;
            border-left: 1px solid rgba(255, 255, 255, 0.3) !important;
            min-width: 20px !important;
        }

        .dropdown-toggle-split::after {
            margin-left: 0;
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }

        .btn-group>.btn:not(:first-child) {
            border-left: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        .btn-group .btn {
            position: relative;
            z-index: 1;
        }

        .btn-group .dropdown-toggle-split {
            background-color: inherit !important;
            border-color: inherit !important;
        }

        .dropdown-header {
            font-size: 0.75rem;
            font-weight: 600;
            color: #6c757d;
            padding: 0.5rem 1rem 0.25rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .dropdown-item i {
            width: 16px;
            margin-right: 8px;
        }
    </style>
@endsection

@section('content')
    <div class="container-fluid">
        <!-- Page Heading -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <h1 class="h3 mb-0 text-gray-800">User Activity Log</h1>
            <div>
                @can('manage-user-activity')
                    <button type="button" class="btn btn-sm btn-success shadow-sm" id="exportBtn">
                        <i class="fas fa-download fa-sm text-white-50"></i> Export to CSV
                    </button>
                    <button type="button" class="btn btn-sm btn-danger shadow-sm" data-toggle="modal"
                        data-target="#clearLogsModal">
                        <i class="fas fa-trash fa-sm text-white-50"></i> Clear Old Logs
                    </button>
                @endcan
            </div>
        </div>

        <!-- Filters Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Filter Activities</h6>
            </div>
            <div class="card-body">
                <form id="filterForm">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="user_id">User</label>
                            <select class="form-control" id="user_id" name="user_id">
                                <option value="">All Users</option>
                                @foreach ($users as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="action">Action</label>
                            <select class="form-control" id="action" name="action">
                                <option value="">All Actions</option>
                                @foreach ($actions as $action)
                                    <option value="{{ $action }}">{{ ucfirst($action) }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="module">Module</label>
                            <select class="form-control" id="module" name="module">
                                <option value="">All Modules</option>
                                @foreach ($modules as $module)
                                    <option value="{{ $module }}">{{ ucfirst($module) }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="date_from">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="date_to">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to">
                        </div>
                        <div class="col-md-3 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-primary mr-2" id="applyFilters">
                                <i class="fas fa-filter"></i> Apply Filters
                            </button>
                            <button type="button" class="btn btn-secondary" id="resetFilters">
                                <i class="fas fa-sync"></i> Reset
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Activities Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Activity Log</h6>
                <div class="text-muted small">
                    <i class="fas fa-info-circle"></i>
                    <strong>Actions Column:</strong> View details, access user profiles, and perform administrative actions
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="activitiesTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Action</th>
                                <th>Module</th>
                                <th>Description</th>
                                <th>IP Address</th>
                                <th>Date & Time</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Clear Logs Modal -->
    @can('manage-user-activity')
        <div class="modal fade" id="clearLogsModal" tabindex="-1" role="dialog" aria-labelledby="clearLogsModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <form action="{{ route('admin.user-activity.clear') }}" method="POST">
                        @csrf
                        <div class="modal-header">
                            <h5 class="modal-title" id="clearLogsModalLabel">Clear Old Activity Logs</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="days">Delete logs older than</label>
                                <select class="form-control" id="days" name="days" required>
                                    <option value="30">30 days</option>
                                    <option value="60">60 days</option>
                                    <option value="90">90 days</option>
                                    <option value="180">180 days</option>
                                    <option value="365">365 days</option>
                                </select>
                            </div>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> Warning: This action cannot be undone. All activity
                                logs older than the selected period will be permanently deleted.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-danger">Clear Logs</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endcan
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            console.log('Initializing enhanced User Activity DataTable...');

            // Initialize DataTable with enhanced configuration
            var table = $('#activitiesTable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('admin.user-activity.data') }}',
                    type: 'GET',
                    data: function(d) {
                        d.user_id = $('#user_id').val();
                        d.action = $('#action').val();
                        d.module = $('#module').val();
                        d.date_from = $('#date_from').val();
                        d.date_to = $('#date_to').val();
                    },
                    error: function(xhr, error, thrown) {
                        console.error('DataTables AJAX error:', error, thrown);
                        console.error('Response:', xhr.responseText);
                        if (typeof toastr !== 'undefined') {
                            toastr.error('Failed to load activity data. Please try again.');
                        } else {
                            alert('Failed to load data: ' + error);
                        }
                    }
                },
                columns: [{
                        data: 'user',
                        name: 'user',
                        title: 'User',
                        orderable: false,
                        searchable: false,
                        width: '200px',
                        className: 'activity-user'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        title: 'Action',
                        width: '100px',
                        className: 'text-center'
                    },
                    {
                        data: 'module',
                        name: 'module',
                        title: 'Module',
                        width: '120px',
                        className: 'text-center'
                    },
                    {
                        data: 'description',
                        name: 'description',
                        title: 'Description',
                        className: 'activity-description'
                    },
                    {
                        data: 'ip_address',
                        name: 'ip_address',
                        title: 'IP Address',
                        width: '130px',
                        className: 'text-center'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at',
                        title: 'Date & Time',
                        width: '150px',
                        className: 'text-center'
                    },
                    {
                        data: 'actions',
                        name: 'actions',
                        title: 'Actions',
                        orderable: false,
                        searchable: false,
                        width: '120px',
                        className: 'text-center'
                    }
                ],
                order: [
                    [5, 'desc']
                ], // Order by date descending
                pageLength: 25,
                lengthMenu: [
                    [10, 25, 50, 100],
                    [10, 25, 50, 100]
                ],
                responsive: true,
                autoWidth: false,
                language: {
                    processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>',
                    emptyTable: "No activity logs found",
                    zeroRecords: "No matching activity logs found",
                    search: "Search activities:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    infoEmpty: "Showing 0 to 0 of 0 entries",
                    infoFiltered: "(filtered from _MAX_ total entries)"
                },
                drawCallback: function(settings) {
                    // Re-initialize tooltips and dropdowns after each draw
                    $('[data-toggle="tooltip"]').tooltip();

                    // Initialize Bootstrap dropdowns
                    $('.dropdown-toggle').dropdown();

                    // Fix dropdown positioning
                    $('.dropdown-toggle').on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Close other dropdowns
                        $('.dropdown-menu').removeClass('show');

                        // Toggle current dropdown
                        $(this).next('.dropdown-menu').toggleClass('show');
                    });

                    // Close dropdown when clicking outside
                    $(document).on('click', function(e) {
                        if (!$(e.target).closest('.btn-group').length) {
                            $('.dropdown-menu').removeClass('show');
                        }
                    });
                }
            });

            // Apply filters
            $('#applyFilters').click(function() {
                table.ajax.reload();
            });

            // Reset filters
            $('#resetFilters').click(function() {
                $('#filterForm')[0].reset();
                table.ajax.reload();
            });

            // Export functionality
            $('#exportBtn').click(function() {
                var params = new URLSearchParams();
                params.append('user_id', $('#user_id').val());
                params.append('action', $('#action').val());
                params.append('module', $('#module').val());
                params.append('date_from', $('#date_from').val());
                params.append('date_to', $('#date_to').val());

                window.location.href = '{{ route('admin.user-activity.export') }}?' + params.toString();
            });

            // Auto-reload table when filters change
            $('#user_id, #action, #module, #date_from, #date_to').change(function() {
                table.ajax.reload();
            });

            // Show success/error messages
            @if (session('success'))
                toastr.success('{{ session('success') }}');
            @endif

            @if (session('error'))
                toastr.error('{{ session('error') }}');
            @endif
        });

        // Copy to clipboard function for IP addresses
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    toastr.success('IP address copied to clipboard!');
                }).catch(function(err) {
                    console.error('Failed to copy: ', err);
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                fallbackCopyTextToClipboard(text);
            }
        }

        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    toastr.success('IP address copied to clipboard!');
                } else {
                    toastr.error('Failed to copy IP address');
                }
            } catch (err) {
                toastr.error('Failed to copy IP address');
            }

            document.body.removeChild(textArea);
        }
    </script>
@endsection
