<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PixelFlow - Free Power Bank Rentals</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary: #10b981;
            --dark: #1f2937;
            --light: #f9fafb;
            --gray: #6b7280;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            line-height: 1.6;
            color: var(--dark);
            background-color: var(--light);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary);
            display: flex;
            align-items: center;
        }

        .logo span {
            color: var(--secondary);
        }

        .nav-links {
            display: flex;
            list-style: none;
        }

        .nav-links li {
            margin-left: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--gray);
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .btn {
            padding: 10px 20px;
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background-color: var(--primary-dark);
        }

        .btn-outline {
            background-color: transparent;
            border: 2px solid var(--primary);
            color: var(--primary);
        }

        .btn-outline:hover {
            background-color: var(--primary);
            color: white;
        }

        .hero {
            padding: 160px 0 80px;
            text-align: center;
            background: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), url('/api/placeholder/1200/600') center/cover;
            position: relative;
        }

        h1 {
            font-size: 48px;
            margin-bottom: 20px;
            color: var(--dark);
        }

        .subtitle {
            font-size: 20px;
            color: var(--white);
            margin-bottom: 40px;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 60px;
        }

        .section {
            padding: 80px 0;
        }

        .section-title {
            font-size: 36px;
            text-align: center;
            margin-bottom: 60px;
        }

        .how-it-works {
            background-color: white;
        }

        .steps {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 30px;
        }

        .step {
            flex: 1;
            min-width: 250px;
            text-align: center;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            background-color: white;
            transition: transform 0.3s;
        }

        .step:hover {
            transform: translateY(-10px);
        }

        .step-icon {
            width: 80px;
            height: 80px;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
        }

        .step h3 {
            margin-bottom: 15px;
            color: var(--dark);
        }

        .features {
            background-color: #f3f4f6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .feature-icon {
            font-size: 24px;
            color: var(--primary);
            margin-bottom: 20px;
        }

        .feature h3 {
            margin-bottom: 15px;
        }

        .cta {
            text-align: center;
            background: linear-gradient(45deg, var(--primary), var(--primary-dark));
            color: white;
        }

        .cta h2 {
            margin-bottom: 30px;
        }

        .cta .btn {
            background-color: white;
            color: var(--primary);
            font-weight: bold;
        }

        .cta .btn:hover {
            background-color: var(--light);
        }

        .ad-mockup {
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 60px;
        }

        .ad-mockup h3 {
            margin-bottom: 20px;
            font-size: 24px;
        }

        .ad-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .ad-stat {
            text-align: center;
        }

        .ad-stat .number {
            font-size: 36px;
            font-weight: bold;
            color: var(--primary);
            display: block;
        }

        .ad-stat .label {
            color: var(--gray);
        }

        .locations {
            background-color: white;
        }

        .map-container {
            height: 400px;
            background-color: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }

        .map-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        footer {
            background-color: var(--dark);
            color: white;
            padding: 60px 0 30px;
        }

        .footer-content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: white;
        }

        .footer-logo span {
            color: var(--secondary);
        }

        .footer-contact p {
            margin-bottom: 10px;
        }

        .footer-links h4 {
            margin-bottom: 20px;
            font-size: 18px;
        }

        .footer-links ul {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            text-decoration: none;
            color: #d1d5db;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
            margin-top: 40px;
            text-align: center;
            color: var(--gray);
        }

        .footer-legal-links {
            margin-top: 10px;
        }

        .footer-legal-links a {
            color: var(--gray);
            text-decoration: none;
            margin: 0 10px;
            transition: color 0.3s;
        }

        .footer-legal-links a:hover {
            color: var(--primary);
        }

        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .social-links a {
            display: inline-block;
            width: 40px;
            height: 40px;
            background-color: #374151;
            border-radius: 50%;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }

        .social-links a:hover {
            background-color: var(--primary);
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 36px;
            }

            .section-title {
                font-size: 30px;
            }

            .nav-links {
                display: none;
            }

            .hero {
                padding: 120px 0 60px;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .steps {
                flex-direction: column;
            }
        }
    </style>
</head>

<body>
    <header>
        <div class="container">
            <nav>
                <div class="logo">Pixel<span>Flow</span></div>
                <ul class="nav-links">
                    <li><a href="#how-it-works">How It Works</a></li>
                    <li><a href="#features">Features</a></li>
                    <li><a href="#for-advertisers">For Advertisers</a></li>
                    <li><a href="#locations">Locations</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
                <!-- <a href="#download" class="btn">Download App</a> -->
                <ul class="nav-links">
                <li class="nav-item">
                    <a class="nav-link" href="{{ route('customer.login') }}">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        {{ __('Login') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('customer.register') }}">
                            <i class="fas fa-user-plus me-1"></i>
                            {{ __('Register') }}
                        </a>
                    </li>
            </ul>

            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <h1>Free Power Bank Rentals <br>Anywhere, Anytime</h1>
            <p class="subtitle">Never run out of battery again. Grab a free power bank from any PixelFlow station and
                return it to any location nationwide.</p>
            <div class="hero-buttons">
                <!-- <a href="#download" class="btn">Download App</a> -->
                <a href="#locations" class="btn btn-outline">Find Stations</a>
            </div>
            <img src="{{ URL::asset('assets/images/pixelflow_powerbank.jpg') }}" alt="PixelFlow Power Bank Station"
                style="max-width: 100%; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.15);">
        </div>
    </section>

    <section id="how-it-works" class="section how-it-works">
        <div class="container">
            <h2 class="section-title">How It Works</h2>
            <div class="steps">
                <div class="step">
                    <div class="step-icon">1</div>
                    <h3>Scan QR Code</h3>
                    <p>Find a PixelFlow station near you and scan the QR code with your phone camera or our app.</p>
                </div>
                <div class="step">
                    <div class="step-icon">2</div>
                    <h3>Get Your Power Bank</h3>
                    <p>A fully charged power bank will be dispensed automatically from the station.</p>
                </div>
                <div class="step">
                    <div class="step-icon">3</div>
                    <h3>Use & Return</h3>
                    <p>Charge your device and return the power bank to any PixelFlow station when done.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="features" class="section features">
        <div class="container">
            <h2 class="section-title">Why Choose PixelFlow</h2>
            <div class="features-grid">
                <div class="feature">
                    <div class="feature-icon">💰</div>
                    <h3>Completely Free</h3>
                    <p>Our service is 100% free for users. No rental fees, no subscriptions, no hidden costs.</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔋</div>
                    <h3>High-Capacity Power Banks</h3>
                    <p>Our power banks feature fast charging technology and enough capacity to fully charge most
                        smartphones multiple times.</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🌎</div>
                    <h3>Return Anywhere</h3>
                    <p>Return your power bank to any PixelFlow station in any city where our service is available.</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔒</div>
                    <h3>Secure & Sanitary</h3>
                    <p>All power banks are automatically sanitized when returned and securely locked into our stations.
                    </p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🚶</div>
                    <h3>Conveniently Located</h3>
                    <p>Find our stations in malls, airports, cafes, train stations, and other high-traffic areas.</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📱</div>
                    <h3>Universal Compatibility</h3>
                    <p>Our power banks work with all modern smartphones and USB-C devices.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="for-advertisers" class="section cta">
        <div class="container">
            <h2>For Advertisers</h2>
            <p class="subtitle">Reach your target audience with high-visibility digital advertising on our 43-inch
                displays in prime locations.</p>
            <a href="#contact" class="btn">Advertise With Us</a>

            <div class="ad-mockup">
                <h3>Why Advertise on PixelFlow Stations?</h3>
                <p>Our strategically placed stations feature 43-inch high-definition displays that capture attention in
                    high-traffic areas. With users actively engaging with our stations, your ads receive maximum
                    visibility.</p>

                <div class="ad-stats">
                    <div class="ad-stat">
                        <span class="number">1M+</span>
                        <span class="label">Monthly Views</span>
                    </div>
                    <div class="ad-stat">
                        <span class="number">200+</span>
                        <span class="label">Prime Locations</span>
                    </div>
                    <div class="ad-stat">
                        <span class="number">30s</span>
                        <span class="label">Average View Time</span>
                    </div>
                    <div class="ad-stat">
                        <span class="number">24/7</span>
                        <span class="label">Advertisement Display</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="locations" class="section locations">
        <div class="container">
            <h2 class="section-title">Our Location</h2>
            <div class="map-container">
                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3621.0634079912875!2d67.0259622!3d24.827505099999996!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3eb33dc16826b2bb%3A0xb0946ab1735b5110!2sMarina%20View!5e0!3m2!1sen!2s!4v1750831697305!5m2!1sen!2s" width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
            </div>
        </div>
    </section>

    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div>
                    <div class="footer-logo">Pixel<span>Flow</span></div>
                    <p>Free power bank rentals supported by advertising. <br>Never run out of battery again.</p>
                    <div class="social-links">
                        <a href="https://www.facebook.com/share/1Hy8fF8huc/" aria-label="Facebook">f</a>
                        <a href="https://www.instagram.com/pixel_flowpk?igsh=YzljYTk1ODg3Zg==" aria-label="Instagram">i</a>
                        <a href="https://www.linkedin.com/company/pixel-flow-pakistan/" aria-label="LinkedIn">in</a>
                    </div>
                </div>
<!-- 
                <div class="footer-links">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Our Team</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Press</a></li>
                    </ul>
                </div>

                <div class="footer-links">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="#">For Users</a></li>
                        <li><a href="#">For Advertisers</a></li>
                        <li><a href="#">For Venue Partners</a></li>
                        <li><a href="#">API Access</a></li>
                    </ul>
                </div> -->

                <div class="footer-links">
                    <h4>Legal Policies</h4>
                    <ul>
                        <li><a href="{{ route('terms.of.use') }}">Terms of Use</a></li>
                        <li><a href="{{ route('privacy.policy') }}">Privacy Policy</a></li>
                        <li><a href="{{ route('cookie.policy') }}">Cookie Policy</a></li>
                    </ul>
                </div>

                <div class="footer-contact">
                    <h4>Contact Us</h4>
                    <p>Email: <EMAIL></p>
                    <p>Email: <EMAIL></p>
                    <p>Marina View, D-76, Block-7, Clifton, Karachi</p>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 PixelFlow. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>

</html>