@extends('layouts.admin')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="card-title">Audit Trail Details</h4>
                        <a href="{{ route('admin.audit-trail.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">ID</th>
                                        <td>{{ $auditTrail->id }}</td>
                                    </tr>
                                    <tr>
                                        <th>User</th>
                                        <td>{{ $auditTrail->user ? $auditTrail->user->name : 'System' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Model Type</th>
                                        <td>{{ class_basename($auditTrail->auditable_type) }}</td>
                                    </tr>
                                    <tr>
                                        <th>Model ID</th>
                                        <td>{{ $auditTrail->auditable_id }}</td>
                                    </tr>
                                    <tr>
                                        <th>Action</th>
                                        <td>{{ ucfirst($auditTrail->action) }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">IP Address</th>
                                        <td>{{ $auditTrail->ip_address }}</td>
                                    </tr>
                                    <tr>
                                        <th>User Agent</th>
                                        <td>{{ $auditTrail->user_agent }}</td>
                                    </tr>
                                    <tr>
                                        <th>Created At</th>
                                        <td>{{ $auditTrail->created_at->format('Y-m-d H:i:s') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h5>Old Values</h5>
                                <div class="card">
                                    <div class="card-body">
                                        @if ($auditTrail->old_values)
                                            <pre class="mb-0">{{ json_encode($auditTrail->old_values, JSON_PRETTY_PRINT) }}</pre>
                                        @else
                                            <p class="text-muted mb-0">No old values</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>New Values</h5>
                                <div class="card">
                                    <div class="card-body">
                                        @if ($auditTrail->new_values)
                                            <pre class="mb-0">{{ json_encode($auditTrail->new_values, JSON_PRETTY_PRINT) }}</pre>
                                        @else
                                            <p class="text-muted mb-0">No new values</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
