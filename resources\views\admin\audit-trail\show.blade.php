@extends('layouts.master')

@section('title', 'Audit Trail Details')

@section('content')
    <div class="container-fluid">
        <!-- Page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('admin.audit-trail.index') }}">Audit Trail</a></li>
                            <li class="breadcrumb-item active">Record #{{ $auditTrail->id }}</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Audit Trail Record #{{ $auditTrail->id }}</h4>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">Record Details</h4>

                        <div class="table-responsive">
                            <table class="table table-striped mb-0">
                                <tbody>
                                    <tr>
                                        <th scope="row" style="width: 30%;">ID</th>
                                        <td>{{ $auditTrail->id }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">User</th>
                                        <td>
                                            @if ($auditTrail->user)
                                                <div class="d-flex align-items-center">
                                                    <div class="mr-2">
                                                        <img class="rounded-circle" width="32" height="32"
                                                            src="{{ $auditTrail->user->profile_photo_url ?? asset('assets/images/users/default-avatar.png') }}"
                                                            alt="{{ $auditTrail->user->name }}"
                                                            onerror="this.src='{{ asset('assets/images/users/default-avatar.png') }}'">
                                                    </div>
                                                    <div>
                                                        <div class="font-weight-bold">{{ $auditTrail->user->name }}</div>
                                                        <small class="text-muted">{{ $auditTrail->user->email }}</small>
                                                    </div>
                                                </div>
                                            @else
                                                <div class="d-flex align-items-center">
                                                    <div class="mr-2">
                                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center"
                                                            style="width:32px;height:32px;">
                                                            <i class="fas fa-cog text-white"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="font-weight-bold">System</div>
                                                        <small class="text-muted">Automated Process</small>
                                                    </div>
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Action</th>
                                        <td>
                                            @php
                                                $actionClass = match ($auditTrail->action) {
                                                    'created' => 'success',
                                                    'updated' => 'info',
                                                    'deleted' => 'danger',
                                                    'viewed' => 'secondary',
                                                    'login' => 'primary',
                                                    'logout' => 'warning',
                                                    'failed_login' => 'danger',
                                                    default => 'secondary',
                                                };
                                            @endphp
                                            <span
                                                class="badge badge-{{ $actionClass }}">{{ ucfirst($auditTrail->action) }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Module</th>
                                        <td>
                                            @if ($auditTrail->module)
                                                <span class="badge badge-info">{{ ucfirst($auditTrail->module) }}</span>
                                            @else
                                                <span class="badge badge-secondary">N/A</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Severity</th>
                                        <td>
                                            @php
                                                $severityClass = match ($auditTrail->severity) {
                                                    'low' => 'success',
                                                    'medium' => 'warning',
                                                    'high' => 'danger',
                                                    default => 'secondary',
                                                };
                                            @endphp
                                            <span
                                                class="badge badge-{{ $severityClass }}">{{ ucfirst($auditTrail->severity) }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Notes</th>
                                        <td>{{ $auditTrail->notes ?? 'No notes' }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Date & Time</th>
                                        <td>{{ $auditTrail->created_at->format('Y-m-d H:i:s') }}
                                            ({{ $auditTrail->created_at->diffForHumans() }})</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">IP Address</th>
                                        <td>{{ $auditTrail->ip_address ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">User Agent</th>
                                        <td>
                                            <small class="text-muted">{{ $auditTrail->user_agent ?? 'N/A' }}</small>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                @if ($auditTrail->changes || $auditTrail->old_values || $auditTrail->new_values)
                    <div class="card">
                        <div class="card-body">
                            <h4 class="header-title mb-3">Data Changes</h4>

                            <ul class="nav nav-tabs" id="changesTab" role="tablist">
                                @if ($auditTrail->changes)
                                    <li class="nav-item">
                                        <a class="nav-link active" id="changes-tab" data-toggle="tab" href="#changes"
                                            role="tab">
                                            Changes
                                        </a>
                                    </li>
                                @endif
                                @if ($auditTrail->old_values)
                                    <li class="nav-item">
                                        <a class="nav-link {{ !$auditTrail->changes ? 'active' : '' }}" id="old-values-tab"
                                            data-toggle="tab" href="#old-values" role="tab">
                                            Old Values
                                        </a>
                                    </li>
                                @endif
                                @if ($auditTrail->new_values)
                                    <li class="nav-item">
                                        <a class="nav-link {{ !$auditTrail->changes && !$auditTrail->old_values ? 'active' : '' }}"
                                            id="new-values-tab" data-toggle="tab" href="#new-values" role="tab">
                                            New Values
                                        </a>
                                    </li>
                                @endif
                            </ul>

                            <div class="tab-content p-3" id="changesTabContent">
                                @if ($auditTrail->changes)
                                    <div class="tab-pane fade show active" id="changes" role="tabpanel">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Field</th>
                                                        <th>Old Value</th>
                                                        <th>New Value</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach ($auditTrail->changes as $field => $change)
                                                        <tr>
                                                            <td><strong>{{ $field }}</strong></td>
                                                            <td>{{ is_array($change['old'] ?? null) ? json_encode($change['old']) : $change['old'] ?? 'null' }}
                                                            </td>
                                                            <td>{{ is_array($change['new'] ?? null) ? json_encode($change['new']) : $change['new'] ?? 'null' }}
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                @endif

                                @if ($auditTrail->old_values)
                                    <div class="tab-pane fade {{ !$auditTrail->changes ? 'show active' : '' }}"
                                        id="old-values" role="tabpanel">
                                        <pre class="bg-light p-3 rounded"><code>{{ json_encode($auditTrail->old_values, JSON_PRETTY_PRINT) }}</code></pre>
                                    </div>
                                @endif

                                @if ($auditTrail->new_values)
                                    <div class="tab-pane fade {{ !$auditTrail->changes && !$auditTrail->old_values ? 'show active' : '' }}"
                                        id="new-values" role="tabpanel">
                                        <pre class="bg-light p-3 rounded"><code>{{ json_encode($auditTrail->new_values, JSON_PRETTY_PRINT) }}</code></pre>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">Actions</h4>

                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.audit-trail.index') }}" class="btn btn-primary">
                                <i class="fe-arrow-left mr-1"></i> Back to Audit Trail
                            </a>

                            @if ($auditTrail->user)
                                <a href="{{ route('admin.users.show', $auditTrail->user->id) }}" class="btn btn-info">
                                    <i class="fe-user mr-1"></i> View User Profile
                                </a>
                            @endif

                            {{-- @if ($auditTrail->auditable_type && $auditTrail->auditable_id)
                                <a href="#" class="btn btn-secondary">
                                    <i class="fe-link mr-1"></i> View Related
                                    {{ class_basename($auditTrail->auditable_type) }}
                                </a>
                            @endif --}}
                        </div>
                    </div>
                </div>

                @if ($auditTrail->auditable_type && $auditTrail->auditable_id)
                    <div class="card">
                        <div class="card-body">
                            <h4 class="header-title mb-3">Related Record</h4>

                            <div class="table-responsive">
                                <table class="table table-striped mb-0">
                                    <tbody>
                                        <tr>
                                            <th scope="row">Type</th>
                                            <td>{{ class_basename($auditTrail->auditable_type) }}</td>
                                        </tr>
                                        <tr>
                                            <th scope="row">ID</th>
                                            <td>{{ $auditTrail->auditable_id }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection
