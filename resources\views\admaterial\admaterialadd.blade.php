@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-6">
            <div class="card-box">
                <h4 class="header-title">Add New Advertisement Material</h4>
                <p class="sub-header">
                    Upload image or video files to use in your advertisement plans.
                </p>

                <form method="POST" class="parsley-examples" action="{{ route('admaterial.store') }}"
                    enctype="multipart/form-data">
                    @csrf

                    <div class="form-group">
                        <label for="name">Material Name<span class="text-danger">*</span></label>
                        <input type="text" name="name" parsley-trigger="change" required
                            placeholder="Enter material name" class="form-control" id="name"
                            value="{{ old('name') }}">
                        @error('name')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="type">Material Type<span class="text-danger">*</span></label>
                        <select name="type" id="type" class="form-control" required>
                            <option value="">Select type</option>
                            <option value="image" {{ old('type') == 'image' ? 'selected' : '' }}>Image</option>
                            <option value="video" {{ old('type') == 'video' ? 'selected' : '' }}>Video</option>
                        </select>
                        @error('type')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="file">Upload File<span class="text-danger">*</span></label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="file" name="file" required>
                            <label class="custom-file-label" for="file">Choose file</label>
                        </div>
                        <small class="form-text text-muted" id="file-help">
                            Supported formats: JPEG, PNG, GIF, MP4, WebM, OGG. Max size: 30MB.
                        </small>
                        @error('file')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea name="description" class="form-control" id="description" rows="3"
                            placeholder="Enter material description">{{ old('description') }}</textarea>
                        @error('description')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group text-right mb-0">
                        <button class="btn btn-primary waves-effect waves-light" type="submit">
                            <i class="fas fa-save mr-1"></i> Save Material
                        </button>
                        <a href="{{ route('admaterial.index') }}" class="btn btn-light waves-effect ml-1">
                            <i class="fas fa-arrow-left mr-1"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Show selected filename in custom file input
            $('input[type="file"]').change(function(e) {
                var fileName = e.target.files[0].name;
                $('.custom-file-label').html(fileName);
            });

            // Update help text based on selected type
            $('#type').change(function() {
                if ($(this).val() === 'image') {
                    $('#file-help').text('Supported formats: JPEG, PNG, GIF. Max size: 20MB.');
                } else if ($(this).val() === 'video') {
                    $('#file-help').text('Supported formats: MP4, WebM, OGG. Max size: 20MB.');
                } else {
                    $('#file-help').text(
                        'Supported formats: JPEG, PNG, GIF, MP4, WebM, OGG. Max size: 20MB.');
                }
            });

            // Initialize form validation
            $('.parsley-examples').parsley();
        });
    </script>
@endsection
