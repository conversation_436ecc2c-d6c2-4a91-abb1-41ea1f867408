<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeviceGroup extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'is_active'
    ];

    /**
     * The devices that belong to the group.
     */
    public function devices()
    {
        return $this->belongsToMany(Device::class, 'device_device_group')
                    ->withTimestamps();
    }

    /**
     * The ad plans assigned to this group.
     */
    public function adPlans()
    {
        return $this->belongsToMany(AdPlan::class, 'ad_plan_device_group')
                    ->withTimestamps();
    }
}