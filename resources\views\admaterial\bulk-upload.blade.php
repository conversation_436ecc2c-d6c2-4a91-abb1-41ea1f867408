@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card-box">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="header-title">Bulk Upload Advertisement Materials</h4>
                    <a href="{{ route('admaterial.index') }}" class="btn btn-light waves-effect">
                        <i class="fas fa-arrow-left mr-1"></i> Back to List
                    </a>
                </div>

                <div class="bulk-upload-section">
                    <div class="form-group">
                        <label for="bulk_type">Material Type<span class="text-danger">*</span></label>
                        <select name="bulk_type" id="bulk_type" class="form-control" required>
                            <option value="">Select type</option>
                            <option value="image">Image</option>
                            <option value="video">Video</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="bulk_files">Upload Multiple Files<span class="text-danger">*</span></label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="bulk_files" name="bulk_files[]" multiple>
                            <label class="custom-file-label" for="bulk_files">Choose files</label>
                        </div>
                        <small class="form-text text-muted" id="bulk-file-help">
                            Select multiple files to upload. Supported formats: JPEG, PNG, GIF, MP4, WebM, OGG. Max size: 35MB per file.
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="bulk_description">Description (Optional)</label>
                        <textarea name="bulk_description" class="form-control" id="bulk_description" rows="2"
                            placeholder="Enter description for all materials"></textarea>
                    </div>

                    <div class="form-group">
                        <button type="button" id="bulk-upload-btn" class="btn btn-success waves-effect waves-light">
                            <i class="fas fa-upload mr-1"></i> Upload Selected Files
                        </button>
                    </div>

                    <div id="upload-preview" class="mt-3">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>File Name</th>
                                        <th>Type</th>
                                        <th>Size</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="preview-body"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Show selected filename in custom file input
            $('#bulk_files').change(function(e) {
                const files = this.files;
                const fileNames = Array.from(files).map(file => file.name).join(', ');
                $(this).next('.custom-file-label').html(fileNames || 'Choose files');

                // Update preview table
                const previewBody = $('#preview-body');
                previewBody.empty();

                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const fileType = file.type.split('/')[0];
                    const fileSize = (file.size / (1024 * 1024)).toFixed(2);
                    
                    const row = `
                        <tr>
                            <td>${file.name}</td>
                            <td>${fileType}</td>
                            <td>${fileSize} MB</td>
                            <td><span class="badge badge-info">Pending</span></td>
                        </tr>
                    `;
                    previewBody.append(row);
                }
            });

            // Update help text based on selected type
            $('#bulk_type').change(function() {
                if ($(this).val() === 'image') {
                    $('#bulk-file-help').text('Supported formats: JPEG, PNG, GIF. Max size: 35MB per file.');
                } else if ($(this).val() === 'video') {
                    $('#bulk-file-help').text('Supported formats: MP4, WebM, OGG. Max size: 35MB per file.');
                } else {
                    $('#bulk-file-help').text('Supported formats: JPEG, PNG, GIF, MP4, WebM, OGG. Max size: 35MB per file.');
                }
            });

            // Handle bulk upload
            $('#bulk-upload-btn').click(function() {
                const files = $('#bulk_files')[0].files;
                const type = $('#bulk_type').val();
                const description = $('#bulk_description').val();

                if (!type) {
                    alert('Please select a material type');
                    return;
                }

                if (files.length === 0) {
                    alert('Please select files to upload');
                    return;
                }

                const formData = new FormData();
                for (let i = 0; i < files.length; i++) {
                    formData.append('files[]', files[i]);
                }
                formData.append('type', type);
                formData.append('description', description);
                formData.append('_token', '{{ csrf_token() }}');

                // Disable upload button and show loading state
                const uploadBtn = $(this);
                uploadBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> Uploading...');

                // Send AJAX request
                $.ajax({
                    url: '{{ route("admaterial.bulk-store") }}',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        // Update status for each file
                        response.forEach((result, index) => {
                            const statusCell = $(`#preview-body tr:eq(${index}) td:last-child`);
                            if (result.success) {
                                statusCell.html('<span class="badge badge-success">Success</span>');
                            } else {
                                statusCell.html(`<span class="badge badge-danger">${result.message}</span>`);
                            }
                        });

                        // Show success message
                        alert('Bulk upload completed');
                        
                        // Reset form
                        $('#bulk_files').val('');
                        $('.custom-file-label').html('Choose files');
                        $('#bulk_description').val('');
                        uploadBtn.prop('disabled', false).html('<i class="fas fa-upload mr-1"></i> Upload Selected Files');
                    },
                    error: function(xhr) {
                        alert('Error occurred during upload');
                        uploadBtn.prop('disabled', false).html('<i class="fas fa-upload mr-1"></i> Upload Selected Files');
                    }
                });
            });
        });
    </script>
@endsection 