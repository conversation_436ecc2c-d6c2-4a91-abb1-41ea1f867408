<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('tcp_commands', function (Blueprint $table) {
            $table->id();
            $table->string('device_sn'); // Serial number of the target device
            $table->text('command'); // Command to be sent
            $table->enum('status', ['pending', 'sent', 'failed'])->default('pending'); // Command status
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('tcp_commands');
    }
};
