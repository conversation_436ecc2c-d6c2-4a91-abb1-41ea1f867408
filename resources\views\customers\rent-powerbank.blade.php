@extends('layouts.customer')

@section('content')
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Rent a Powerbank</h5>
                            <a href="{{ route('customer.logout') }}" class="btn btn-sm btn-light">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <h4 class="mb-3">Welcome, {{ $customer->name ?? " Customer"}}!</h4>

                        <div class="alert alert-info mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle me-3 fa-2x"></i>
                                <div>
                                    <h5 class="mb-1">Device Information</h5>
                                    <p class="mb-0"><strong>Device ID:</strong> {{ $device->sn }}</p>
                                    @if ($device->location)
                                        <p class="mb-0"><strong>Location:</strong> {{ $device->location }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>

                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        @if ($powerbanks->count() > 0)
                            <div id="powerbanksContainer">
                                <p class="lead">Select a powerbank to rent:</p>

                                <div class="list-group mt-3" id="powerbanksList">
                                    @foreach ($powerbanks as $powerbank)
                                        @php
                                            $slotNumber =
                                                $powerbank->slot_number % 12 == 0 ? 12 : $powerbank->slot_number % 12;
                                            $chargeClass = '';
                                            if ($powerbank->current_charge >= 80) {
                                                $chargeClass = 'bg-success';
                                            } elseif ($powerbank->current_charge >= 50) {
                                                $chargeClass = 'bg-info';
                                            } elseif ($powerbank->current_charge >= 20) {
                                                $chargeClass = 'bg-warning';
                                            } else {
                                                $chargeClass = 'bg-danger';
                                            }
                                        @endphp

                                        <div class="list-group-item list-group-item-action powerbank-item"
                                            data-powerbank-id="{{ $powerbank->id }}" data-slot-number="{{ $slotNumber }}"
                                            data-aims="{{ $powerbank->aims ?? 0 }}">
                                            <div class="d-flex w-100 justify-content-between align-items-center">
                                                <h5 class="mb-1">Slot {{ $slotNumber }}</h5>
                                                <span
                                                    class="badge {{ $chargeClass }}">{{ $powerbank->current_charge }}%</span>
                                            </div>
                                            <p class="mb-1">Battery ID: {{ $powerbank->battery_id ?? 'N/A' }}</p>
                                            <small class="text-muted">Click to rent this powerbank</small>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <div id="rentalProgressContainer" class="d-none">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="mb-0">Renting Powerbank</h5>
                                    <button type="button" class="btn btn-secondary btn-sm back-to-powerbanks" disabled>
                                        <i class="fas fa-arrow-left"></i> Back
                                    </button>
                                </div>

                                <div class="alert alert-primary">
                                    <p class="mb-0"><strong>Device:</strong> {{ $device->sn }}</p>
                                    <p class="mb-0"><strong>Slot:</strong> <span id="rentingSlotNumber"></span></p>
                                </div>

                                <div class="text-center py-4">
                                    <h4 class="mb-3">Ejecting Powerbank...</h4>
                                    <div class="progress mb-3" style="height: 25px;">
                                        <div id="rentalProgressBar"
                                            class="progress-bar progress-bar-striped progress-bar-animated"
                                            role="progressbar" style="width: 0%">0%</div>
                                    </div>
                                    <p id="rentalStatusMessage" class="lead">Sending command to device...</p>
                                    <p id="countdownTimer" class="text-muted">Please wait: <span
                                            id="secondsRemaining">30</span> seconds</p>
                                </div>
                            </div>

                            <div id="rentalSuccessContainer" class="d-none">
                                <div class="text-center py-4">
                                    <div class="mb-4">
                                        <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                                    </div>
                                    <h3 class="mb-3">Powerbank Rented Successfully!</h3>
                                    <p class="lead">Please take your powerbank from the device.</p>
                                    <div class="alert alert-info mt-3">
                                        <p class="mb-0">Remember to return the powerbank to any device when you're done.
                                        </p>
                                    </div>
                                    <a href="{{ route('powerbanks.available') }}" class="btn btn-primary mt-3">
                                        <i class="fas fa-home"></i> Back to Home
                                    </a>
                                </div>
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> No powerbanks available for rent in this device.
                            </div>
                            <div class="d-grid gap-2 mt-4">
                                <a href="{{ route('powerbanks.available') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Available Devices
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden form for powerbank rental -->
    <form id="rentPowerbankForm" style="display: none;">
        @csrf
        <input type="hidden" name="device_sn" value="{{ $device->sn }}">
        <input type="hidden" name="customer_id" value="{{ $customer->id }}">
        <input type="hidden" name="slot_number" id="form_slot_number">
        <input type="hidden" name="aims" id="form_aims" value="0">
        <input type="hidden" name="powerbank_id" id="form_powerbank_id">
    </form>
@endsection

@push('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .powerbank-item {
            cursor: pointer;
            transition: all 0.2s;
        }

        .powerbank-item:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
        }

        .progress-bar {
            transition: width 1s;
        }
    </style>
@endpush

@push('scripts')
    <script>
        $(document).ready(function() {
            // Auto-select the first available powerbank
            const firstAvailablePowerbank = $('.powerbank-item').first();
            if (firstAvailablePowerbank.length) {
                // Highlight the first powerbank
                firstAvailablePowerbank.addClass('selected-powerbank');

                // Add a "Rent Now" button at the top
                $('#powerbanksContainer').prepend(
                    `<div class="text-center mb-4">
                        <button id="rentNowBtn" class="btn btn-primary btn-lg">
                            <i class="fas fa-bolt"></i> Rent Now
                        </button>
                    </div>`
                );

                // Handle the Rent Now button click
                $('#rentNowBtn').click(function() {
                    // Get data from the selected powerbank
                    const powerbankId = firstAvailablePowerbank.data('powerbank-id');
                    const slotNumber = firstAvailablePowerbank.data('slot-number');
                    const aims = firstAvailablePowerbank.data('aims');

                    // Set form values
                    $('#form_slot_number').val(slotNumber);
                    $('#form_aims').val(aims);
                    $('#form_powerbank_id').val(powerbankId);

                    // Start rental process
                    $('#powerbanksContainer').addClass('d-none');
                    $('#rentalProgressContainer').removeClass('d-none');
                    $('#rentingSlotNumber').text(slotNumber);
                    rentPowerbank();
                });
            }

            // Handle powerbank selection
            $('.powerbank-item').click(function() {
                const powerbankId = $(this).data('powerbank-id');
                const slotNumber = $(this).data('slot-number');
                const aims = $(this).data('aims');

                // Set form values
                $('#form_slot_number').val(slotNumber);
                $('#form_aims').val(aims);
                $('#form_powerbank_id').val(powerbankId);

                // Show rental progress
                $('#powerbanksContainer').addClass('d-none');
                $('#rentalProgressContainer').removeClass('d-none');

                // Set rental info
                $('#rentingSlotNumber').text(slotNumber);

                // Start rental process
                rentPowerbank();
            });

            // Handle back button
            $('.back-to-powerbanks').click(function() {
                if ($(this).prop('disabled')) return;

                // Reset and show powerbanks container
                $('#powerbanksContainer').removeClass('d-none');
                $('#rentalProgressContainer').addClass('d-none');
                $('#rentalSuccessContainer').addClass('d-none');

                // Reset progress
                $('#rentalProgressBar').css('width', '0%').text('0%');
                $('#secondsRemaining').text('30');
                $('#rentalStatusMessage').text('Sending command to device...');
            });

            // Function to rent powerbank
            function rentPowerbank() {
                // Get form data
                const formData = $('#rentPowerbankForm').serialize();

                // Start progress animation
                startCountdown();

                // Send AJAX request
                $.ajax({
                    url: '{{ route('customer.rent-powerbank') }}',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        $('#rentalStatusMessage').text(
                            'Command sent successfully. Powerbank is being ejected...');
                    },
                    error: function(xhr) {
                        let errorMessage = 'An error occurred. Please try again.';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        $('#rentalStatusMessage').text('Error: ' + errorMessage);
                        $('#rentalProgressBar').addClass('bg-danger');
                        $('.back-to-powerbanks').prop('disabled', false);
                    }
                });
            }

            // Function to start countdown
            function startCountdown() {
                let secondsRemaining = 30;
                let progress = 0;

                // Update every second
                const countdownInterval = setInterval(function() {
                    secondsRemaining--;
                    progress = ((30 - secondsRemaining) / 30) * 100;

                    $('#secondsRemaining').text(secondsRemaining);
                    $('#rentalProgressBar').css('width', progress + '%').text(Math.round(progress) + '%');

                    if (secondsRemaining <= 0) {
                        clearInterval(countdownInterval);

                        // Show success container
                        $('#rentalProgressContainer').addClass('d-none');
                        $('#rentalSuccessContainer').removeClass('d-none');
                    }
                }, 1000);
            }
        });
    </script>
@endpush
