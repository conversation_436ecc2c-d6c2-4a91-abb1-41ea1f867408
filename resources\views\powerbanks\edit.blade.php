@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title m-0">Edit Powerbank</h4>
                        <a href="{{ route('powerbanks.show', $powerbank->id) }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i> Back to Details
                        </a>
                    </div>

                    <form action="{{ route('powerbanks.update', $powerbank->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="form-group">
                            <label for="serial_number">Serial Number</label>
                            <input type="text" class="form-control @error('serial_number') is-invalid @enderror"
                                id="serial_number" name="serial_number"
                                value="{{ old('serial_number', $powerbank->serial_number) }}" required>
                            @error('serial_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="device_id">Device</label>
                            <select class="form-control @error('device_id') is-invalid @enderror" id="device_id"
                                name="device_id">
                                <option value="">Select Device (Optional)</option>
                                @foreach ($devices as $device)
                                    <option value="{{ $device->id }}"
                                        {{ old('device_id', $powerbank->device_id) == $device->id ? 'selected' : '' }}>
                                        {{ $device->sn }}
                                    </option>
                                @endforeach
                            </select>
                            @error('device_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="capacity">Capacity (mAh)</label>
                            <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                id="capacity" name="capacity" value="{{ old('capacity', $powerbank->capacity) }}" required>
                            @error('capacity')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="current_charge">Current Charge (%)</label>
                            <input type="number" class="form-control @error('current_charge') is-invalid @enderror"
                                id="current_charge" name="current_charge"
                                value="{{ old('current_charge', $powerbank->current_charge) }}" required>
                            @error('current_charge')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="slot_number">Slot Number</label>
                            <input type="number" class="form-control @error('slot_number') is-invalid @enderror"
                                id="slot_number" name="slot_number"
                                value="{{ old('slot_number', $powerbank->slot_number) }}">
                            @error('slot_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status"
                                required>
                                <option value="available"
                                    {{ old('status', $powerbank->status) == 'available' ? 'selected' : '' }}>Available
                                </option>
                                <option value="rented"
                                    {{ old('status', $powerbank->status) == 'rented' ? 'selected' : '' }}>Rented</option>
                                <option value="charging"
                                    {{ old('status', $powerbank->status) == 'charging' ? 'selected' : '' }}>Charging
                                </option>
                                <option value="maintenance"
                                    {{ old('status', $powerbank->status) == 'maintenance' ? 'selected' : '' }}>Maintenance
                                </option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="charge_cycles">Charge Cycles</label>
                            <input type="number" class="form-control @error('charge_cycles') is-invalid @enderror"
                                id="charge_cycles" name="charge_cycles"
                                value="{{ old('charge_cycles', $powerbank->charge_cycles) }}">
                            @error('charge_cycles')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="aims">AIMS Value</label>
                            <input type="number" class="form-control @error('aims') is-invalid @enderror" id="aims"
                                name="aims" value="{{ old('aims', $powerbank->aims) }}" min="0" max="4">
                            @error('aims')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">AIMS value must be between 0 and 4</small>
                        </div>

                        <div class="mt-3">
                            <button type="submit" class="btn btn-primary">Update Powerbank</button>
                            <a href="{{ route('powerbanks.show', $powerbank->id) }}" class="btn btn-light ml-2">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
