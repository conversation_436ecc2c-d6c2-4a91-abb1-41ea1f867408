@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Assign Ad Plans to Group: {{ $devicegroup->name }}</h4>
                    <p class="sub-header">
                        Select the ad plans to assign to this device group. All devices in this group will display these
                        plans.
                    </p>

                    <form method="POST" action="{{ route('devicegroup.assign.plans', $devicegroup->id) }}">
                        @csrf

                        <div class="form-group">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th width="5%">Select</th>
                                            <th>Plan Name</th>
                                            <th>Time Range</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($adPlans as $plan)
                                            <tr>
                                                <td class="text-center">
                                                    <input type="checkbox" name="ad_plans[]" value="{{ $plan->id }}"
                                                        {{ in_array($plan->id, $assignedPlans) ? 'checked' : '' }}>
                                                </td>
                                                <td>{{ $plan->name }}</td>
                                                <td>{{ $plan->start_time }} - {{ $plan->end_time }}</td>
                                                <td>{{ $plan->priority }}</td>
                                                <td>
                                                    <span class="badge badge-{{ $plan->is_active ? 'success' : 'danger' }}">
                                                        {{ $plan->is_active ? 'Active' : 'Inactive' }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="form-group text-right">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-1"></i> Save Assignments
                            </button>
                            <a href="{{ route('devicegroups.show', $devicegroup->id) }}" class="btn btn-light ml-1">
                                <i class="fas fa-times mr-1"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
