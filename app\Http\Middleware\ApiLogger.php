<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\ApiLog;

class ApiLogger
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Log the request and response
        $logData = [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'request_data' => json_encode($request->all()),
            'response_data' => json_encode($response->getContent()),
            'status_code' => $response->getStatusCode(),
            'ip_address' => $request->ip(),
            'created_at' => now(),
        ];

        // Store log in the database
        ApiLog::create($logData);

        // Store log in a file
        Log::info('API Log', $logData);

        return $response;
    }
}
