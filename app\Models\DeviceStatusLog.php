<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeviceStatusLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'device_id',
        'status',
    ];

    /**
     * Get the device that owns the status log.
     */
    public function device()
    {
        return $this->belongsTo(Device::class);
    }
}