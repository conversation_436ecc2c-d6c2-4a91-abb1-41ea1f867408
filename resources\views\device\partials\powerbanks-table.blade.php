<!-- Only tab content panels, NO tab navigation -->
@php $firstAims = true; @endphp

@if($aimsGroups->isEmpty())
    <div class="tab-pane fade show active" id="aims-0" role="tabpanel" aria-labelledby="aims-0-tab">
        <div class="alert alert-info">
            No powerbanks found for this device.
        </div>
    </div>
@else
    @foreach ($aimsGroups as $aims => $powerbanks)
        <div class="tab-pane fade {{ $firstAims ? 'show active' : '' }}" id="aims-{{ $aims }}" role="tabpanel"
            aria-labelledby="aims-{{ $aims }}-tab">
            <div class="table-responsive">
                <table class="table table-centered table-striped table-bordered mb-0">
                    <thead>
                        <tr>
                            <th>Slot</th>
                            <th>Serial Number</th>
                            <th>Charge</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if($powerbanks->isEmpty())
                            <tr>
                                <td colspan="5" class="text-center">No powerbanks found in this AIMS group.</td>
                            </tr>
                        @else
                            @foreach ($powerbanks as $powerbank)
                                <tr>
                                    <td>{{ is_null($powerbank->slot_number) ? 'Not assigned' : ($powerbank->slot_number % 12 == 0 ? 12 : $powerbank->slot_number % 12) }}
                                    </td>
                                    <td>{{ $powerbank->serial_number }}</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-{{ $powerbank->current_charge > 70 ? 'success' : ($powerbank->current_charge > 30 ? 'warning' : 'danger') }}"
                                                role="progressbar" style="width: {{ $powerbank->current_charge }}%"
                                                aria-valuenow="{{ $powerbank->current_charge }}" aria-valuemin="0"
                                                aria-valuemax="100">
                                                {{ $powerbank->current_charge }}%</div>
                                        </div>
                                    </td>
                                    <td>
                                        <span
                                            class="badge badge-{{ $powerbank->status == 'available'
                                                ? 'success'
                                                : ($powerbank->status == 'rented'
                                                    ? 'warning'
                                                    : ($powerbank->status == 'charging'
                                                        ? 'info'
                                                        : 'danger')) }}">
                                            {{ ucfirst($powerbank->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('powerbanks.show', $powerbank->id) }}"
                                                class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-warning btn-sm eject-btn" data-toggle="modal"
                                                data-target="#ejectPowerbankModal" data-id="{{ $powerbank->id }}"
                                                data-sn="{{ $powerbank->serial_number }}" data-device-sn="{{ $device->sn }}"
                                                data-slot="{{ $powerbank->slot_number % 12 == 0 ? 12 : $powerbank->slot_number % 12 }}"
                                                data-aims="{{ $powerbank->aims }}">
                                                <i class="fas fa-eject"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
        @php $firstAims = false; @endphp
    @endforeach
@endif


