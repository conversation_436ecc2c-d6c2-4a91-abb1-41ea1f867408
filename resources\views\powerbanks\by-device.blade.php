@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title m-0">Powerbanks for Device: {{ $device->sn }}</h4>
                        <div>
                            <a href="{{ route('device.index') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-list mr-1"></i> Back to Devices
                            </a>
                            {{-- <a href="{{ route('powerbanks.create', ['device_id' => $device->id]) }}"
                                class="btn btn-primary btn-sm">
                                <i class="fas fa-plus mr-1"></i> Add Powerbank
                            </a> --}}
                        </div>
                    </div>

                    @if (count($powerbanks) > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered dt-responsive nowrap">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Serial Number</th>
                                        <th>Slot</th>
                                        <th>Capacity</th>
                                        <th>Current Charge</th>
                                        <th>Status</th>
                                        <th>Cycles</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($powerbanks as $powerbank)
                                        <tr>
                                            <td>{{ $powerbank->id }}</td>
                                            <td>{{ $powerbank->serial_number }}</td>
                                            <td>{{ $powerbank->slot_number ?? 'Not assigned' }}</td>
                                            <td>{{ $powerbank->capacity }} mAh</td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar
                                                    @if ($powerbank->current_charge < 20) bg-danger
                                                    @elseif($powerbank->current_charge < 50) bg-warning
                                                    @else bg-success @endif"
                                                        role="progressbar" style="width: {{ $powerbank->current_charge }}%;"
                                                        aria-valuenow="{{ $powerbank->current_charge }}" aria-valuemin="0"
                                                        aria-valuemax="100">{{ $powerbank->current_charge }}%</div>
                                                </div>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge badge-{{ $powerbank->status == 'available'
                                                        ? 'success'
                                                        : ($powerbank->status == 'rented'
                                                            ? 'warning'
                                                            : ($powerbank->status == 'charging'
                                                                ? 'info'
                                                                : 'danger')) }}">
                                                    {{ ucfirst($powerbank->status) }}
                                                </span>
                                            </td>
                                            <td>{{ $powerbank->charge_cycles }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ route('powerbanks.show', $powerbank->id) }}"
                                                        class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('powerbanks.edit', $powerbank->id) }}"
                                                        class="btn btn-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('powerbanks.destroy', $powerbank->id) }}"
                                                        method="POST" style="display: inline-block">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-sm"
                                                            onclick="return confirm('Are you sure you want to delete this powerbank?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-battery-empty text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">No Powerbanks Available</h5>
                            <p class="text-muted">There are no powerbanks assigned to this device yet.</p>
                            <a href="{{ route('powerbanks.create', ['device_id' => $device->id]) }}"
                                class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i> Add Powerbank
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
