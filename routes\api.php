<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdController;
use App\Http\Controllers\ApiLogController;
use App\Http\Controllers\DeviceController;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\TCPController;
use App\Http\Controllers\LogController;

use App\Models\ApiLog;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Route::any('/debug', function () {
//     Log::info('Route Called:', [
//         'url' => request()->fullUrl(),
//         'method' => request()->method(),
//         'params' => request()->all(),
//     ]);
//     return response()->json(['message' => 'Debugging route']);
// });

Route::get('/logs', [LogController::class, 'index']);
Route::get('/logs/{sn}', [LogController::class, 'showByDevice']);

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/screen/plan', [AdController::class, 'getAds']);
Route::post('/screen/rent', [AdController::class, 'rent']);
Route::post('/screen/audio', [AdController::class, 'getVoicePrompts']);

// Route::get('/logs', [ApiLogController::class, 'index']);
Route::get('/devices', [DeviceController::class, 'index']);
Route::get('/devices/{sn}', [DeviceController::class, 'show']);

Route::get('/logs', function () {
    return response()->json(ApiLog::latest()->paginate(10));
});

Route::post('/screen/login', [DeviceController::class, 'login']);
Route::post('/screen/capture', [DeviceController::class, 'captureJson']);


Route::post('/screen/rent', [TCPController::class, 'rent']);

Route::middleware('auth:sanctum')->group(function () {
    // Route::post('/cloud/screen/login', [DeviceController::class, 'login']);
    Route::post('/screen/heartbeat', [DeviceController::class, 'heartbeat']);
    Route::post('/screen/detail', [DeviceController::class, 'queryDetail']);
    Route::post('/screen/detailup', [DeviceController::class, 'updateDetail']);
    Route::post('/screen/rent1', [DeviceController::class, 'rent']);
    Route::post('/screen/return', [DeviceController::class, 'return']);
    Route::post('/screen/force', [DeviceController::class, 'forcePopOut']);
});

// Device Data API Routes
Route::post('/device-data', [App\Http\Controllers\DeviceDataController::class, 'store']);
Route::get('/device-data/device/{sn}', [App\Http\Controllers\DeviceDataController::class, 'apiGetByDeviceSn']);

// Device Group API Routes
Route::middleware('auth:sanctum')->group(function () {
    // List all device groups
    Route::get('/devicegroups', [App\Http\Controllers\Api\DeviceGroupController::class, 'index']);

    // Get a specific device group
    Route::get('/devicegroups/{devicegroup}', [App\Http\Controllers\Api\DeviceGroupController::class, 'show']);

    // Create a new device group
    Route::post('/devicegroups', [App\Http\Controllers\Api\DeviceGroupController::class, 'store']);

    // Update a device group
    Route::put('/devicegroups/{devicegroup}', [App\Http\Controllers\Api\DeviceGroupController::class, 'update']);

    // Delete a device group
    Route::delete('/devicegroups/{devicegroup}', [App\Http\Controllers\Api\DeviceGroupController::class, 'destroy']);

    // Assign devices to a group
    Route::post('/devicegroups/{devicegroup}/assign-devices', [App\Http\Controllers\Api\DeviceGroupController::class, 'assignDevices']);

    // Assign ad plans to a group (you already have this)
    Route::post('/devicegroups/{devicegroup}/assign-plans', [App\Http\Controllers\Api\DeviceGroupController::class, 'assignPlans']);
});
