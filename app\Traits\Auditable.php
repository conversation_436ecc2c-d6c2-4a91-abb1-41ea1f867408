<?php

namespace App\Traits;

use App\Models\AuditTrail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

trait Auditable
{
    /**
     * Boot the trait
     */
    protected static function bootAuditable()
    {
        static::created(function (Model $model) {
            self::createAuditTrail('created', $model);
        });

        static::updated(function (Model $model) {
            // Only log if actual changes were made
            if (count($model->getDirty()) > 0) {
                self::createAuditTrail('updated', $model);
            }
        });

        static::deleted(function (Model $model) {
            self::createAuditTrail('deleted', $model);
        });
    }

    /**
     * Create an audit trail record
     *
     * @param string $action
     * @param Model $model
     * @param string|null $module
     * @param string $severity
     * @param string|null $notes
     * @return void
     */
    protected static function createAuditTrail($action, $model, $module = null, $severity = 'low', $notes = null)
    {
        // Determine module if not provided
        if (!$module) {
            $module = self::determineModule($model);
        }

        // Determine severity based on action
        if ($severity === 'low' && in_array($action, ['deleted', 'restored'])) {
            $severity = 'medium';
        }

        // Create the audit trail record
        AuditTrail::create([
            'user_id' => Auth::id(),
            'auditable_type' => get_class($model),
            'auditable_id' => $model->getKey(),
            'action' => $action,
            'old_values' => $action === 'created' ? null : $model->getOriginal(),
            'new_values' => $action === 'deleted' ? null : $model->getAttributes(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'module' => $module,
            'severity' => $severity,
            'notes' => $notes,
        ]);
    }

    /**
     * Manually log an audit for viewing a record
     *
     * @param string|null $module
     * @param string $severity
     * @param string|null $notes
     * @return void
     */
    public function logView($module = null, $severity = 'low', $notes = null)
    {
        self::createAuditTrail('viewed', $this, $module, $severity, $notes);
    }

    /**
     * Determine the module based on the model class
     *
     * @param Model $model
     * @return string
     */
    protected static function determineModule($model)
    {
        $className = class_basename($model);

        // Map model names to modules
        $moduleMap = [
            'User' => 'users',
            'Role' => 'roles',
            'Permission' => 'permissions',
            'Customer' => 'customers',
            'Product' => 'products',
            'Order' => 'orders',
            'Invoice' => 'invoices',
            'Payment' => 'payments',
            // Add more mappings as needed
        ];

        return $moduleMap[$className] ?? strtolower($className);
    }
}
