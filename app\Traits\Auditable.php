<?php

namespace App\Traits;

use App\Models\AuditTrail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

trait Auditable
{
    /**
     * Boot the trait
     */
    protected static function bootAuditable()
    {
        static::created(function (Model $model) {
            self::createAuditTrail('created', $model);
        });

        static::updated(function (Model $model) {
            self::createAuditTrail('updated', $model);
        });

        static::deleted(function (Model $model) {
            self::createAuditTrail('deleted', $model);
        });
    }

    /**
     * Create an audit trail record
     *
     * @param string $action
     * @param Model $model
     * @return void
     */
    protected static function createAuditTrail($action, $model)
    {
        AuditTrail::create([
            'user_id' => Auth::id(),
            'auditable_type' => get_class($model),
            'auditable_id' => $model->getKey(),
            'action' => $action,
            'old_values' => $action === 'created' ? null : $model->getOriginal(),
            'new_values' => $action === 'deleted' ? null : $model->getAttributes(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }
}