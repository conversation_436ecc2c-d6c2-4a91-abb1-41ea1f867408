@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="header-title m-0">Device Group Details</h4>
                        <div>
                            <a href="{{ route('devicegroups.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-1"></i> Back to List
                            </a>
                            <a href="{{ route('devicegroups.edit', $devicegroup->id) }}" class="btn btn-primary">
                                <i class="fas fa-edit mr-1"></i> Edit Group
                            </a>
                        </div>
                    </div>

                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-body">
                                    <h5 class="card-title">Group Information</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered mb-0">
                                            <tbody>
                                                <tr>
                                                    <th width="30%">ID</th>
                                                    <td>{{ $devicegroup->id }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Name</th>
                                                    <td>{{ $devicegroup->name }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Description</th>
                                                    <td>{{ $devicegroup->description }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Status</th>
                                                    <td>
                                                        <span
                                                            class="badge badge-{{ $devicegroup->is_active ? 'success' : 'danger' }}">
                                                            {{ $devicegroup->is_active ? 'Active' : 'Inactive' }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Created At</th>
                                                    <td>{{ $devicegroup->created_at->format('Y-m-d H:i:s') }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Updated At</th>
                                                    <td>{{ $devicegroup->updated_at->format('Y-m-d H:i:s') }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title m-0">Actions</h5>
                                    </div>
                                    <div class="list-group">
                                        <a href="{{ route('devicegroup.assign.devices.show', $devicegroup->id) }}"
                                            class="list-group-item list-group-item-action">
                                            <i class="fas fa-mobile-alt mr-2"></i> Assign Devices
                                        </a>
                                        <a href="{{ route('devicegroup.assign.plans.show', $devicegroup->id) }}"
                                            class="list-group-item list-group-item-action">
                                            <i class="fas fa-tasks mr-2"></i> Assign Ad Plans
                                        </a>
                                        <button type="button" class="list-group-item list-group-item-action text-danger"
                                            onclick="document.getElementById('delete-form').submit()">
                                            <i class="fas fa-trash mr-2"></i> Delete Group
                                        </button>
                                        <form id="delete-form"
                                            action="{{ route('devicegroups.destroy', $devicegroup->id) }}" method="POST"
                                            class="d-none">
                                            @csrf
                                            @method('DELETE')
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title m-0">Assigned Devices ({{ $devicegroup->devices->count() }})
                                        </h5>
                                        <a href="{{ route('devicegroup.assign.devices.show', $devicegroup->id) }}"
                                            class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit mr-1"></i> Manage
                                        </a>
                                    </div>

                                    @if ($devicegroup->devices->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-sm table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>SN</th>
                                                        <th>Model</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach ($devicegroup->devices as $device)
                                                        <tr>
                                                            <td>{{ $device->sn }}</td>
                                                            <td>{{ $device->model }}</td>
                                                            <td>{{ $device->status }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <div class="text-center py-3">
                                            <p class="text-muted mb-0">No devices assigned to this group.</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title m-0">Assigned Ad Plans ({{ $devicegroup->adPlans->count() }})
                                        </h5>
                                        <a href="{{ route('devicegroup.assign.plans.show', $devicegroup->id) }}"
                                            class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit mr-1"></i> Manage
                                        </a>
                                    </div>

                                    @if ($devicegroup->adPlans->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-sm table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach ($devicegroup->adPlans as $plan)
                                                        <tr>
                                                            <td>{{ $plan->name }}</td>
                                                            <td>
                                                                <span
                                                                    class="badge badge-{{ $plan->is_active ? 'success' : 'danger' }}">
                                                                    {{ $plan->is_active ? 'Active' : 'Inactive' }}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <div class="text-center py-3">
                                            <p class="text-muted mb-0">No ad plans assigned to this group.</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
