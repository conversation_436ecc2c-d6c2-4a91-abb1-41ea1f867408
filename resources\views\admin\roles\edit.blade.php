@extends('layouts.master')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">Edit Role: {{ $role->name }}</h4>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('role.update', $role) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <div class="mb-3">
                                <label for="name" class="form-label">Role Name</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                    id="name" name="name" value="{{ old('name', $role->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description"
                                    rows="3">{{ old('description', $role->description) }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Permissions</label>
                                <p class="text-muted small mb-3">Select the permissions you want to assign to this role.
                                    Permissions are organized by category for easier management.</p>

                                <!-- Select All Controls -->
                                <div class="mb-3 p-3 bg-light rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-bold">Quick Actions:</span>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-primary mr-2"
                                                onclick="selectAllPermissions()">
                                                <i class="fe-check-square"></i> Select All
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                                onclick="deselectAllPermissions()">
                                                <i class="fe-square"></i> Deselect All
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Permission Groups -->
                                <div class="card-group" id="permissionAccordion">
                                    @foreach ($groupedPermissions as $groupName => $group)
                                        <div class="card mb-2">
                                            <div class="card-header" id="heading{{ Str::slug($groupName) }}">
                                                <h5 class="mb-0">
                                                    <button
                                                        class="btn btn-link w-100 text-left {{ $loop->first ? '' : 'collapsed' }}"
                                                        type="button" data-toggle="collapse"
                                                        data-target="#collapse{{ Str::slug($groupName) }}"
                                                        aria-expanded="{{ $loop->first ? 'true' : 'false' }}"
                                                        aria-controls="collapse{{ Str::slug($groupName) }}">
                                                        <div class="d-flex align-items-center">
                                                            <i class="{{ $group['icon'] }} mr-2 text-primary"></i>
                                                            <div class="flex-grow-1">
                                                                <strong>{{ $groupName }}</strong>
                                                                <small
                                                                    class="d-block text-muted">{{ $group['description'] }}</small>
                                                            </div>
                                                            <span
                                                                class="badge badge-secondary ml-auto mr-3">{{ count($group['permissions']) }}
                                                                permissions</span>
                                                        </div>
                                                    </button>
                                                </h5>
                                            </div>
                                            <div id="collapse{{ Str::slug($groupName) }}"
                                                class="collapse {{ $loop->first ? 'show' : '' }}"
                                                aria-labelledby="heading{{ Str::slug($groupName) }}"
                                                data-parent="#permissionAccordion">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                                        <span class="text-muted">{{ $group['description'] }}</span>
                                                        <div>
                                                            <button type="button"
                                                                class="btn btn-sm btn-outline-success me-1"
                                                                onclick="selectGroupPermissions('{{ Str::slug($groupName) }}')">
                                                                <i class="fe-check"></i> Select All
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                                onclick="deselectGroupPermissions('{{ Str::slug($groupName) }}')">
                                                                <i class="fe-x"></i> Deselect All
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        @foreach ($group['permissions'] as $permission)
                                                            <div class="col-md-6 col-lg-4 mb-3">
                                                                <div
                                                                    class="form-check p-3 border rounded {{ in_array($permission->id, $rolePermissions) ? 'border-primary bg-primary bg-opacity-10' : 'border-light' }}">
                                                                    <input
                                                                        class="form-check-input permission-checkbox group-{{ Str::slug($groupName) }}"
                                                                        type="checkbox" name="permissions[]"
                                                                        value="{{ $permission->id }}"
                                                                        id="permission_{{ $permission->id }}"
                                                                        {{ in_array($permission->id, $rolePermissions) ? 'checked' : '' }}>
                                                                    <label class="form-check-label w-100"
                                                                        for="permission_{{ $permission->id }}">
                                                                        <div class="fw-bold">{{ $permission->name }}</div>
                                                                        <small
                                                                            class="text-muted">{{ $permission->description }}</small>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ route('role.index') }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Update Role</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        // Select all permissions
        function selectAllPermissions() {
            document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
                checkbox.checked = true;
                updateCheckboxStyle(checkbox);
            });
        }

        // Deselect all permissions
        function deselectAllPermissions() {
            document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
                checkbox.checked = false;
                updateCheckboxStyle(checkbox);
            });
        }

        // Select all permissions in a specific group
        function selectGroupPermissions(groupSlug) {
            document.querySelectorAll('.group-' + groupSlug).forEach(function(checkbox) {
                checkbox.checked = true;
                updateCheckboxStyle(checkbox);
            });
        }

        // Deselect all permissions in a specific group
        function deselectGroupPermissions(groupSlug) {
            document.querySelectorAll('.group-' + groupSlug).forEach(function(checkbox) {
                checkbox.checked = false;
                updateCheckboxStyle(checkbox);
            });
        }

        // Update checkbox styling based on checked state
        function updateCheckboxStyle(checkbox) {
            const container = checkbox.closest('.form-check');
            if (checkbox.checked) {
                container.classList.remove('border-light');
                container.classList.add('border-primary', 'bg-primary', 'bg-opacity-10');
            } else {
                container.classList.remove('border-primary', 'bg-primary', 'bg-opacity-10');
                container.classList.add('border-light');
            }
        }

        // Add event listeners to all checkboxes for styling updates
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    updateCheckboxStyle(this);
                });
            });

            // Update selected count
            updateSelectedCount();
        });

        // Update selected permissions count
        function updateSelectedCount() {
            const totalPermissions = document.querySelectorAll('.permission-checkbox').length;
            const selectedPermissions = document.querySelectorAll('.permission-checkbox:checked').length;

            // You can add a counter display here if needed
            console.log(`Selected ${selectedPermissions} of ${totalPermissions} permissions`);
        }

        // Add change event to update count
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('permission-checkbox')) {
                updateSelectedCount();
            }
        });
    </script>
@endsection
