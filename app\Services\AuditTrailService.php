<?php

namespace App\Services;

use App\Models\AuditTrail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class AuditTrailService
{
    /**
     * Log a model creation event.
     *
     * @param Model $model The created model
     * @param string|null $module The module name
     * @param string $severity The severity level
     * @param string|null $notes Additional notes
     * @return AuditTrail
     */
    public static function logCreated(Model $model, ?string $module = null, string $severity = 'low', ?string $notes = null)
    {
        // Get all model attributes as new values
        $newValues = $model->getAttributes();

        // Remove timestamps and any sensitive data
        unset($newValues['created_at'], $newValues['updated_at'], $newValues['password']);

        return AuditTrail::logModel(
            'created',
            $model,
            null,
            $newValues,
            $module ?? self::guessModule($model),
            $severity,
            $notes ?? "Created new " . class_basename($model) . " record"
        );
    }

    /**
     * Log a model update event.
     *
     * @param Model $model The updated model
     * @param array $oldValues The old values
     * @param string|null $module The module name
     * @param string $severity The severity level
     * @param string|null $notes Additional notes
     * @return AuditTrail
     */
    public static function logUpdated(Model $model, array $oldValues, ?string $module = null, string $severity = 'low', ?string $notes = null)
    {
        // Get current model attributes as new values
        $newValues = $model->getAttributes();

        // Remove timestamps and any sensitive data
        unset($oldValues['created_at'], $oldValues['updated_at'], $oldValues['password']);
        unset($newValues['created_at'], $newValues['updated_at'], $newValues['password']);

        return AuditTrail::logModel(
            'updated',
            $model,
            $oldValues,
            $newValues,
            $module ?? self::guessModule($model),
            $severity,
            $notes ?? "Updated " . class_basename($model) . " record"
        );
    }

    /**
     * Log a model deletion event.
     *
     * @param Model $model The deleted model
     * @param string|null $module The module name
     * @param string $severity The severity level
     * @param string|null $notes Additional notes
     * @return AuditTrail
     */
    public static function logDeleted(Model $model, ?string $module = null, string $severity = 'medium', ?string $notes = null)
    {
        // Get all model attributes as old values
        $oldValues = $model->getAttributes();

        // Remove timestamps and any sensitive data
        unset($oldValues['created_at'], $oldValues['updated_at'], $oldValues['password']);

        return AuditTrail::logModel(
            'deleted',
            $model,
            $oldValues,
            null,
            $module ?? self::guessModule($model),
            $severity,
            $notes ?? "Deleted " . class_basename($model) . " record"
        );
    }

    /**
     * Log a model view event.
     *
     * @param Model $model The viewed model
     * @param string|null $module The module name
     * @param string $severity The severity level
     * @param string|null $notes Additional notes
     * @return AuditTrail
     */
    public static function logViewed(Model $model, ?string $module = null, string $severity = 'low', ?string $notes = null)
    {
        return AuditTrail::logModel(
            'viewed',
            $model,
            null,
            null,
            $module ?? self::guessModule($model),
            $severity,
            $notes ?? "Viewed " . class_basename($model) . " record"
        );
    }

    /**
     * Log a user login event.
     *
     * @param Model $user The user model
     * @param bool $success Whether login was successful
     * @param string|null $notes Additional notes
     * @return AuditTrail
     */
    public static function logLogin(Model $user, bool $success = true, ?string $notes = null)
    {
        $action = $success ? 'logged_in' : 'failed_login';
        $severity = $success ? 'low' : 'medium';

        return AuditTrail::logModel(
            $action,
            $user,
            null,
            null,
            'authentication',
            $severity,
            $notes ?? ($success ? "User logged in successfully" : "Failed login attempt")
        );
    }

    /**
     * Log a user logout event.
     *
     * @param Model $user The user model
     * @param string|null $notes Additional notes
     * @return AuditTrail
     */
    public static function logLogout(Model $user, ?string $notes = null)
    {
        return AuditTrail::logModel(
            'logged_out',
            $user,
            null,
            null,
            'authentication',
            'low',
            $notes ?? "User logged out"
        );
    }

    /**
     * Log a custom event.
     *
     * @param string $action The action name
     * @param string $module The module name
     * @param string $severity The severity level
     * @param string $notes Additional notes
     * @param array|null $oldValues Old values if applicable
     * @param array|null $newValues New values if applicable
     * @return AuditTrail
     */
    public static function logCustom(
        string $action,
        string $module,
        string $severity,
        string $notes,
        ?array $oldValues = null,
        ?array $newValues = null
    ) {
        $request = request();

        return AuditTrail::create([
            'user_id' => Auth::id(),
            'auditable_type' => 'Custom',
            'auditable_id' => 0,
            'action' => $action,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'module' => $module,
            'severity' => $severity,
            'notes' => $notes,
        ]);
    }

    /**
     * Log a database backup event.
     *
     * @param string $filename The backup filename
     * @param string $action The action (created, restored, deleted)
     * @param string $severity The severity level
     * @param string|null $notes Additional notes
     * @return AuditTrail
     */
    public static function logDatabaseBackup(
        string $filename,
        string $action = 'created',
        string $severity = 'medium',
        ?string $notes = null
    ) {
        $request = request();

        return AuditTrail::create([
            'user_id' => Auth::id(),
            'auditable_type' => 'DatabaseBackup',
            'auditable_id' => 0,
            'action' => $action,
            'old_values' => null,
            'new_values' => ['filename' => $filename],
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'module' => 'database_backup',
            'severity' => $severity,
            'notes' => $notes ?? "Database backup {$action}: {$filename}",
        ]);
    }

    /**
     * Log a system configuration change.
     *
     * @param array $oldValues The old configuration values
     * @param array $newValues The new configuration values
     * @param string $severity The severity level
     * @param string|null $notes Additional notes
     * @return AuditTrail
     */
    public static function logConfigChange(
        array $oldValues,
        array $newValues,
        string $severity = 'high',
        ?string $notes = null
    ) {
        return self::logCustom(
            'config_changed',
            'system_configuration',
            $severity,
            $notes ?? 'System configuration changed',
            $oldValues,
            $newValues
        );
    }

    /**
     * Log a security event.
     *
     * @param string $action The security action
     * @param string $severity The severity level
     * @param string $notes Details about the security event
     * @param array|null $data Additional data
     * @return AuditTrail
     */
    public static function logSecurityEvent(
        string $action,
        string $severity,
        string $notes,
        ?array $data = null
    ) {
        return self::logCustom(
            $action,
            'security',
            $severity,
            $notes,
            null,
            $data
        );
    }

    /**
     * Guess the module name from the model class.
     *
     * @param Model $model The model instance
     * @return string The guessed module name
     */
    protected static function guessModule(Model $model)
    {
        $className = class_basename($model);

        // Convert camel case to snake case
        $module = strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $className));

        return $module;
    }

    /**
     * Clear old audit trail records.
     *
     * @param int $daysToKeep Number of days of records to keep
     * @return int Number of records deleted
     */
    public static function clearOldRecords(int $daysToKeep = 90)
    {
        $cutoffDate = now()->subDays($daysToKeep);

        // Log the clearing action before deleting
        self::logCustom(
            'cleared',
            'audit_trail',
            'medium',
            "Cleared audit trail records older than {$daysToKeep} days",
            ['cutoff_date' => $cutoffDate->toDateTimeString()],
            null
        );

        // Delete old records
        return AuditTrail::where('created_at', '<', $cutoffDate)->delete();
    }
}
